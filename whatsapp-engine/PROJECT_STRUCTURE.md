# WhatsApp 引擎项目结构

```
whatsapp-engine/
├── main.go                          # 主程序入口
├── go.mod                           # Go 模块定义
├── go.sum                           # Go 依赖锁定文件
├── Dockerfile                       # Docker 构建文件
├── docker-compose.yml               # Docker Compose 配置
├── Makefile                         # 构建工具
├── start.sh                         # 启动脚本
├── README.md                        # 项目说明文档
├── PROJECT_STRUCTURE.md             # 项目结构说明
├── .gitignore                       # Git 忽略文件
│
├── config/                          # 配置文件目录
│   └── config.yaml                  # 主配置文件
│
├── internal/                        # 内部包目录
│   ├── api/                         # API 接口层
│   │   └── routes.go                # 路由定义和处理器
│   │
│   ├── config/                      # 配置管理
│   │   └── config.go                # 配置结构和加载
│   │
│   ├── engine/                      # 主引擎
│   │   └── engine.go                # WhatsApp 引擎核心
│   │
│   ├── proxy/                       # 代理管理模块
│   │   ├── manager.go               # 代理管理器
│   │   ├── allocator.go             # 代理分配器
│   │   ├── health_checker.go        # 健康检查器
│   │   └── failover.go              # 故障转移管理器
│   │
│   ├── session/                     # 会话管理模块
│   │   └── manager.go               # 会话管理器
│   │
│   ├── message/                     # 消息管理模块
│   │   └── manager.go               # 消息管理器
│   │
│   ├── storage/                     # 存储层
│   │   ├── database.go              # 数据库操作
│   │   └── cache.go                 # 内存缓存
│   │
│   └── types/                       # 类型定义
│       └── types.go                 # 所有数据结构定义
│
├── test/                            # 测试文件目录
│   └── test_api.sh                  # API 测试脚本
│
├── data/                            # 数据目录 (运行时创建)
│   └── whatsapp.db                  # SQLite 数据库文件
│
├── sessions/                        # 会话存储目录 (运行时创建)
│   └── [session-files]              # WhatsApp 会话文件
│
└── logs/                            # 日志目录 (运行时创建)
    └── whatsapp-engine.log          # 应用日志文件
```

## 📁 目录说明

### `/config`
配置文件目录，包含应用的各种配置：
- `config.yaml`: 主配置文件，包含服务器、数据库、代理等所有配置

### `/internal`
内部包目录，包含所有业务逻辑代码：

#### `/internal/api`
API 接口层，处理 HTTP 请求和响应：
- 兼容原 Node.js 服务的接口
- 新增的代理管理、会话管理接口
- 监控和健康检查接口

#### `/internal/config`
配置管理模块：
- 配置结构定义
- 配置文件加载和验证
- 默认配置设置

#### `/internal/engine`
主引擎模块，协调各个子模块：
- 引擎生命周期管理
- 组件初始化和协调
- 统计信息收集

#### `/internal/proxy`
代理管理模块，核心特性之一：
- **manager.go**: 代理管理器，统一管理所有代理
- **allocator.go**: 代理分配器，智能分配代理给会话
- **health_checker.go**: 健康检查器，监控代理可用性
- **failover.go**: 故障转移管理器，处理代理失效

#### `/internal/session`
会话管理模块：
- WhatsApp 会话生命周期管理
- 会话状态跟踪
- 自动恢复和清理

#### `/internal/message`
消息管理模块：
- 消息发送和接收
- 消息队列和重试机制
- 速率限制

#### `/internal/storage`
存储层：
- **database.go**: 数据库操作，使用 GORM
- **cache.go**: 内存缓存，替代 Redis

#### `/internal/types`
类型定义：
- 所有数据结构定义
- 接口定义
- 常量定义

### `/test`
测试文件目录：
- API 测试脚本
- 单元测试文件

### 运行时目录

#### `/data`
数据存储目录：
- SQLite 数据库文件
- 其他持久化数据

#### `/sessions`
WhatsApp 会话存储目录：
- whatsmeow 会话文件
- 设备信息

#### `/logs`
日志文件目录：
- 应用运行日志
- 错误日志

## 🔧 核心组件关系

```
┌─────────────────┐
│   main.go       │ ──┐
└─────────────────┘   │
                      ▼
┌─────────────────┐   ┌─────────────────┐   ┌─────────────────┐
│   API Layer     │◄──│   Engine        │──►│  Config         │
└─────────────────┘   └─────────────────┘   └─────────────────┘
                              │
                              ▼
        ┌─────────────────────┼─────────────────────┐
        │                     │                     │
        ▼                     ▼                     ▼
┌─────────────────┐   ┌─────────────────┐   ┌─────────────────┐
│ Proxy Manager   │   │Session Manager  │   │Message Manager  │
└─────────────────┘   └─────────────────┘   └─────────────────┘
        │                     │                     │
        └─────────────────────┼─────────────────────┘
                              ▼
                    ┌─────────────────┐
                    │  Storage Layer  │
                    └─────────────────┘
```

## 🚀 启动流程

1. **main.go** 加载配置文件
2. **Engine** 初始化各个管理器
3. **Storage** 初始化数据库和缓存
4. **Proxy Manager** 启动代理健康检查
5. **Session Manager** 恢复现有会话
6. **Message Manager** 启动消息队列
7. **API Layer** 启动 HTTP 服务器

## 📊 数据流

1. **API 请求** → API Layer → Engine → 相应的 Manager
2. **代理分配** → Proxy Manager → Allocator → Health Checker
3. **消息发送** → Message Manager → Session Manager → WhatsApp
4. **数据持久化** → Storage Layer → Database/Cache

这个架构设计确保了：
- **高性能**: 各组件异步工作，互不阻塞
- **高可用**: 代理故障转移，会话自动恢复
- **易扩展**: 模块化设计，便于添加新功能
- **易维护**: 清晰的分层架构，职责分明
