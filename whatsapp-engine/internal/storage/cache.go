package storage

import (
	"encoding/json"
	"sync"
	"time"

	"whatsapp-engine/internal/config"
)

// CacheItem 缓存项
type CacheItem struct {
	Value     interface{}
	ExpiresAt time.Time
}

// IsExpired 检查是否过期
func (item *CacheItem) IsExpired() bool {
	return time.Now().After(item.ExpiresAt)
}

// MemoryCache 内存缓存实现
type MemoryCache struct {
	items   map[string]*CacheItem
	mutex   sync.RWMutex
	maxSize int
	ttl     time.Duration
}

// NewMemoryCache 创建内存缓存
func NewMemoryCache(maxSize int, defaultTTL time.Duration) *MemoryCache {
	cache := &MemoryCache{
		items:   make(map[string]*CacheItem),
		maxSize: maxSize,
		ttl:     defaultTTL,
	}
	
	// 启动清理协程
	go cache.cleanup()
	
	return cache
}

// Set 设置缓存
func (c *MemoryCache) Set(key string, value interface{}, ttl time.Duration) error {
	c.mutex.Lock()
	defer c.mutex.Unlock()
	
	// 如果缓存已满，删除最旧的项
	if len(c.items) >= c.maxSize {
		c.evictOldest()
	}
	
	if ttl == 0 {
		ttl = c.ttl
	}
	
	c.items[key] = &CacheItem{
		Value:     value,
		ExpiresAt: time.Now().Add(ttl),
	}
	
	return nil
}

// Get 获取缓存
func (c *MemoryCache) Get(key string) (interface{}, bool) {
	c.mutex.RLock()
	defer c.mutex.RUnlock()
	
	item, exists := c.items[key]
	if !exists {
		return nil, false
	}
	
	if item.IsExpired() {
		delete(c.items, key)
		return nil, false
	}
	
	return item.Value, true
}

// Delete 删除缓存
func (c *MemoryCache) Delete(key string) error {
	c.mutex.Lock()
	defer c.mutex.Unlock()
	
	delete(c.items, key)
	return nil
}

// Exists 检查键是否存在
func (c *MemoryCache) Exists(key string) bool {
	_, exists := c.Get(key)
	return exists
}

// Clear 清空缓存
func (c *MemoryCache) Clear() error {
	c.mutex.Lock()
	defer c.mutex.Unlock()
	
	c.items = make(map[string]*CacheItem)
	return nil
}

// Size 获取缓存大小
func (c *MemoryCache) Size() int {
	c.mutex.RLock()
	defer c.mutex.RUnlock()
	
	return len(c.items)
}

// Keys 获取所有键
func (c *MemoryCache) Keys() []string {
	c.mutex.RLock()
	defer c.mutex.RUnlock()
	
	keys := make([]string, 0, len(c.items))
	for key, item := range c.items {
		if !item.IsExpired() {
			keys = append(keys, key)
		}
	}
	
	return keys
}

// SetJSON 设置 JSON 数据
func (c *MemoryCache) SetJSON(key string, value interface{}, ttl time.Duration) error {
	data, err := json.Marshal(value)
	if err != nil {
		return err
	}
	
	return c.Set(key, data, ttl)
}

// GetJSON 获取 JSON 数据
func (c *MemoryCache) GetJSON(key string, dest interface{}) error {
	data, exists := c.Get(key)
	if !exists {
		return ErrCacheKeyNotFound
	}
	
	bytes, ok := data.([]byte)
	if !ok {
		return ErrCacheInvalidType
	}
	
	return json.Unmarshal(bytes, dest)
}

// Increment 递增计数器
func (c *MemoryCache) Increment(key string, delta int64) (int64, error) {
	c.mutex.Lock()
	defer c.mutex.Unlock()
	
	item, exists := c.items[key]
	if !exists || item.IsExpired() {
		// 如果键不存在或已过期，创建新的计数器
		c.items[key] = &CacheItem{
			Value:     delta,
			ExpiresAt: time.Now().Add(c.ttl),
		}
		return delta, nil
	}
	
	// 尝试转换为 int64
	var currentValue int64
	switch v := item.Value.(type) {
	case int64:
		currentValue = v
	case int:
		currentValue = int64(v)
	case float64:
		currentValue = int64(v)
	default:
		return 0, ErrCacheInvalidType
	}
	
	newValue := currentValue + delta
	item.Value = newValue
	
	return newValue, nil
}

// SetExpire 设置过期时间
func (c *MemoryCache) SetExpire(key string, ttl time.Duration) error {
	c.mutex.Lock()
	defer c.mutex.Unlock()
	
	item, exists := c.items[key]
	if !exists {
		return ErrCacheKeyNotFound
	}
	
	item.ExpiresAt = time.Now().Add(ttl)
	return nil
}

// GetTTL 获取剩余过期时间
func (c *MemoryCache) GetTTL(key string) (time.Duration, error) {
	c.mutex.RLock()
	defer c.mutex.RUnlock()
	
	item, exists := c.items[key]
	if !exists {
		return 0, ErrCacheKeyNotFound
	}
	
	if item.IsExpired() {
		return 0, ErrCacheKeyExpired
	}
	
	return time.Until(item.ExpiresAt), nil
}

// evictOldest 删除最旧的项（LRU策略的简化版本）
func (c *MemoryCache) evictOldest() {
	var oldestKey string
	var oldestTime time.Time
	
	for key, item := range c.items {
		if oldestKey == "" || item.ExpiresAt.Before(oldestTime) {
			oldestKey = key
			oldestTime = item.ExpiresAt
		}
	}
	
	if oldestKey != "" {
		delete(c.items, oldestKey)
	}
}

// cleanup 定期清理过期项
func (c *MemoryCache) cleanup() {
	ticker := time.NewTicker(time.Minute)
	defer ticker.Stop()
	
	for range ticker.C {
		c.mutex.Lock()
		for key, item := range c.items {
			if item.IsExpired() {
				delete(c.items, key)
			}
		}
		c.mutex.Unlock()
	}
}

// 缓存错误定义
var (
	ErrCacheKeyNotFound = &CacheError{Code: "KEY_NOT_FOUND", Message: "缓存键不存在"}
	ErrCacheKeyExpired  = &CacheError{Code: "KEY_EXPIRED", Message: "缓存键已过期"}
	ErrCacheInvalidType = &CacheError{Code: "INVALID_TYPE", Message: "缓存值类型无效"}
)

// CacheError 缓存错误
type CacheError struct {
	Code    string
	Message string
}

func (e *CacheError) Error() string {
	return e.Message
}

// InitCache 初始化缓存（内嵌内存缓存）
func InitCache(cfg config.CacheConfig) (*MemoryCache, error) {
	maxSize := cfg.MaxSize
	if maxSize <= 0 {
		maxSize = 10000
	}

	defaultTTL := cfg.DefaultTTL
	if defaultTTL <= 0 {
		defaultTTL = time.Hour
	}

	cache := NewMemoryCache(maxSize, defaultTTL)

	return cache, nil
}

// CacheInterface 缓存接口
type CacheInterface interface {
	Set(key string, value interface{}, ttl time.Duration) error
	Get(key string) (interface{}, bool)
	Delete(key string) error
	Exists(key string) bool
	Clear() error
	Size() int
	Keys() []string
	SetJSON(key string, value interface{}, ttl time.Duration) error
	GetJSON(key string, dest interface{}) error
	Increment(key string, delta int64) (int64, error)
	SetExpire(key string, ttl time.Duration) error
	GetTTL(key string) (time.Duration, error)
}

// 确保 MemoryCache 实现了 CacheInterface
var _ CacheInterface = (*MemoryCache)(nil)
