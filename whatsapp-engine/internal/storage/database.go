package storage

import (
	"fmt"
	"os"
	"path/filepath"
	"time"

	"whatsapp-engine/internal/config"
	"whatsapp-engine/internal/types"

	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

// InitDatabase 初始化数据库
func InitDatabase(cfg config.DatabaseConfig) (*gorm.DB, error) {
	var db *gorm.DB
	var err error

	// 设置 GORM 配置
	gormConfig := &gorm.Config{
		Logger: logger.Default.LogMode(logger.Silent),
	}

	switch cfg.Type {
	case "sqlite":
		// 确保数据目录存在
		dbPath := cfg.URL
		if dbPath == "" {
			dbPath = "./data/whatsapp.db"
		}
		
		dir := filepath.Dir(dbPath)
		if err := os.MkdirAll(dir, 0755); err != nil {
			return nil, fmt.Errorf("创建数据目录失败: %v", err)
		}

		db, err = gorm.Open(sqlite.Open(dbPath), gormConfig)
		if err != nil {
			return nil, fmt.Errorf("连接 SQLite 数据库失败: %v", err)
		}

	default:
		return nil, fmt.Errorf("不支持的数据库类型: %s", cfg.Type)
	}

	// 配置连接池
	sqlDB, err := db.DB()
	if err != nil {
		return nil, fmt.Errorf("获取数据库连接失败: %v", err)
	}

	sqlDB.SetMaxOpenConns(cfg.MaxOpenConns)
	sqlDB.SetMaxIdleConns(cfg.MaxIdleConns)
	sqlDB.SetConnMaxLifetime(cfg.ConnMaxLifetime)

	// 自动迁移
	if err := autoMigrate(db); err != nil {
		return nil, fmt.Errorf("数据库迁移失败: %v", err)
	}

	return db, nil
}

// autoMigrate 自动迁移数据库表
func autoMigrate(db *gorm.DB) error {
	return db.AutoMigrate(
		&types.WhatsAppSession{},
		&types.ProxyInfo{},
		&types.ProxyGroup{},
		&types.MessageRecord{},
	)
}

// SessionStorage 会话存储接口
type SessionStorage interface {
	CreateSession(session *types.WhatsAppSession) error
	GetSession(sessionID string) (*types.WhatsAppSession, error)
	UpdateSession(session *types.WhatsAppSession) error
	DeleteSession(sessionID string) error
	ListSessions(tenantID uint) ([]*types.WhatsAppSession, error)
	GetSessionsByStatus(status types.SessionStatus) ([]*types.WhatsAppSession, error)
}

// ProxyStorage 代理存储接口
type ProxyStorage interface {
	CreateProxy(proxy *types.ProxyInfo) error
	GetProxy(proxyID string) (*types.ProxyInfo, error)
	UpdateProxy(proxy *types.ProxyInfo) error
	DeleteProxy(proxyID string) error
	ListProxies(filter *ProxyFilter) ([]*types.ProxyInfo, error)
	CreateGroup(group *types.ProxyGroup) error
	GetGroup(groupID string) (*types.ProxyGroup, error)
	UpdateGroup(group *types.ProxyGroup) error
	DeleteGroup(groupID string) error
	ListGroups() ([]*types.ProxyGroup, error)
}

// MessageStorage 消息存储接口
type MessageStorage interface {
	SaveMessage(message *types.MessageRecord) error
	GetMessage(messageID string) (*types.MessageRecord, error)
	UpdateMessage(message *types.MessageRecord) error
	QueryMessages(sessionID string, filter *MessageFilter) ([]*types.MessageRecord, error)
	DeleteOldMessages(beforeDate time.Time) error
}

// ProxyFilter 代理过滤器
type ProxyFilter struct {
	GroupID string
	Region  string
	Quality types.ProxyQuality
	Status  types.ProxyStatus
}

// MessageFilter 消息过滤器
type MessageFilter struct {
	ChatJID      string
	MessageType  types.MessageType
	StartTime    *time.Time
	EndTime      *time.Time
	Limit        int
	Offset       int
}

// DatabaseStorage 数据库存储实现
type DatabaseStorage struct {
	db *gorm.DB
}

// NewDatabaseStorage 创建数据库存储
func NewDatabaseStorage(db *gorm.DB) *DatabaseStorage {
	return &DatabaseStorage{db: db}
}

// 实现 SessionStorage 接口
func (s *DatabaseStorage) CreateSession(session *types.WhatsAppSession) error {
	return s.db.Create(session).Error
}

func (s *DatabaseStorage) GetSession(sessionID string) (*types.WhatsAppSession, error) {
	var session types.WhatsAppSession
	err := s.db.Where("id = ?", sessionID).First(&session).Error
	if err != nil {
		return nil, err
	}
	return &session, nil
}

func (s *DatabaseStorage) UpdateSession(session *types.WhatsAppSession) error {
	return s.db.Save(session).Error
}

func (s *DatabaseStorage) DeleteSession(sessionID string) error {
	return s.db.Where("id = ?", sessionID).Delete(&types.WhatsAppSession{}).Error
}

func (s *DatabaseStorage) ListSessions(tenantID uint) ([]*types.WhatsAppSession, error) {
	var sessions []*types.WhatsAppSession
	err := s.db.Where("tenant_id = ?", tenantID).Find(&sessions).Error
	return sessions, err
}

func (s *DatabaseStorage) GetSessionsByStatus(status types.SessionStatus) ([]*types.WhatsAppSession, error) {
	var sessions []*types.WhatsAppSession
	err := s.db.Where("status = ?", status).Find(&sessions).Error
	return sessions, err
}

// 实现 ProxyStorage 接口
func (s *DatabaseStorage) CreateProxy(proxy *types.ProxyInfo) error {
	return s.db.Create(proxy).Error
}

func (s *DatabaseStorage) GetProxy(proxyID string) (*types.ProxyInfo, error) {
	var proxy types.ProxyInfo
	err := s.db.Where("id = ?", proxyID).First(&proxy).Error
	if err != nil {
		return nil, err
	}
	return &proxy, nil
}

func (s *DatabaseStorage) UpdateProxy(proxy *types.ProxyInfo) error {
	return s.db.Save(proxy).Error
}

func (s *DatabaseStorage) DeleteProxy(proxyID string) error {
	return s.db.Where("id = ?", proxyID).Delete(&types.ProxyInfo{}).Error
}

func (s *DatabaseStorage) ListProxies(filter *ProxyFilter) ([]*types.ProxyInfo, error) {
	query := s.db.Model(&types.ProxyInfo{})
	
	if filter != nil {
		if filter.GroupID != "" {
			query = query.Where("group_id = ?", filter.GroupID)
		}
		if filter.Region != "" {
			query = query.Where("region = ?", filter.Region)
		}
		if filter.Quality != "" {
			query = query.Where("quality = ?", filter.Quality)
		}
		if filter.Status != "" {
			query = query.Where("status = ?", filter.Status)
		}
	}
	
	var proxies []*types.ProxyInfo
	err := query.Find(&proxies).Error
	return proxies, err
}

func (s *DatabaseStorage) CreateGroup(group *types.ProxyGroup) error {
	return s.db.Create(group).Error
}

func (s *DatabaseStorage) GetGroup(groupID string) (*types.ProxyGroup, error) {
	var group types.ProxyGroup
	err := s.db.Where("id = ?", groupID).First(&group).Error
	if err != nil {
		return nil, err
	}
	return &group, nil
}

func (s *DatabaseStorage) UpdateGroup(group *types.ProxyGroup) error {
	return s.db.Save(group).Error
}

func (s *DatabaseStorage) DeleteGroup(groupID string) error {
	return s.db.Where("id = ?", groupID).Delete(&types.ProxyGroup{}).Error
}

func (s *DatabaseStorage) ListGroups() ([]*types.ProxyGroup, error) {
	var groups []*types.ProxyGroup
	err := s.db.Find(&groups).Error
	return groups, err
}

// 实现 MessageStorage 接口
func (s *DatabaseStorage) SaveMessage(message *types.MessageRecord) error {
	return s.db.Create(message).Error
}

func (s *DatabaseStorage) GetMessage(messageID string) (*types.MessageRecord, error) {
	var message types.MessageRecord
	err := s.db.Where("message_id = ?", messageID).First(&message).Error
	if err != nil {
		return nil, err
	}
	return &message, nil
}

func (s *DatabaseStorage) UpdateMessage(message *types.MessageRecord) error {
	return s.db.Save(message).Error
}

func (s *DatabaseStorage) QueryMessages(sessionID string, filter *MessageFilter) ([]*types.MessageRecord, error) {
	query := s.db.Where("session_id = ?", sessionID)
	
	if filter != nil {
		if filter.ChatJID != "" {
			query = query.Where("chat_jid = ?", filter.ChatJID)
		}
		if filter.MessageType != "" {
			query = query.Where("message_type = ?", filter.MessageType)
		}
		if filter.StartTime != nil {
			query = query.Where("timestamp >= ?", *filter.StartTime)
		}
		if filter.EndTime != nil {
			query = query.Where("timestamp <= ?", *filter.EndTime)
		}
		if filter.Limit > 0 {
			query = query.Limit(filter.Limit)
		}
		if filter.Offset > 0 {
			query = query.Offset(filter.Offset)
		}
	}
	
	var messages []*types.MessageRecord
	err := query.Order("timestamp DESC").Find(&messages).Error
	return messages, err
}

func (s *DatabaseStorage) DeleteOldMessages(beforeDate time.Time) error {
	return s.db.Where("created_at < ?", beforeDate).Delete(&types.MessageRecord{}).Error
}

// GetSessionCount 获取会话总数
func (s *DatabaseStorage) GetSessionCount() (int64, error) {
	var count int64
	err := s.db.Model(&types.WhatsAppSession{}).Count(&count).Error
	return count, err
}

// GetProxyCount 获取代理总数
func (s *DatabaseStorage) GetProxyCount() (int64, error) {
	var count int64
	err := s.db.Model(&types.ProxyInfo{}).Count(&count).Error
	return count, err
}

// GetActiveProxyCount 获取活跃代理数量
func (s *DatabaseStorage) GetActiveProxyCount() (int64, error) {
	var count int64
	err := s.db.Model(&types.ProxyInfo{}).Where("status = ?", types.ProxyStatusActive).Count(&count).Error
	return count, err
}

// GetMessageCount 获取消息总数
func (s *DatabaseStorage) GetMessageCount() (int64, error) {
	var count int64
	err := s.db.Model(&types.MessageRecord{}).Count(&count).Error
	return count, err
}
