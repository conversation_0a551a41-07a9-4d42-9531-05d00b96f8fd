package proxy

import (
	"fmt"
	"sync"
	"time"

	"whatsapp-engine/internal/config"
	"whatsapp-engine/internal/storage"
	"whatsapp-engine/internal/types"

	"github.com/google/uuid"
	"github.com/sirupsen/logrus"
)

// Manager 代理管理器
type Manager struct {
	storage       storage.ProxyStorage
	cache         storage.CacheInterface
	allocator     *Allocator
	healthChecker *HealthChecker
	failover      *FailoverManager
	config        *config.ProxyConfig
	
	// 内存中的代理映射
	proxies sync.Map // proxyID -> *types.ProxyInfo
	groups  sync.Map // groupID -> *types.ProxyGroup
	
	// 分配映射
	allocations sync.Map // sessionID -> proxyID
	
	logger *logrus.Logger
}

// NewManager 创建代理管理器
func NewManager(cfg *config.ProxyConfig, storage storage.ProxyStorage, cache storage.CacheInterface) *Manager {
	manager := &Manager{
		storage: storage,
		cache:   cache,
		config:  cfg,
		logger:  logrus.New(),
	}
	
	// 初始化子组件
	manager.allocator = NewAllocator(manager, cfg.Allocation)
	manager.healthChecker = NewHealthChecker(manager, cfg.HealthCheck)
	manager.failover = NewFailoverManager(manager, cfg.Failover)
	
	return manager
}

// Start 启动代理管理器
func (m *Manager) Start() error {
	m.logger.Info("启动代理管理器...")
	
	// 加载现有代理和分组
	if err := m.loadProxies(); err != nil {
		return fmt.Errorf("加载代理失败: %v", err)
	}
	
	if err := m.loadGroups(); err != nil {
		return fmt.Errorf("加载代理分组失败: %v", err)
	}
	
	// 启动健康检查器
	if err := m.healthChecker.Start(); err != nil {
		return fmt.Errorf("启动健康检查器失败: %v", err)
	}
	
	// 启动故障转移管理器
	if err := m.failover.Start(); err != nil {
		return fmt.Errorf("启动故障转移管理器失败: %v", err)
	}
	
	m.logger.Info("代理管理器启动成功")
	return nil
}

// Stop 停止代理管理器
func (m *Manager) Stop() error {
	m.logger.Info("停止代理管理器...")
	
	// 停止子组件
	if err := m.healthChecker.Stop(); err != nil {
		m.logger.Errorf("停止健康检查器失败: %v", err)
	}
	
	if err := m.failover.Stop(); err != nil {
		m.logger.Errorf("停止故障转移管理器失败: %v", err)
	}
	
	m.logger.Info("代理管理器已停止")
	return nil
}

// AddProxy 添加代理
func (m *Manager) AddProxy(proxy *types.ProxyInfo) error {
	if proxy.ID == "" {
		proxy.ID = uuid.New().String()
	}
	
	proxy.CreatedAt = time.Now()
	proxy.UpdatedAt = time.Now()
	proxy.Status = types.ProxyStatusActive
	
	// 保存到数据库
	if err := m.storage.CreateProxy(proxy); err != nil {
		return fmt.Errorf("保存代理到数据库失败: %v", err)
	}
	
	// 添加到内存映射
	m.proxies.Store(proxy.ID, proxy)
	
	// 缓存代理信息
	cacheKey := fmt.Sprintf("proxy:%s", proxy.ID)
	if err := m.cache.SetJSON(cacheKey, proxy, time.Hour); err != nil {
		m.logger.Warnf("缓存代理信息失败: %v", err)
	}
	
	m.logger.Infof("添加代理成功: %s (%s:%d)", proxy.ID, proxy.Host, proxy.Port)
	return nil
}

// GetProxy 获取代理
func (m *Manager) GetProxy(proxyID string) *types.ProxyInfo {
	// 先从内存中获取
	if proxy, exists := m.proxies.Load(proxyID); exists {
		return proxy.(*types.ProxyInfo)
	}
	
	// 从缓存中获取
	cacheKey := fmt.Sprintf("proxy:%s", proxyID)
	var proxy types.ProxyInfo
	if err := m.cache.GetJSON(cacheKey, &proxy); err == nil {
		m.proxies.Store(proxyID, &proxy)
		return &proxy
	}
	
	// 从数据库中获取
	proxy_db, err := m.storage.GetProxy(proxyID)
	if err != nil {
		return nil
	}
	
	// 更新内存和缓存
	m.proxies.Store(proxyID, proxy_db)
	m.cache.SetJSON(cacheKey, proxy_db, time.Hour)
	
	return proxy_db
}

// UpdateProxy 更新代理
func (m *Manager) UpdateProxy(proxy *types.ProxyInfo) error {
	proxy.UpdatedAt = time.Now()
	
	// 更新数据库
	if err := m.storage.UpdateProxy(proxy); err != nil {
		return fmt.Errorf("更新代理到数据库失败: %v", err)
	}
	
	// 更新内存映射
	m.proxies.Store(proxy.ID, proxy)
	
	// 更新缓存
	cacheKey := fmt.Sprintf("proxy:%s", proxy.ID)
	if err := m.cache.SetJSON(cacheKey, proxy, time.Hour); err != nil {
		m.logger.Warnf("更新代理缓存失败: %v", err)
	}
	
	return nil
}

// DeleteProxy 删除代理
func (m *Manager) DeleteProxy(proxyID string) error {
	// 从数据库删除
	if err := m.storage.DeleteProxy(proxyID); err != nil {
		return fmt.Errorf("从数据库删除代理失败: %v", err)
	}
	
	// 从内存删除
	m.proxies.Delete(proxyID)
	
	// 从缓存删除
	cacheKey := fmt.Sprintf("proxy:%s", proxyID)
	m.cache.Delete(cacheKey)
	
	m.logger.Infof("删除代理成功: %s", proxyID)
	return nil
}

// ListProxies 列出代理
func (m *Manager) ListProxies(filter *storage.ProxyFilter) ([]*types.ProxyInfo, error) {
	return m.storage.ListProxies(filter)
}

// CreateGroup 创建代理分组
func (m *Manager) CreateGroup(group *types.ProxyGroup) error {
	if group.ID == "" {
		group.ID = uuid.New().String()
	}
	
	group.CreatedAt = time.Now()
	group.UpdatedAt = time.Now()
	
	// 保存到数据库
	if err := m.storage.CreateGroup(group); err != nil {
		return fmt.Errorf("保存代理分组到数据库失败: %v", err)
	}
	
	// 添加到内存映射
	m.groups.Store(group.ID, group)
	
	m.logger.Infof("创建代理分组成功: %s", group.ID)
	return nil
}

// GetGroup 获取代理分组
func (m *Manager) GetGroup(groupID string) *types.ProxyGroup {
	// 先从内存中获取
	if group, exists := m.groups.Load(groupID); exists {
		return group.(*types.ProxyGroup)
	}
	
	// 从数据库中获取
	group, err := m.storage.GetGroup(groupID)
	if err != nil {
		return nil
	}
	
	// 更新内存
	m.groups.Store(groupID, group)
	return group
}

// AllocateProxy 分配代理
func (m *Manager) AllocateProxy(req *types.ProxyAllocationRequest) (*types.ProxyInfo, error) {
	return m.allocator.AllocateProxy(req)
}

// ReleaseProxy 释放代理
func (m *Manager) ReleaseProxy(sessionID string) error {
	if proxyID, exists := m.allocations.Load(sessionID); exists {
		// 减少代理负载
		if proxy := m.GetProxy(proxyID.(string)); proxy != nil {
			proxy.CurrentLoad--
			if proxy.CurrentLoad < 0 {
				proxy.CurrentLoad = 0
			}
			m.UpdateProxy(proxy)
		}
		
		// 删除分配记录
		m.allocations.Delete(sessionID)
	}
	
	return nil
}

// MarkProxyFailed 标记代理失败
func (m *Manager) MarkProxyFailed(proxyID string, reason string) {
	proxy := m.GetProxy(proxyID)
	if proxy == nil {
		return
	}
	
	proxy.Status = types.ProxyStatusError
	proxy.UpdatedAt = time.Now()
	
	if err := m.UpdateProxy(proxy); err != nil {
		m.logger.Errorf("更新失败代理状态失败: %v", err)
	}
	
	m.logger.Warnf("代理标记为失败: %s, 原因: %s", proxyID, reason)
}

// IncrementProxyLoad 增加代理负载
func (m *Manager) IncrementProxyLoad(proxyID string) {
	proxy := m.GetProxy(proxyID)
	if proxy == nil {
		return
	}
	
	proxy.CurrentLoad++
	proxy.LastCheck = time.Now()
	
	if err := m.UpdateProxy(proxy); err != nil {
		m.logger.Errorf("更新代理负载失败: %v", err)
	}
}

// GetStats 获取代理统计信息
func (m *Manager) GetStats() *ProxyStats {
	stats := &ProxyStats{
		TotalProxies:  0,
		ActiveProxies: 0,
		FailedProxies: 0,
		RegionStats:   make(map[string]int),
		QualityStats:  make(map[string]int),
	}
	
	m.proxies.Range(func(key, value interface{}) bool {
		proxy := value.(*types.ProxyInfo)
		stats.TotalProxies++
		
		switch proxy.Status {
		case types.ProxyStatusActive:
			stats.ActiveProxies++
		case types.ProxyStatusError:
			stats.FailedProxies++
		}
		
		stats.RegionStats[proxy.Region]++
		stats.QualityStats[string(proxy.Quality)]++
		
		return true
	})
	
	return stats
}

// loadProxies 加载现有代理
func (m *Manager) loadProxies() error {
	proxies, err := m.storage.ListProxies(nil)
	if err != nil {
		return err
	}
	
	for _, proxy := range proxies {
		m.proxies.Store(proxy.ID, proxy)
	}
	
	m.logger.Infof("加载了 %d 个代理", len(proxies))
	return nil
}

// loadGroups 加载现有分组
func (m *Manager) loadGroups() error {
	groups, err := m.storage.ListGroups()
	if err != nil {
		return err
	}
	
	for _, group := range groups {
		m.groups.Store(group.ID, group)
	}
	
	m.logger.Infof("加载了 %d 个代理分组", len(groups))
	return nil
}

// ProxyStats 代理统计信息
type ProxyStats struct {
	TotalProxies  int            `json:"total_proxies"`
	ActiveProxies int            `json:"active_proxies"`
	FailedProxies int            `json:"failed_proxies"`
	RegionStats   map[string]int `json:"region_stats"`
	QualityStats  map[string]int `json:"quality_stats"`
}
