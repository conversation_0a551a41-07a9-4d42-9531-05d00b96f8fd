package proxy

import (
	"sync"
	"time"

	"whatsapp-engine/internal/config"
	"whatsapp-engine/internal/types"

	"github.com/sirupsen/logrus"
)

// FailoverManager 故障转移管理器
type FailoverManager struct {
	manager      *Manager
	config       config.ProxyFailoverConfig
	failoverQueue chan *FailoverRequest
	stopChan     chan struct{}
	wg           sync.WaitGroup
	logger       *logrus.Logger
	
	// 故障转移统计
	failoverCount sync.Map // proxyID -> int
	lastFailover  sync.Map // proxyID -> time.Time
}

// FailoverRequest 故障转移请求
type FailoverRequest struct {
	SessionID    string    `json:"session_id"`
	FailedProxy  string    `json:"failed_proxy"`
	Reason       string    `json:"reason"`
	Timestamp    time.Time `json:"timestamp"`
	Urgent       bool      `json:"urgent"`
	RetryCount   int       `json:"retry_count"`
}

// NewFailoverManager 创建故障转移管理器
func NewFailoverManager(manager *Manager, cfg config.ProxyFailoverConfig) *FailoverManager {
	return &FailoverManager{
		manager:       manager,
		config:        cfg,
		failoverQueue: make(chan *FailoverRequest, 1000),
		stopChan:      make(chan struct{}),
		logger:        logrus.New(),
	}
}

// Start 启动故障转移管理器
func (fm *FailoverManager) Start() error {
	if !fm.config.Enabled {
		fm.logger.Info("故障转移管理器已禁用")
		return nil
	}
	
	fm.logger.Info("启动故障转移管理器...")
	
	// 启动工作协程
	for i := 0; i < fm.config.Workers; i++ {
		fm.wg.Add(1)
		go fm.failoverWorker(i)
	}
	
	fm.logger.Infof("故障转移管理器启动成功，工作协程数: %d", fm.config.Workers)
	return nil
}

// Stop 停止故障转移管理器
func (fm *FailoverManager) Stop() error {
	if !fm.config.Enabled {
		return nil
	}
	
	fm.logger.Info("停止故障转移管理器...")
	
	close(fm.stopChan)
	fm.wg.Wait()
	
	fm.logger.Info("故障转移管理器已停止")
	return nil
}

// HandleProxyFailure 处理代理失败
func (fm *FailoverManager) HandleProxyFailure(proxyID string, reason string) {
	if !fm.config.Enabled {
		return
	}
	
	// 查找使用该代理的所有会话
	affectedSessions := fm.findAffectedSessions(proxyID)
	
	for _, sessionID := range affectedSessions {
		request := &FailoverRequest{
			SessionID:   sessionID,
			FailedProxy: proxyID,
			Reason:      reason,
			Timestamp:   time.Now(),
			Urgent:      true,
			RetryCount:  0,
		}
		
		// 添加到故障转移队列
		select {
		case fm.failoverQueue <- request:
			fm.logger.Infof("添加故障转移请求: 会话 %s, 失败代理 %s", sessionID, proxyID)
		default:
			fm.logger.Errorf("故障转移队列已满，丢弃请求: 会话 %s", sessionID)
		}
	}
}

// HandleSessionFailover 处理会话故障转移
func (fm *FailoverManager) HandleSessionFailover(sessionID string, reason string) {
	if !fm.config.Enabled {
		return
	}
	
	// 获取当前使用的代理
	proxyID, exists := fm.manager.allocations.Load(sessionID)
	if !exists {
		fm.logger.Warnf("会话 %s 没有分配代理，无法进行故障转移", sessionID)
		return
	}
	
	request := &FailoverRequest{
		SessionID:   sessionID,
		FailedProxy: proxyID.(string),
		Reason:      reason,
		Timestamp:   time.Now(),
		Urgent:      false,
		RetryCount:  0,
	}
	
	// 添加到故障转移队列
	select {
	case fm.failoverQueue <- request:
		fm.logger.Infof("添加会话故障转移请求: %s", sessionID)
	default:
		fm.logger.Errorf("故障转移队列已满，丢弃会话请求: %s", sessionID)
	}
}

// failoverWorker 故障转移工作协程
func (fm *FailoverManager) failoverWorker(workerID int) {
	defer fm.wg.Done()
	
	fm.logger.Debugf("故障转移工作协程 %d 启动", workerID)
	
	for {
		select {
		case request := <-fm.failoverQueue:
			fm.performFailover(request)
		case <-fm.stopChan:
			fm.logger.Debugf("故障转移工作协程 %d 停止", workerID)
			return
		}
	}
}

// performFailover 执行故障转移
func (fm *FailoverManager) performFailover(request *FailoverRequest) {
	fm.logger.Infof("执行故障转移: 会话 %s, 失败代理 %s, 原因: %s", 
		request.SessionID, request.FailedProxy, request.Reason)
	
	// 检查重试次数
	if request.RetryCount >= fm.config.MaxFailoverAttempts {
		fm.logger.Errorf("故障转移达到最大重试次数: 会话 %s", request.SessionID)
		return
	}
	
	// 记录故障转移统计
	fm.recordFailover(request.FailedProxy)
	
	// 标记失败的代理
	fm.manager.MarkProxyFailed(request.FailedProxy, request.Reason)
	
	// 分配新代理
	newProxy, err := fm.allocateNewProxy(request)
	if err != nil {
		fm.logger.Errorf("为会话 %s 分配新代理失败: %v", request.SessionID, err)
		
		// 如果分配失败，重试
		if request.RetryCount < fm.config.MaxFailoverAttempts {
			request.RetryCount++
			
			// 延迟重试
			go func() {
				time.Sleep(time.Second * 5)
				select {
				case fm.failoverQueue <- request:
					// 重新加入队列
				default:
					fm.logger.Errorf("重试队列已满，放弃重试: 会话 %s", request.SessionID)
				}
			}()
		}
		return
	}
	
	// 更新分配记录
	fm.manager.allocations.Store(request.SessionID, newProxy.ID)
	
	// 增加新代理负载
	fm.manager.IncrementProxyLoad(newProxy.ID)
	
	fm.logger.Infof("故障转移成功: 会话 %s 从代理 %s 切换到 %s", 
		request.SessionID, request.FailedProxy, newProxy.ID)
	
	// 通知会话管理器重新连接（如果有的话）
	// 这里可以添加回调机制通知上层应用
}

// allocateNewProxy 分配新代理
func (fm *FailoverManager) allocateNewProxy(request *FailoverRequest) (*types.ProxyInfo, error) {
	// 构造分配请求
	allocReq := &types.ProxyAllocationRequest{
		SessionID:      request.SessionID,
		ExcludeProxies: []string{request.FailedProxy},
		Sticky:         false, // 故障转移时不使用粘性分配
	}
	
	// 尝试获取会话的首选地区
	if region, exists := fm.manager.allocator.affinityMap.Load(request.SessionID); exists {
		allocReq.PreferredRegion = region.(string)
	}
	
	// 分配新代理
	return fm.manager.allocator.AllocateProxy(allocReq)
}

// findAffectedSessions 查找受影响的会话
func (fm *FailoverManager) findAffectedSessions(proxyID string) []string {
	var sessions []string
	
	fm.manager.allocations.Range(func(key, value interface{}) bool {
		sessionID := key.(string)
		allocatedProxyID := value.(string)
		
		if allocatedProxyID == proxyID {
			sessions = append(sessions, sessionID)
		}
		
		return true
	})
	
	return sessions
}

// recordFailover 记录故障转移
func (fm *FailoverManager) recordFailover(proxyID string) {
	// 增加故障转移计数
	count := 1
	if existing, exists := fm.failoverCount.Load(proxyID); exists {
		count = existing.(int) + 1
	}
	fm.failoverCount.Store(proxyID, count)
	
	// 记录最后故障转移时间
	fm.lastFailover.Store(proxyID, time.Now())
}

// GetFailoverStats 获取故障转移统计
func (fm *FailoverManager) GetFailoverStats() *FailoverStats {
	stats := &FailoverStats{
		TotalFailovers:   0,
		QueueSize:        len(fm.failoverQueue),
		ProxyFailovers:   make(map[string]int),
		RecentFailovers:  make([]FailoverRecord, 0),
	}
	
	// 统计总故障转移次数
	fm.failoverCount.Range(func(key, value interface{}) bool {
		proxyID := key.(string)
		count := value.(int)
		
		stats.TotalFailovers += count
		stats.ProxyFailovers[proxyID] = count
		
		// 获取最后故障转移时间
		if lastTime, exists := fm.lastFailover.Load(proxyID); exists {
			stats.RecentFailovers = append(stats.RecentFailovers, FailoverRecord{
				ProxyID:   proxyID,
				Count:     count,
				LastTime:  lastTime.(time.Time),
			})
		}
		
		return true
	})
	
	return stats
}

// IsProxyInFailover 检查代理是否在故障转移中
func (fm *FailoverManager) IsProxyInFailover(proxyID string) bool {
	if lastTime, exists := fm.lastFailover.Load(proxyID); exists {
		// 如果最近5分钟内发生过故障转移，认为还在故障转移状态
		return time.Since(lastTime.(time.Time)) < 5*time.Minute
	}
	return false
}

// ClearFailoverHistory 清理故障转移历史
func (fm *FailoverManager) ClearFailoverHistory(proxyID string) {
	fm.failoverCount.Delete(proxyID)
	fm.lastFailover.Delete(proxyID)
}

// FailoverStats 故障转移统计
type FailoverStats struct {
	TotalFailovers  int                    `json:"total_failovers"`
	QueueSize       int                    `json:"queue_size"`
	ProxyFailovers  map[string]int         `json:"proxy_failovers"`
	RecentFailovers []FailoverRecord       `json:"recent_failovers"`
}

// FailoverRecord 故障转移记录
type FailoverRecord struct {
	ProxyID  string    `json:"proxy_id"`
	Count    int       `json:"count"`
	LastTime time.Time `json:"last_time"`
}
