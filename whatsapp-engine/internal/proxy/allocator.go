package proxy

import (
	"fmt"
	"sort"
	"sync"
	"time"

	"whatsapp-engine/internal/config"
	"whatsapp-engine/internal/storage"
	"whatsapp-engine/internal/types"

	"github.com/sirupsen/logrus"
)

// Allocator 代理分配器
type Allocator struct {
	manager   *Manager
	config    config.ProxyAllocationConfig
	affinityMap sync.Map // sessionID -> preferredRegion
	logger    *logrus.Logger
}

// NewAllocator 创建代理分配器
func NewAllocator(manager *Manager, cfg config.ProxyAllocationConfig) *Allocator {
	return &Allocator{
		manager: manager,
		config:  cfg,
		logger:  logrus.New(),
	}
}

// AllocateProxy 分配代理
func (a *Allocator) AllocateProxy(req *types.ProxyAllocationRequest) (*types.ProxyInfo, error) {
	// 1. 检查是否已有分配的代理（粘性会话）
	if a.config.StickySessions && req.Sticky {
		if existingProxyID, exists := a.manager.allocations.Load(req.SessionID); exists {
			if proxy := a.manager.GetProxy(existingProxyID.(string)); proxy != nil {
				if proxy.Status == types.ProxyStatusActive && a.isProxyHealthy(proxy) {
					a.logger.Debugf("使用现有代理: %s for session %s", proxy.ID, req.SessionID)
					return proxy, nil
				}
			}
		}
	}
	
	// 2. 根据策略选择新代理
	proxy, err := a.selectProxyByStrategy(req)
	if err != nil {
		return nil, err
	}
	
	// 3. 分配代理
	a.manager.allocations.Store(req.SessionID, proxy.ID)
	if req.PreferredRegion != "" {
		a.affinityMap.Store(req.SessionID, req.PreferredRegion)
	}
	
	// 4. 更新代理负载
	a.manager.IncrementProxyLoad(proxy.ID)
	
	a.logger.Infof("为会话 %s 分配代理: %s (%s:%d)", req.SessionID, proxy.ID, proxy.Host, proxy.Port)
	return proxy, nil
}

// selectProxyByStrategy 根据策略选择代理
func (a *Allocator) selectProxyByStrategy(req *types.ProxyAllocationRequest) (*types.ProxyInfo, error) {
	switch a.config.Strategy {
	case "region_affinity":
		return a.selectByRegionAffinity(req)
	case "load_balance":
		return a.selectByLoadBalance(req)
	case "quality_first":
		return a.selectByQuality(req)
	default:
		return a.selectByRegionAffinity(req)
	}
}

// selectByRegionAffinity 按地区亲和性选择代理
func (a *Allocator) selectByRegionAffinity(req *types.ProxyAllocationRequest) (*types.ProxyInfo, error) {
	// 获取首选地区
	preferredRegion := req.PreferredRegion
	if preferredRegion == "" {
		if region, exists := a.affinityMap.Load(req.SessionID); exists {
			preferredRegion = region.(string)
		}
	}
	
	// 构建过滤器
	filter := &storage.ProxyFilter{
		Status: types.ProxyStatusActive,
	}
	
	if req.RequiredQuality != "" {
		filter.Quality = req.RequiredQuality
	}
	
	// 首先尝试在首选地区查找
	if preferredRegion != "" {
		filter.Region = preferredRegion
		proxies, err := a.manager.ListProxies(filter)
		if err == nil && len(proxies) > 0 {
			return a.selectBestProxy(proxies, req.ExcludeProxies)
		}
	}
	
	// 如果首选地区没有可用代理，则在所有地区查找
	filter.Region = ""
	proxies, err := a.manager.ListProxies(filter)
	if err != nil {
		return nil, fmt.Errorf("查询代理失败: %v", err)
	}
	
	if len(proxies) == 0 {
		return nil, fmt.Errorf("没有可用的代理")
	}
	
	return a.selectBestProxy(proxies, req.ExcludeProxies)
}

// selectByLoadBalance 按负载均衡选择代理
func (a *Allocator) selectByLoadBalance(req *types.ProxyAllocationRequest) (*types.ProxyInfo, error) {
	filter := &storage.ProxyFilter{
		Status: types.ProxyStatusActive,
	}
	
	if req.RequiredQuality != "" {
		filter.Quality = req.RequiredQuality
	}
	
	proxies, err := a.manager.ListProxies(filter)
	if err != nil {
		return nil, fmt.Errorf("查询代理失败: %v", err)
	}
	
	if len(proxies) == 0 {
		return nil, fmt.Errorf("没有可用的代理")
	}
	
	// 过滤排除的代理
	availableProxies := a.filterExcludedProxies(proxies, req.ExcludeProxies)
	if len(availableProxies) == 0 {
		return nil, fmt.Errorf("没有可用的代理（排除后）")
	}
	
	// 按负载排序，选择负载最低的
	sort.Slice(availableProxies, func(i, j int) bool {
		loadRatioI := float64(availableProxies[i].CurrentLoad) / float64(availableProxies[i].MaxConnections)
		loadRatioJ := float64(availableProxies[j].CurrentLoad) / float64(availableProxies[j].MaxConnections)
		return loadRatioI < loadRatioJ
	})
	
	return availableProxies[0], nil
}

// selectByQuality 按质量优先选择代理
func (a *Allocator) selectByQuality(req *types.ProxyAllocationRequest) (*types.ProxyInfo, error) {
	filter := &storage.ProxyFilter{
		Status: types.ProxyStatusActive,
	}
	
	proxies, err := a.manager.ListProxies(filter)
	if err != nil {
		return nil, fmt.Errorf("查询代理失败: %v", err)
	}
	
	if len(proxies) == 0 {
		return nil, fmt.Errorf("没有可用的代理")
	}
	
	// 过滤排除的代理
	availableProxies := a.filterExcludedProxies(proxies, req.ExcludeProxies)
	if len(availableProxies) == 0 {
		return nil, fmt.Errorf("没有可用的代理（排除后）")
	}
	
	// 按质量排序：高质量 > 中等质量 > 低质量
	sort.Slice(availableProxies, func(i, j int) bool {
		qualityOrder := map[types.ProxyQuality]int{
			types.ProxyQualityHigh:   3,
			types.ProxyQualityMedium: 2,
			types.ProxyQualityLow:    1,
		}
		
		orderI := qualityOrder[availableProxies[i].Quality]
		orderJ := qualityOrder[availableProxies[j].Quality]
		
		if orderI != orderJ {
			return orderI > orderJ
		}
		
		// 质量相同时，按负载排序
		loadRatioI := float64(availableProxies[i].CurrentLoad) / float64(availableProxies[i].MaxConnections)
		loadRatioJ := float64(availableProxies[j].CurrentLoad) / float64(availableProxies[j].MaxConnections)
		return loadRatioI < loadRatioJ
	})
	
	return availableProxies[0], nil
}

// selectBestProxy 选择最佳代理
func (a *Allocator) selectBestProxy(proxies []*types.ProxyInfo, excludeProxies []string) (*types.ProxyInfo, error) {
	// 过滤排除的代理
	availableProxies := a.filterExcludedProxies(proxies, excludeProxies)
	if len(availableProxies) == 0 {
		return nil, fmt.Errorf("没有可用的代理（排除后）")
	}
	
	// 过滤健康的代理
	healthyProxies := make([]*types.ProxyInfo, 0)
	for _, proxy := range availableProxies {
		if a.isProxyHealthy(proxy) {
			healthyProxies = append(healthyProxies, proxy)
		}
	}
	
	if len(healthyProxies) == 0 {
		// 如果没有健康的代理，返回第一个可用的
		return availableProxies[0], nil
	}
	
	// 按综合评分排序
	sort.Slice(healthyProxies, func(i, j int) bool {
		scoreI := a.calculateProxyScore(healthyProxies[i])
		scoreJ := a.calculateProxyScore(healthyProxies[j])
		return scoreI > scoreJ
	})
	
	return healthyProxies[0], nil
}

// filterExcludedProxies 过滤排除的代理
func (a *Allocator) filterExcludedProxies(proxies []*types.ProxyInfo, excludeProxies []string) []*types.ProxyInfo {
	if len(excludeProxies) == 0 {
		return proxies
	}
	
	excludeMap := make(map[string]bool)
	for _, proxyID := range excludeProxies {
		excludeMap[proxyID] = true
	}
	
	filtered := make([]*types.ProxyInfo, 0)
	for _, proxy := range proxies {
		if !excludeMap[proxy.ID] {
			filtered = append(filtered, proxy)
		}
	}
	
	return filtered
}

// isProxyHealthy 检查代理是否健康
func (a *Allocator) isProxyHealthy(proxy *types.ProxyInfo) bool {
	// 检查代理状态
	if proxy.Status != types.ProxyStatusActive {
		return false
	}
	
	// 检查负载
	if proxy.MaxConnections > 0 {
		loadRatio := float64(proxy.CurrentLoad) / float64(proxy.MaxConnections)
		if loadRatio >= 0.9 { // 负载超过90%认为不健康
			return false
		}
	}
	
	// 检查成功率
	if proxy.SuccessRate < 0.8 { // 成功率低于80%认为不健康
		return false
	}
	
	// 检查最后检查时间
	if time.Since(proxy.LastCheck) > 5*time.Minute { // 超过5分钟没有检查认为不健康
		return false
	}
	
	return true
}

// calculateProxyScore 计算代理评分
func (a *Allocator) calculateProxyScore(proxy *types.ProxyInfo) float64 {
	score := 0.0
	
	// 质量评分 (40%)
	qualityScore := map[types.ProxyQuality]float64{
		types.ProxyQualityHigh:   1.0,
		types.ProxyQualityMedium: 0.7,
		types.ProxyQualityLow:    0.4,
	}
	score += qualityScore[proxy.Quality] * 0.4
	
	// 成功率评分 (30%)
	score += proxy.SuccessRate * 0.3
	
	// 负载评分 (20%)
	if proxy.MaxConnections > 0 {
		loadRatio := float64(proxy.CurrentLoad) / float64(proxy.MaxConnections)
		loadScore := 1.0 - loadRatio
		if loadScore < 0 {
			loadScore = 0
		}
		score += loadScore * 0.2
	} else {
		score += 0.2 // 如果没有设置最大连接数，给满分
	}
	
	// 响应时间评分 (10%)
	responseTimeScore := 1.0
	if proxy.ResponseTime > 0 {
		// 响应时间越短评分越高，以1秒为基准
		responseTimeScore = 1000.0 / float64(proxy.ResponseTime)
		if responseTimeScore > 1.0 {
			responseTimeScore = 1.0
		}
	}
	score += responseTimeScore * 0.1
	
	return score
}

// ReleaseAllocation 释放分配
func (a *Allocator) ReleaseAllocation(sessionID string) {
	a.manager.allocations.Delete(sessionID)
	a.affinityMap.Delete(sessionID)
}

// GetAllocation 获取分配信息
func (a *Allocator) GetAllocation(sessionID string) (string, bool) {
	if proxyID, exists := a.manager.allocations.Load(sessionID); exists {
		return proxyID.(string), true
	}
	return "", false
}

// GetAllocationStats 获取分配统计
func (a *Allocator) GetAllocationStats() *AllocationStats {
	stats := &AllocationStats{
		TotalAllocations: 0,
		RegionStats:      make(map[string]int),
	}
	
	a.manager.allocations.Range(func(key, value interface{}) bool {
		stats.TotalAllocations++
		
		proxyID := value.(string)
		if proxy := a.manager.GetProxy(proxyID); proxy != nil {
			stats.RegionStats[proxy.Region]++
		}
		
		return true
	})
	
	return stats
}

// AllocationStats 分配统计
type AllocationStats struct {
	TotalAllocations int            `json:"total_allocations"`
	RegionStats      map[string]int `json:"region_stats"`
}
