package proxy

import (
	"context"
	"fmt"
	"net/http"
	"net/url"
	"sync"
	"time"

	"whatsapp-engine/internal/config"
	"whatsapp-engine/internal/types"

	"github.com/sirupsen/logrus"
)

// HealthChecker 代理健康检查器
type HealthChecker struct {
	manager     *Manager
	config      config.ProxyHealthCheckConfig
	checkQueue  chan *types.ProxyInfo
	stopChan    chan struct{}
	wg          sync.WaitGroup
	logger      *logrus.Logger
	
	// 失败计数器
	failureCount sync.Map // proxyID -> int
}

// NewHealthChecker 创建健康检查器
func NewHealthChecker(manager *Manager, cfg config.ProxyHealthCheckConfig) *HealthChecker {
	return &HealthChecker{
		manager:    manager,
		config:     cfg,
		checkQueue: make(chan *types.ProxyInfo, 1000),
		stopChan:   make(chan struct{}),
		logger:     logrus.New(),
	}
}

// Start 启动健康检查器
func (hc *HealthChecker) Start() error {
	hc.logger.Info("启动代理健康检查器...")
	
	// 启动工作协程
	for i := 0; i < hc.config.Workers; i++ {
		hc.wg.Add(1)
		go hc.healthCheckWorker(i)
	}
	
	// 启动定时检查协程
	hc.wg.Add(1)
	go hc.scheduleHealthChecks()
	
	hc.logger.Infof("代理健康检查器启动成功，工作协程数: %d", hc.config.Workers)
	return nil
}

// Stop 停止健康检查器
func (hc *HealthChecker) Stop() error {
	hc.logger.Info("停止代理健康检查器...")
	
	close(hc.stopChan)
	hc.wg.Wait()
	
	hc.logger.Info("代理健康检查器已停止")
	return nil
}

// CheckProxy 检查单个代理
func (hc *HealthChecker) CheckProxy(proxy *types.ProxyInfo) *types.HealthCheckResult {
	start := time.Now()
	
	result := &types.HealthCheckResult{
		ProxyID:   proxy.ID,
		Timestamp: start,
	}
	
	// 创建代理客户端
	client, err := hc.createProxyClient(proxy)
	if err != nil {
		result.Success = false
		result.Error = fmt.Sprintf("创建代理客户端失败: %v", err)
		result.ResponseTime = time.Since(start)
		return result
	}
	
	// 创建请求上下文
	ctx, cancel := context.WithTimeout(context.Background(), hc.config.Timeout)
	defer cancel()
	
	// 创建测试请求
	req, err := http.NewRequestWithContext(ctx, "GET", "https://httpbin.org/ip", nil)
	if err != nil {
		result.Success = false
		result.Error = fmt.Sprintf("创建请求失败: %v", err)
		result.ResponseTime = time.Since(start)
		return result
	}
	
	// 发送请求
	resp, err := client.Do(req)
	result.ResponseTime = time.Since(start)
	
	if err != nil {
		result.Success = false
		result.Error = fmt.Sprintf("请求失败: %v", err)
		return result
	}
	defer resp.Body.Close()
	
	// 检查响应状态
	if resp.StatusCode == 200 {
		result.Success = true
	} else {
		result.Success = false
		result.Error = fmt.Sprintf("HTTP状态码: %d", resp.StatusCode)
	}
	
	return result
}

// createProxyClient 创建代理客户端
func (hc *HealthChecker) createProxyClient(proxy *types.ProxyInfo) (*http.Client, error) {
	// 构造代理URL
	var proxyURL *url.URL
	var err error
	
	if proxy.Username != "" && proxy.Password != "" {
		proxyURL, err = url.Parse(fmt.Sprintf("%s://%s:%s@%s:%d",
			proxy.Type, proxy.Username, proxy.Password, proxy.Host, proxy.Port))
	} else {
		proxyURL, err = url.Parse(fmt.Sprintf("%s://%s:%d",
			proxy.Type, proxy.Host, proxy.Port))
	}
	
	if err != nil {
		return nil, fmt.Errorf("解析代理URL失败: %v", err)
	}
	
	// 创建传输层
	transport := &http.Transport{
		Proxy: http.ProxyURL(proxyURL),
	}
	
	// 创建客户端
	client := &http.Client{
		Transport: transport,
		Timeout:   hc.config.Timeout,
	}
	
	return client, nil
}

// healthCheckWorker 健康检查工作协程
func (hc *HealthChecker) healthCheckWorker(workerID int) {
	defer hc.wg.Done()
	
	hc.logger.Debugf("健康检查工作协程 %d 启动", workerID)
	
	for {
		select {
		case proxy := <-hc.checkQueue:
			hc.performHealthCheck(proxy)
		case <-hc.stopChan:
			hc.logger.Debugf("健康检查工作协程 %d 停止", workerID)
			return
		}
	}
}

// performHealthCheck 执行健康检查
func (hc *HealthChecker) performHealthCheck(proxy *types.ProxyInfo) {
	hc.logger.Debugf("检查代理健康状态: %s (%s:%d)", proxy.ID, proxy.Host, proxy.Port)
	
	// 执行健康检查
	result := hc.CheckProxy(proxy)
	
	// 更新代理信息
	proxy.LastCheck = result.Timestamp
	proxy.ResponseTime = result.ResponseTime.Milliseconds()
	
	if result.Success {
		// 检查成功，重置失败计数
		hc.failureCount.Delete(proxy.ID)
		
		// 更新成功率
		hc.updateSuccessRate(proxy, true)
		
		// 如果代理之前是错误状态，恢复为活跃状态
		if proxy.Status == types.ProxyStatusError {
			proxy.Status = types.ProxyStatusActive
			hc.logger.Infof("代理恢复正常: %s", proxy.ID)
		}
	} else {
		// 检查失败，增加失败计数
		failCount := hc.incrementFailureCount(proxy.ID)
		
		// 更新成功率
		hc.updateSuccessRate(proxy, false)
		
		hc.logger.Warnf("代理健康检查失败: %s, 失败次数: %d, 错误: %s", 
			proxy.ID, failCount, result.Error)
		
		// 如果失败次数超过阈值，标记为错误状态
		if failCount >= hc.config.FailureThreshold {
			proxy.Status = types.ProxyStatusError
			hc.logger.Errorf("代理标记为错误状态: %s, 连续失败 %d 次", proxy.ID, failCount)
			
			// 触发故障转移
			hc.manager.failover.HandleProxyFailure(proxy.ID, result.Error)
		}
	}
	
	// 更新代理信息
	if err := hc.manager.UpdateProxy(proxy); err != nil {
		hc.logger.Errorf("更新代理信息失败: %v", err)
	}
}

// scheduleHealthChecks 定时调度健康检查
func (hc *HealthChecker) scheduleHealthChecks() {
	defer hc.wg.Done()
	
	ticker := time.NewTicker(hc.config.Interval)
	defer ticker.Stop()
	
	hc.logger.Infof("开始定时健康检查，间隔: %v", hc.config.Interval)
	
	for {
		select {
		case <-ticker.C:
			hc.scheduleAllProxies()
		case <-hc.stopChan:
			return
		}
	}
}

// scheduleAllProxies 调度所有代理进行健康检查
func (hc *HealthChecker) scheduleAllProxies() {
	hc.manager.proxies.Range(func(key, value interface{}) bool {
		proxy := value.(*types.ProxyInfo)
		
		// 只检查活跃和错误状态的代理
		if proxy.Status == types.ProxyStatusActive || proxy.Status == types.ProxyStatusError {
			select {
			case hc.checkQueue <- proxy:
				// 成功加入队列
			default:
				// 队列满了，跳过这次检查
				hc.logger.Warnf("健康检查队列已满，跳过代理: %s", proxy.ID)
			}
		}
		
		return true
	})
}

// incrementFailureCount 增加失败计数
func (hc *HealthChecker) incrementFailureCount(proxyID string) int {
	count := 1
	if existing, exists := hc.failureCount.Load(proxyID); exists {
		count = existing.(int) + 1
	}
	hc.failureCount.Store(proxyID, count)
	return count
}

// updateSuccessRate 更新成功率
func (hc *HealthChecker) updateSuccessRate(proxy *types.ProxyInfo, success bool) {
	// 简单的移动平均算法
	alpha := 0.1 // 平滑因子
	
	if success {
		proxy.SuccessRate = proxy.SuccessRate*(1-alpha) + alpha
	} else {
		proxy.SuccessRate = proxy.SuccessRate * (1 - alpha)
	}
	
	// 确保成功率在 0-1 范围内
	if proxy.SuccessRate < 0 {
		proxy.SuccessRate = 0
	} else if proxy.SuccessRate > 1 {
		proxy.SuccessRate = 1
	}
}

// GetHealthStats 获取健康检查统计
func (hc *HealthChecker) GetHealthStats() *HealthStats {
	stats := &HealthStats{
		TotalChecks:    0,
		SuccessfulChecks: 0,
		FailedChecks:   0,
		QueueSize:     len(hc.checkQueue),
	}
	
	hc.manager.proxies.Range(func(key, value interface{}) bool {
		proxy := value.(*types.ProxyInfo)
		stats.TotalChecks++
		
		if proxy.Status == types.ProxyStatusActive {
			stats.SuccessfulChecks++
		} else if proxy.Status == types.ProxyStatusError {
			stats.FailedChecks++
		}
		
		return true
	})
	
	if stats.TotalChecks > 0 {
		stats.SuccessRate = float64(stats.SuccessfulChecks) / float64(stats.TotalChecks)
	}
	
	return stats
}

// HealthStats 健康检查统计
type HealthStats struct {
	TotalChecks      int     `json:"total_checks"`
	SuccessfulChecks int     `json:"successful_checks"`
	FailedChecks     int     `json:"failed_checks"`
	SuccessRate      float64 `json:"success_rate"`
	QueueSize        int     `json:"queue_size"`
}
