package message

import (
	"context"
	"fmt"
	"sync"
	"time"

	"whatsapp-engine/internal/config"
	"whatsapp-engine/internal/session"
	"whatsapp-engine/internal/storage"
	"whatsapp-engine/internal/types"

	"github.com/google/uuid"
	"github.com/sirupsen/logrus"
	waProto "go.mau.fi/whatsmeow/binary/proto"
	waTypes "go.mau.fi/whatsmeow/types"
	"golang.org/x/time/rate"
	"google.golang.org/protobuf/proto"
)

// Manager 消息管理器
type Manager struct {
	config         *config.MessageConfig
	storage        storage.MessageStorage
	cache          storage.CacheInterface
	sessionManager *session.Manager
	
	// 速率限制器
	rateLimiters   sync.Map // sessionID -> *rate.Limiter
	
	// 消息队列
	messageQueue   chan *types.MessageRequest
	stopChan       chan struct{}
	wg             sync.WaitGroup
	
	// 统计信息
	stats          *Stats
	statsMutex     sync.RWMutex
	
	logger         *logrus.Logger
}

// NewManager 创建消息管理器
func NewManager(cfg *config.MessageConfig, storage storage.MessageStorage, 
	cache storage.CacheInterface, sessionManager *session.Manager) *Manager {
	
	return &Manager{
		config:         cfg,
		storage:        storage,
		cache:          cache,
		sessionManager: sessionManager,
		messageQueue:   make(chan *types.MessageRequest, cfg.Queue.BufferSize),
		stopChan:       make(chan struct{}),
		stats:          &Stats{},
		logger:         logrus.New(),
	}
}

// Start 启动消息管理器
func (m *Manager) Start() error {
	m.logger.Info("启动消息管理器...")
	
	// 启动工作协程
	for i := 0; i < m.config.Queue.Workers; i++ {
		m.wg.Add(1)
		go m.messageWorker(i)
	}
	
	m.logger.Infof("消息管理器启动成功，工作协程数: %d", m.config.Queue.Workers)
	return nil
}

// Stop 停止消息管理器
func (m *Manager) Stop() error {
	m.logger.Info("停止消息管理器...")
	
	close(m.stopChan)
	m.wg.Wait()
	
	m.logger.Info("消息管理器已停止")
	return nil
}

// SendMessage 发送消息
func (m *Manager) SendMessage(req *types.MessageRequest) (*types.MessageResult, error) {
	// 验证请求
	if err := m.validateMessageRequest(req); err != nil {
		return nil, fmt.Errorf("消息请求验证失败: %v", err)
	}
	
	// 检查速率限制
	if !m.checkRateLimit(req.SessionID) {
		return nil, fmt.Errorf("速率限制超出: %s", req.SessionID)
	}
	
	// 获取会话
	session := m.sessionManager.GetSession(req.SessionID)
	if session == nil {
		return nil, fmt.Errorf("会话不存在: %s", req.SessionID)
	}
	
	if session.Status != types.SessionStatusConnected {
		return nil, fmt.Errorf("会话未连接: %s", session.Status)
	}
	
	// 发送消息
	result, err := m.sendMessageDirect(session, req)
	if err != nil {
		m.updateStats(false)
		return nil, err
	}
	
	m.updateStats(true)
	return result, nil
}

// SendMessageAsync 异步发送消息
func (m *Manager) SendMessageAsync(req *types.MessageRequest) error {
	// 验证请求
	if err := m.validateMessageRequest(req); err != nil {
		return fmt.Errorf("消息请求验证失败: %v", err)
	}
	
	// 添加到队列
	select {
	case m.messageQueue <- req:
		return nil
	default:
		return fmt.Errorf("消息队列已满")
	}
}

// sendMessageDirect 直接发送消息
func (m *Manager) sendMessageDirect(session *types.WhatsAppSession, req *types.MessageRequest) (*types.MessageResult, error) {
	// 解析目标JID
	jid, err := waTypes.ParseJID(req.To)
	if err != nil {
		return nil, fmt.Errorf("无效的目标号码: %v", err)
	}
	
	// 构造WhatsApp消息
	waMsg, err := m.buildWhatsAppMessage(req)
	if err != nil {
		return nil, fmt.Errorf("构造消息失败: %v", err)
	}
	
	// 发送消息
	resp, err := session.Client.SendMessage(context.Background(), jid, waMsg)
	if err != nil {
		return nil, fmt.Errorf("发送消息失败: %v", err)
	}
	
	// 创建消息结果
	result := &types.MessageResult{
		MessageID: resp.ID,
		Status:    types.MessageStatusSent,
		SentAt:    time.Now(),
		Timestamp: resp.Timestamp,
	}
	
	// 保存消息记录
	messageRecord := &types.MessageRecord{
		ID:          uuid.New().String(),
		SessionID:   req.SessionID,
		ChatJID:     jid.String(),
		MessageID:   resp.ID,
		FromJID:     session.ClientInfo.WID,
		ToJID:       req.To,
		MessageType: req.MessageType,
		Content:     req.Content,
		Status:      types.MessageStatusSent,
		IsFromMe:    true,
		Timestamp:   result.Timestamp,
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
	}
	
	if err := m.storage.SaveMessage(messageRecord); err != nil {
		m.logger.Errorf("保存消息记录失败: %v", err)
	}
	
	// 更新会话活动时间
	session.LastActivity = time.Now()
	session.MessageCount++
	
	return result, nil
}

// buildWhatsAppMessage 构造WhatsApp消息
func (m *Manager) buildWhatsAppMessage(req *types.MessageRequest) (*waProto.Message, error) {
	switch req.MessageType {
	case types.MessageTypeText:
		return &waProto.Message{
			Conversation: proto.String(req.Content),
		}, nil
		
	case types.MessageTypeImage:
		// 这里需要实现图片消息的构造
		// 由于涉及到文件上传，暂时返回错误
		return nil, fmt.Errorf("图片消息暂未实现")
		
	case types.MessageTypeDocument:
		// 这里需要实现文档消息的构造
		return nil, fmt.Errorf("文档消息暂未实现")
		
	case types.MessageTypeAudio:
		// 这里需要实现音频消息的构造
		return nil, fmt.Errorf("音频消息暂未实现")
		
	case types.MessageTypeVideo:
		// 这里需要实现视频消息的构造
		return nil, fmt.Errorf("视频消息暂未实现")
		
	default:
		return nil, fmt.Errorf("不支持的消息类型: %s", req.MessageType)
	}
}

// validateMessageRequest 验证消息请求
func (m *Manager) validateMessageRequest(req *types.MessageRequest) error {
	if req.SessionID == "" {
		return fmt.Errorf("会话ID不能为空")
	}
	
	if req.To == "" {
		return fmt.Errorf("目标号码不能为空")
	}
	
	if req.MessageType == "" {
		return fmt.Errorf("消息类型不能为空")
	}
	
	if req.MessageType == types.MessageTypeText && req.Content == "" {
		return fmt.Errorf("文本消息内容不能为空")
	}
	
	return nil
}

// checkRateLimit 检查速率限制
func (m *Manager) checkRateLimit(sessionID string) bool {
	if !m.config.RateLimit.Enabled {
		return true
	}
	
	// 获取或创建速率限制器
	limiterInterface, _ := m.rateLimiters.LoadOrStore(sessionID, rate.NewLimiter(
		rate.Every(time.Minute/time.Duration(m.config.RateLimit.RequestsPerMinute)),
		m.config.RateLimit.BurstSize,
	))
	
	limiter := limiterInterface.(*rate.Limiter)
	return limiter.Allow()
}

// messageWorker 消息工作协程
func (m *Manager) messageWorker(workerID int) {
	defer m.wg.Done()
	
	m.logger.Debugf("消息工作协程 %d 启动", workerID)
	
	for {
		select {
		case req := <-m.messageQueue:
			m.processMessageRequest(req)
		case <-m.stopChan:
			m.logger.Debugf("消息工作协程 %d 停止", workerID)
			return
		}
	}
}

// processMessageRequest 处理消息请求
func (m *Manager) processMessageRequest(req *types.MessageRequest) {
	m.logger.Debugf("处理消息请求: %s -> %s", req.SessionID, req.To)
	
	// 发送消息
	_, err := m.SendMessage(req)
	if err != nil {
		m.logger.Errorf("发送消息失败: %v", err)
		
		// 如果需要重试
		if req.RetryCount < m.config.Retry.MaxAttempts {
			req.RetryCount++
			
			// 计算重试延迟
			delay := time.Duration(float64(m.config.Retry.InitialDelay) * 
				float64(req.RetryCount) * m.config.Retry.BackoffMultiplier)
			
			if delay > m.config.Retry.MaxDelay {
				delay = m.config.Retry.MaxDelay
			}
			
			// 延迟重试
			go func() {
				time.Sleep(delay)
				select {
				case m.messageQueue <- req:
					// 重新加入队列
				default:
					m.logger.Errorf("重试队列已满，放弃重试: %s", req.SessionID)
				}
			}()
		}
	}
}

// updateStats 更新统计信息
func (m *Manager) updateStats(success bool) {
	m.statsMutex.Lock()
	defer m.statsMutex.Unlock()
	
	m.stats.TotalMessages++
	if success {
		m.stats.SuccessfulMessages++
	} else {
		m.stats.FailedMessages++
	}
	
	// 计算成功率
	if m.stats.TotalMessages > 0 {
		m.stats.SuccessRate = float64(m.stats.SuccessfulMessages) / float64(m.stats.TotalMessages)
	}
}

// GetStats 获取统计信息
func (m *Manager) GetStats() *Stats {
	m.statsMutex.RLock()
	defer m.statsMutex.RUnlock()
	
	// 复制统计信息
	stats := &Stats{
		TotalMessages:      m.stats.TotalMessages,
		SuccessfulMessages: m.stats.SuccessfulMessages,
		FailedMessages:     m.stats.FailedMessages,
		SuccessRate:        m.stats.SuccessRate,
		QueueSize:          len(m.messageQueue),
	}
	
	return stats
}

// Stats 消息统计信息
type Stats struct {
	TotalMessages      int64   `json:"total_messages"`
	SuccessfulMessages int64   `json:"successful_messages"`
	FailedMessages     int64   `json:"failed_messages"`
	SuccessRate        float64 `json:"success_rate"`
	QueueSize          int     `json:"queue_size"`
}
