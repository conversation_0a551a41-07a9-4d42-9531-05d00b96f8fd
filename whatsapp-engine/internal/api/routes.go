package api

import (
	"fmt"
	"strconv"
	"time"

	"whatsapp-engine/internal/engine"
	"whatsapp-engine/internal/types"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

// SetupRoutes 设置路由
func SetupRoutes(r *gin.Engine, engine *engine.WhatsAppEngine) {
	// 添加 CORS 中间件
	r.Use(corsMiddleware())
	
	// API 路由组
	api := r.Group("/api")
	
	// 兼容原 Node.js 服务的接口
	api.POST("/whatsapp/action", handleWhatsAppAction(engine))
	api.POST("/whatsapp/session/:sessionId/restore", handleSessionRestore(engine))
	api.POST("/whatsapp/send-message", handleSendMessage(engine))
	api.GET("/whatsapp/sessions", handleGetSessions(engine))
	api.POST("/whatsapp/cleanup-all", handleCleanupAll(engine))
	
	// 代理管理接口
	proxy := api.Group("/proxy")
	{
		proxy.POST("/import", handleImportProxies(engine))
		proxy.POST("/groups", handleCreateProxyGroup(engine))
		proxy.GET("/status", handleGetProxyStatus(engine))
		proxy.GET("/groups", handleGetProxyGroups(engine))
		proxy.PUT("/groups/:groupId", handleUpdateProxyGroup(engine))
		proxy.DELETE("/groups/:groupId", handleDeleteProxyGroup(engine))
		proxy.GET("/health", handleGetProxyHealth(engine))
		proxy.POST("/test", handleTestProxy(engine))
	}
	
	// 会话管理接口
	session := api.Group("/session")
	{
		session.GET("/:sessionId", handleGetSessionInfo(engine))
		session.POST("/:sessionId/connect", handleConnectSession(engine))
		session.POST("/:sessionId/disconnect", handleDisconnectSession(engine))
		session.POST("/:sessionId/restart", handleRestartSession(engine))
		session.GET("/:sessionId/status", handleGetSessionStatus(engine))
		session.GET("/:sessionId/metrics", handleGetSessionMetrics(engine))
	}
	
	// 消息管理接口
	message := api.Group("/message")
	{
		message.POST("/send", handleSendMessageNew(engine))
		message.POST("/bulk", handleSendBulkMessages(engine))
		message.GET("/:sessionId/history", handleGetMessageHistory(engine))
		message.GET("/:sessionId/search", handleSearchMessages(engine))
		message.POST("/:sessionId/export", handleExportChatHistory(engine))
	}
	
	// 监控和管理接口
	monitor := api.Group("/monitor")
	{
		monitor.GET("/metrics", handleGetSystemMetrics(engine))
		monitor.GET("/stats", handleGetEngineStats(engine))
		monitor.GET("/health", handleGetEngineHealth(engine))
	}
}

// corsMiddleware CORS 中间件
func corsMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Header("Access-Control-Allow-Origin", "*")
		c.Header("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		c.Header("Access-Control-Allow-Headers", "Origin, Content-Type, Content-Length, Accept-Encoding, X-CSRF-Token, Authorization")
		
		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(204)
			return
		}
		
		c.Next()
	}
}

// 兼容原 Node.js 服务的接口处理器

// handleWhatsAppAction 处理 WhatsApp 操作
func handleWhatsAppAction(engine *engine.WhatsAppEngine) gin.HandlerFunc {
	return func(c *gin.Context) {
		var req types.NodeServiceRequest
		if err := c.ShouldBindJSON(&req); err != nil {
			errorResponse(c, 400, "Invalid request format")
			return
		}
		
		// 根据 action 类型处理
		switch req.Action {
		case "create":
			handleCreateAction(c, engine, &req)
		case "connect":
			handleConnectAction(c, engine, &req)
		case "disconnect":
			handleDisconnectAction(c, engine, &req)
		case "reconnect":
			handleReconnectAction(c, engine, &req)
		case "send_message":
			handleSendMessageAction(c, engine, &req)
		case "get_info":
			handleGetInfoAction(c, engine, &req)
		default:
			nodeErrorResponse(c, "unsupported action: "+req.Action)
		}
	}
}

// handleCreateAction 处理创建会话
func handleCreateAction(c *gin.Context, engine *engine.WhatsAppEngine, req *types.NodeServiceRequest) {
	// 分配代理
	proxyReq := &types.ProxyAllocationRequest{
		SessionID:   req.SessionID,
		TenantID:    req.TenantID,
		PhoneNumber: req.PhoneNumber,
		Sticky:      true,
	}
	
	proxy, err := engine.GetProxyManager().AllocateProxy(proxyReq)
	if err != nil {
		nodeErrorResponse(c, fmt.Sprintf("failed to allocate proxy: %v", err))
		return
	}
	
	// 创建会话
	sessionReq := &types.CreateSessionRequest{
		SessionID:   req.SessionID,
		TenantID:    req.TenantID,
		PhoneNumber: req.PhoneNumber,
		ProxyID:     proxy.ID,
		ProxyRegion: proxy.Region,
	}
	
	session, err := engine.GetSessionManager().CreateSession(sessionReq)
	if err != nil {
		nodeErrorResponse(c, fmt.Sprintf("failed to create session: %v", err))
		return
	}
	
	nodeSuccessResponse(c, "Session created successfully", map[string]interface{}{
		"session_id":   session.ID,
		"status":       session.Status,
		"proxy_id":     session.ProxyID,
		"proxy_region": session.ProxyRegion,
		"created_at":   session.CreatedAt,
	})
}

// handleConnectAction 处理连接会话
func handleConnectAction(c *gin.Context, engine *engine.WhatsAppEngine, req *types.NodeServiceRequest) {
	err := engine.GetSessionManager().ConnectSession(req.SessionID)
	if err != nil {
		nodeErrorResponse(c, fmt.Sprintf("failed to connect session: %v", err))
		return
	}
	
	session := engine.GetSessionManager().GetSession(req.SessionID)
	if session == nil {
		nodeErrorResponse(c, "session not found after connection")
		return
	}
	
	data := map[string]interface{}{
		"session_id": session.ID,
		"status":     session.Status,
	}
	
	// 如果有客户端信息，添加到响应中
	if session.ClientInfo != nil {
		data["client_info"] = session.ClientInfo
	}
	
	// 如果有二维码，添加到响应中
	if session.QRCode != "" {
		data["qr_code"] = session.QRCode
	}
	
	nodeSuccessResponse(c, "Session connected successfully", data)
}

// handleDisconnectAction 处理断开会话
func handleDisconnectAction(c *gin.Context, engine *engine.WhatsAppEngine, req *types.NodeServiceRequest) {
	err := engine.GetSessionManager().DisconnectSession(req.SessionID)
	if err != nil {
		nodeErrorResponse(c, fmt.Sprintf("failed to disconnect session: %v", err))
		return
	}
	
	nodeSuccessResponse(c, "Session disconnected successfully", map[string]interface{}{
		"session_id": req.SessionID,
		"status":     "disconnected",
	})
}

// handleReconnectAction 处理重连会话
func handleReconnectAction(c *gin.Context, engine *engine.WhatsAppEngine, req *types.NodeServiceRequest) {
	// 先断开再连接
	engine.GetSessionManager().DisconnectSession(req.SessionID)
	time.Sleep(time.Second) // 等待断开完成
	
	err := engine.GetSessionManager().ConnectSession(req.SessionID)
	if err != nil {
		nodeErrorResponse(c, fmt.Sprintf("failed to reconnect session: %v", err))
		return
	}
	
	nodeSuccessResponse(c, "Session reconnected successfully", map[string]interface{}{
		"session_id": req.SessionID,
		"status":     "connected",
	})
}

// handleSendMessageAction 处理发送消息
func handleSendMessageAction(c *gin.Context, engine *engine.WhatsAppEngine, req *types.NodeServiceRequest) {
	// 解析消息数据
	to, ok := req.Data["to"].(string)
	if !ok {
		nodeErrorResponse(c, "missing or invalid 'to' parameter")
		return
	}
	
	message, ok := req.Data["message"].(string)
	if !ok {
		nodeErrorResponse(c, "missing or invalid 'message' parameter")
		return
	}
	
	// 构造消息请求
	msgReq := &types.MessageRequest{
		SessionID:   req.SessionID,
		To:          to,
		MessageType: types.MessageTypeText,
		Content:     message,
		Priority:    types.PriorityNormal,
	}
	
	// 发送消息
	result, err := engine.GetMessageManager().SendMessage(msgReq)
	if err != nil {
		nodeErrorResponse(c, fmt.Sprintf("failed to send message: %v", err))
		return
	}
	
	nodeSuccessResponse(c, "Message sent successfully", map[string]interface{}{
		"message_id": result.MessageID,
		"status":     result.Status,
		"sent_at":    result.SentAt,
		"timestamp":  result.Timestamp,
	})
}

// handleGetInfoAction 处理获取信息
func handleGetInfoAction(c *gin.Context, engine *engine.WhatsAppEngine, req *types.NodeServiceRequest) {
	session := engine.GetSessionManager().GetSession(req.SessionID)
	if session == nil {
		nodeErrorResponse(c, "session not found")
		return
	}
	
	data := map[string]interface{}{
		"session_id":       session.ID,
		"status":           session.Status,
		"phone_number":     session.PhoneNumber,
		"proxy_id":         session.ProxyID,
		"proxy_region":     session.ProxyRegion,
		"connection_count": session.ConnectionCount,
		"message_count":    session.MessageCount,
		"last_activity":    session.LastActivity,
		"created_at":       session.CreatedAt,
	}
	
	if session.ClientInfo != nil {
		data["client_info"] = session.ClientInfo
	}
	
	nodeSuccessResponse(c, "Session info retrieved successfully", data)
}

// 响应辅助函数

// successResponse 成功响应
func successResponse(c *gin.Context, data interface{}) {
	c.JSON(200, gin.H{
		"success": true,
		"data":    data,
	})
}

// errorResponse 错误响应
func errorResponse(c *gin.Context, code int, message string) {
	c.JSON(code, gin.H{
		"success": false,
		"error":   message,
	})
}

// nodeSuccessResponse Node.js 兼容成功响应
func nodeSuccessResponse(c *gin.Context, message string, data map[string]interface{}) {
	c.JSON(200, types.NodeServiceResponse{
		Success: true,
		Message: message,
		Data:    data,
	})
}

// nodeErrorResponse Node.js 兼容错误响应
func nodeErrorResponse(c *gin.Context, error string) {
	c.JSON(200, types.NodeServiceResponse{
		Success: false,
		Error:   error,
	})
}

// 其他 API 处理函数的占位符实现

// handleSessionRestore 处理会话恢复
func handleSessionRestore(engine *engine.WhatsAppEngine) gin.HandlerFunc {
	return func(c *gin.Context) {
		sessionID := c.Param("sessionId")

		// 这里实现会话恢复逻辑
		session := engine.GetSessionManager().GetSession(sessionID)
		if session == nil {
			nodeErrorResponse(c, "session not found")
			return
		}

		nodeSuccessResponse(c, "Session restore initiated", map[string]interface{}{
			"session_id": sessionID,
			"status":     session.Status,
		})
	}
}

// handleSendMessage 处理发送消息（旧版接口）
func handleSendMessage(engine *engine.WhatsAppEngine) gin.HandlerFunc {
	return func(c *gin.Context) {
		var req struct {
			AccountID   uint   `json:"account_id"`
			PhoneNumber string `json:"phone_number"`
			MessageType string `json:"message_type"`
			Message     string `json:"message"`
			FilePath    string `json:"file_path"`
		}

		if err := c.ShouldBindJSON(&req); err != nil {
			errorResponse(c, 400, "Invalid request format")
			return
		}

		// 构造消息请求
		msgReq := &types.MessageRequest{
			SessionID:   strconv.Itoa(int(req.AccountID)),
			To:          req.PhoneNumber,
			MessageType: types.MessageType(req.MessageType),
			Content:     req.Message,
			Priority:    types.PriorityNormal,
		}

		// 发送消息
		result, err := engine.GetMessageManager().SendMessage(msgReq)
		if err != nil {
			errorResponse(c, 500, fmt.Sprintf("Failed to send message: %v", err))
			return
		}

		successResponse(c, result)
	}
}

// handleGetSessions 处理获取会话列表
func handleGetSessions(engine *engine.WhatsAppEngine) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 这里应该实现获取所有会话的逻辑
		// 由于会话管理器没有公开的列表方法，暂时返回空列表
		successResponse(c, []interface{}{})
	}
}

// handleCleanupAll 处理清理所有会话
func handleCleanupAll(engine *engine.WhatsAppEngine) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 这里实现清理所有会话的逻辑
		successResponse(c, gin.H{
			"message": "Cleanup initiated",
		})
	}
}

// 代理管理相关的占位符函数

// handleImportProxies 处理导入代理
func handleImportProxies(engine *engine.WhatsAppEngine) gin.HandlerFunc {
	return func(c *gin.Context) {
		var req struct {
			Proxies []struct {
				Host     string   `json:"host" binding:"required"`
				Port     int      `json:"port" binding:"required"`
				Type     string   `json:"type" binding:"required"`
				Username string   `json:"username"`
				Password string   `json:"password"`
				Region   string   `json:"region" binding:"required"`
				Quality  string   `json:"quality" binding:"required"`
				Tags     []string `json:"tags"`
			} `json:"proxies" binding:"required"`
			GroupID string `json:"group_id"`
		}

		if err := c.ShouldBindJSON(&req); err != nil {
			errorResponse(c, 400, "Invalid request format")
			return
		}

		imported := 0
		failed := 0

		for _, proxyData := range req.Proxies {
			proxy := &types.ProxyInfo{
				ID:       uuid.New().String(),
				GroupID:  req.GroupID,
				Host:     proxyData.Host,
				Port:     proxyData.Port,
				Type:     types.ProxyType(proxyData.Type),
				Username: proxyData.Username,
				Password: proxyData.Password,
				Region:   proxyData.Region,
				Quality:  types.ProxyQuality(proxyData.Quality),
				Status:   types.ProxyStatusActive,
				MaxConnections: 100, // 默认值
				CreatedAt: time.Now(),
			}

			if err := engine.GetProxyManager().AddProxy(proxy); err != nil {
				failed++
				continue
			}
			imported++
		}

		successResponse(c, gin.H{
			"imported": imported,
			"failed":   failed,
			"total":    len(req.Proxies),
		})
	}
}

// handleCreateProxyGroup 处理创建代理分组
func handleCreateProxyGroup(engine *engine.WhatsAppEngine) gin.HandlerFunc {
	return func(c *gin.Context) {
		var req struct {
			Name            string  `json:"name" binding:"required"`
			Region          string  `json:"region" binding:"required"`
			Quality         string  `json:"quality" binding:"required"`
			LoadBalanceType string  `json:"load_balance_type"`
			HealthThreshold float64 `json:"health_threshold"`
		}

		if err := c.ShouldBindJSON(&req); err != nil {
			errorResponse(c, 400, "Invalid request format")
			return
		}

		group := &types.ProxyGroup{
			ID:              uuid.New().String(),
			Name:            req.Name,
			Region:          req.Region,
			Quality:         types.ProxyQuality(req.Quality),
			LoadBalanceType: types.LoadBalanceType(req.LoadBalanceType),
			HealthThreshold: req.HealthThreshold,
			CreatedAt:       time.Now(),
		}

		if err := engine.GetProxyManager().CreateGroup(group); err != nil {
			errorResponse(c, 500, fmt.Sprintf("Failed to create group: %v", err))
			return
		}

		successResponse(c, group)
	}
}

// 其他处理函数的占位符实现
func handleGetProxyStatus(engine *engine.WhatsAppEngine) gin.HandlerFunc {
	return func(c *gin.Context) {
		stats := engine.GetProxyManager().GetStats()
		successResponse(c, stats)
	}
}

func handleGetProxyGroups(engine *engine.WhatsAppEngine) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 实现获取代理分组列表
		successResponse(c, []interface{}{})
	}
}

func handleUpdateProxyGroup(engine *engine.WhatsAppEngine) gin.HandlerFunc {
	return func(c *gin.Context) {
		errorResponse(c, 501, "Not implemented")
	}
}

func handleDeleteProxyGroup(engine *engine.WhatsAppEngine) gin.HandlerFunc {
	return func(c *gin.Context) {
		errorResponse(c, 501, "Not implemented")
	}
}

func handleGetProxyHealth(engine *engine.WhatsAppEngine) gin.HandlerFunc {
	return func(c *gin.Context) {
		errorResponse(c, 501, "Not implemented")
	}
}

func handleTestProxy(engine *engine.WhatsAppEngine) gin.HandlerFunc {
	return func(c *gin.Context) {
		errorResponse(c, 501, "Not implemented")
	}
}

// 会话管理相关的占位符函数
func handleGetSessionInfo(engine *engine.WhatsAppEngine) gin.HandlerFunc {
	return func(c *gin.Context) {
		sessionID := c.Param("sessionId")
		session := engine.GetSessionManager().GetSession(sessionID)
		if session == nil {
			errorResponse(c, 404, "Session not found")
			return
		}
		successResponse(c, session)
	}
}

func handleConnectSession(engine *engine.WhatsAppEngine) gin.HandlerFunc {
	return func(c *gin.Context) {
		sessionID := c.Param("sessionId")
		err := engine.GetSessionManager().ConnectSession(sessionID)
		if err != nil {
			errorResponse(c, 500, fmt.Sprintf("Failed to connect: %v", err))
			return
		}
		successResponse(c, gin.H{"status": "connected"})
	}
}

func handleDisconnectSession(engine *engine.WhatsAppEngine) gin.HandlerFunc {
	return func(c *gin.Context) {
		sessionID := c.Param("sessionId")
		err := engine.GetSessionManager().DisconnectSession(sessionID)
		if err != nil {
			errorResponse(c, 500, fmt.Sprintf("Failed to disconnect: %v", err))
			return
		}
		successResponse(c, gin.H{"status": "disconnected"})
	}
}

func handleRestartSession(engine *engine.WhatsAppEngine) gin.HandlerFunc {
	return func(c *gin.Context) {
		errorResponse(c, 501, "Not implemented")
	}
}

func handleGetSessionStatus(engine *engine.WhatsAppEngine) gin.HandlerFunc {
	return func(c *gin.Context) {
		sessionID := c.Param("sessionId")
		session := engine.GetSessionManager().GetSession(sessionID)
		if session == nil {
			errorResponse(c, 404, "Session not found")
			return
		}
		successResponse(c, gin.H{"status": session.Status})
	}
}

func handleGetSessionMetrics(engine *engine.WhatsAppEngine) gin.HandlerFunc {
	return func(c *gin.Context) {
		errorResponse(c, 501, "Not implemented")
	}
}

// 消息管理相关的占位符函数
func handleSendMessageNew(engine *engine.WhatsAppEngine) gin.HandlerFunc {
	return func(c *gin.Context) {
		var req types.MessageRequest
		if err := c.ShouldBindJSON(&req); err != nil {
			errorResponse(c, 400, "Invalid request format")
			return
		}

		result, err := engine.GetMessageManager().SendMessage(&req)
		if err != nil {
			errorResponse(c, 500, fmt.Sprintf("Failed to send message: %v", err))
			return
		}

		successResponse(c, result)
	}
}

func handleSendBulkMessages(engine *engine.WhatsAppEngine) gin.HandlerFunc {
	return func(c *gin.Context) {
		errorResponse(c, 501, "Not implemented")
	}
}

func handleGetMessageHistory(engine *engine.WhatsAppEngine) gin.HandlerFunc {
	return func(c *gin.Context) {
		errorResponse(c, 501, "Not implemented")
	}
}

func handleSearchMessages(engine *engine.WhatsAppEngine) gin.HandlerFunc {
	return func(c *gin.Context) {
		errorResponse(c, 501, "Not implemented")
	}
}

func handleExportChatHistory(engine *engine.WhatsAppEngine) gin.HandlerFunc {
	return func(c *gin.Context) {
		errorResponse(c, 501, "Not implemented")
	}
}

// 监控相关的占位符函数
func handleGetSystemMetrics(engine *engine.WhatsAppEngine) gin.HandlerFunc {
	return func(c *gin.Context) {
		errorResponse(c, 501, "Not implemented")
	}
}

func handleGetEngineStats(engine *engine.WhatsAppEngine) gin.HandlerFunc {
	return func(c *gin.Context) {
		stats := engine.GetStats()
		successResponse(c, stats)
	}
}

func handleGetEngineHealth(engine *engine.WhatsAppEngine) gin.HandlerFunc {
	return func(c *gin.Context) {
		health := engine.HealthCheck()
		successResponse(c, health)
	}
}
