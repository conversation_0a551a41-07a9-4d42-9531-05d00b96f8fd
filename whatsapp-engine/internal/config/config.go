package config

import (
	"os"
	"time"

	"gopkg.in/yaml.v3"
)

// Config 主配置结构
type Config struct {
	Server     ServerConfig     `yaml:"server"`
	Database   DatabaseConfig   `yaml:"database"`
	Cache      CacheConfig      `yaml:"cache"`
	Session    SessionConfig    `yaml:"session"`
	Proxy      ProxyConfig      `yaml:"proxy"`
	Message    MessageConfig    `yaml:"message"`
	History    HistoryConfig    `yaml:"history"`
	Monitoring MonitoringConfig `yaml:"monitoring"`
	Logging    LoggingConfig    `yaml:"logging"`
}

// ServerConfig 服务器配置
type ServerConfig struct {
	Port         string        `yaml:"port"`
	Host         string        `yaml:"host"`
	Mode         string        `yaml:"mode"`
	ReadTimeout  time.Duration `yaml:"read_timeout"`
	WriteTimeout time.Duration `yaml:"write_timeout"`
	IdleTimeout  time.Duration `yaml:"idle_timeout"`
}

// DatabaseConfig 数据库配置
type DatabaseConfig struct {
	Type            string        `yaml:"type"`
	URL             string        `yaml:"url"`
	MaxOpenConns    int           `yaml:"max_open_conns"`
	MaxIdleConns    int           `yaml:"max_idle_conns"`
	ConnMaxLifetime time.Duration `yaml:"conn_max_lifetime"`
}

// CacheConfig 缓存配置
type CacheConfig struct {
	MaxSize    int           `yaml:"max_size"`
	DefaultTTL time.Duration `yaml:"default_ttl"`
	CleanupInterval time.Duration `yaml:"cleanup_interval"`
}

// SessionConfig 会话配置
type SessionConfig struct {
	MaxSessions     int           `yaml:"max_sessions"`
	SessionTimeout  time.Duration `yaml:"session_timeout"`
	CleanupInterval time.Duration `yaml:"cleanup_interval"`
	StoragePath     string        `yaml:"storage_path"`
	AutoRestore     bool          `yaml:"auto_restore"`
}

// ProxyConfig 代理配置
type ProxyConfig struct {
	HealthCheck ProxyHealthCheckConfig `yaml:"health_check"`
	Allocation  ProxyAllocationConfig  `yaml:"allocation"`
	Failover    ProxyFailoverConfig    `yaml:"failover"`
}

// ProxyHealthCheckConfig 代理健康检查配置
type ProxyHealthCheckConfig struct {
	Interval         time.Duration `yaml:"interval"`
	Timeout          time.Duration `yaml:"timeout"`
	RetryCount       int           `yaml:"retry_count"`
	Workers          int           `yaml:"workers"`
	FailureThreshold int           `yaml:"failure_threshold"`
}

// ProxyAllocationConfig 代理分配配置
type ProxyAllocationConfig struct {
	Strategy       string        `yaml:"strategy"`
	StickySessions bool          `yaml:"sticky_sessions"`
	MaxRetries     int           `yaml:"max_retries"`
	RetryDelay     time.Duration `yaml:"retry_delay"`
}

// ProxyFailoverConfig 代理故障转移配置
type ProxyFailoverConfig struct {
	Enabled              bool          `yaml:"enabled"`
	Workers              int           `yaml:"workers"`
	UrgentThreshold      time.Duration `yaml:"urgent_threshold"`
	MaxFailoverAttempts  int           `yaml:"max_failover_attempts"`
}

// MessageConfig 消息配置
type MessageConfig struct {
	RateLimit MessageRateLimitConfig `yaml:"rate_limit"`
	Queue     MessageQueueConfig     `yaml:"queue"`
	Retry     MessageRetryConfig     `yaml:"retry"`
}

// MessageRateLimitConfig 消息速率限制配置
type MessageRateLimitConfig struct {
	Enabled           bool `yaml:"enabled"`
	RequestsPerMinute int  `yaml:"requests_per_minute"`
	BurstSize         int  `yaml:"burst_size"`
}

// MessageQueueConfig 消息队列配置
type MessageQueueConfig struct {
	Workers      int           `yaml:"workers"`
	BufferSize   int           `yaml:"buffer_size"`
	BatchSize    int           `yaml:"batch_size"`
	BatchTimeout time.Duration `yaml:"batch_timeout"`
}

// MessageRetryConfig 消息重试配置
type MessageRetryConfig struct {
	MaxAttempts        int           `yaml:"max_attempts"`
	InitialDelay       time.Duration `yaml:"initial_delay"`
	MaxDelay           time.Duration `yaml:"max_delay"`
	BackoffMultiplier  float64       `yaml:"backoff_multiplier"`
}

// HistoryConfig 历史记录配置
type HistoryConfig struct {
	AutoSyncOnConnect   bool          `yaml:"auto_sync_on_connect"`
	SyncInterval        time.Duration `yaml:"sync_interval"`
	MaxSyncMessages     int           `yaml:"max_sync_messages"`
	RetentionDays       int           `yaml:"retention_days"`
	CacheSize           int           `yaml:"cache_size"`
	CompressionEnabled  bool          `yaml:"compression_enabled"`
}

// MonitoringConfig 监控配置
type MonitoringConfig struct {
	MetricsEnabled   bool          `yaml:"metrics_enabled"`
	MetricsInterval  time.Duration `yaml:"metrics_interval"`
	AlertsEnabled    bool          `yaml:"alerts_enabled"`
	LogLevel         string        `yaml:"log_level"`
}

// LoggingConfig 日志配置
type LoggingConfig struct {
	Level      string `yaml:"level"`
	Format     string `yaml:"format"`
	Output     string `yaml:"output"`
	FilePath   string `yaml:"file_path"`
	MaxSize    int    `yaml:"max_size"`
	MaxBackups int    `yaml:"max_backups"`
	MaxAge     int    `yaml:"max_age"`
}

// Load 加载配置文件
func Load(configPath string) (*Config, error) {
	// 设置默认配置
	config := &Config{
		Server: ServerConfig{
			Port:         "3000",
			Host:         "0.0.0.0",
			Mode:         "debug",
			ReadTimeout:  30 * time.Second,
			WriteTimeout: 30 * time.Second,
			IdleTimeout:  120 * time.Second,
		},
		Database: DatabaseConfig{
			Type:            "sqlite",
			URL:             "./data/whatsapp.db",
			MaxOpenConns:    25,
			MaxIdleConns:    5,
			ConnMaxLifetime: time.Hour,
		},
		Cache: CacheConfig{
			MaxSize:         10000,
			DefaultTTL:      time.Hour,
			CleanupInterval: time.Minute,
		},
		Session: SessionConfig{
			MaxSessions:     10000,
			SessionTimeout:  24 * time.Hour,
			CleanupInterval: time.Hour,
			StoragePath:     "./sessions",
			AutoRestore:     true,
		},
		Proxy: ProxyConfig{
			HealthCheck: ProxyHealthCheckConfig{
				Interval:         30 * time.Second,
				Timeout:          10 * time.Second,
				RetryCount:       3,
				Workers:          10,
				FailureThreshold: 3,
			},
			Allocation: ProxyAllocationConfig{
				Strategy:       "region_affinity",
				StickySessions: true,
				MaxRetries:     3,
				RetryDelay:     5 * time.Second,
			},
			Failover: ProxyFailoverConfig{
				Enabled:             true,
				Workers:             5,
				UrgentThreshold:     10 * time.Second,
				MaxFailoverAttempts: 3,
			},
		},
		Message: MessageConfig{
			RateLimit: MessageRateLimitConfig{
				Enabled:           true,
				RequestsPerMinute: 60,
				BurstSize:         10,
			},
			Queue: MessageQueueConfig{
				Workers:      20,
				BufferSize:   10000,
				BatchSize:    100,
				BatchTimeout: 5 * time.Second,
			},
			Retry: MessageRetryConfig{
				MaxAttempts:       3,
				InitialDelay:      time.Second,
				MaxDelay:          30 * time.Second,
				BackoffMultiplier: 2.0,
			},
		},
		History: HistoryConfig{
			AutoSyncOnConnect:  true,
			SyncInterval:       24 * time.Hour,
			MaxSyncMessages:    10000,
			RetentionDays:      90,
			CacheSize:          1000,
			CompressionEnabled: true,
		},
		Monitoring: MonitoringConfig{
			MetricsEnabled:  true,
			MetricsInterval: 30 * time.Second,
			AlertsEnabled:   true,
			LogLevel:        "info",
		},
		Logging: LoggingConfig{
			Level:      "info",
			Format:     "json",
			Output:     "stdout",
			FilePath:   "./logs/whatsapp-engine.log",
			MaxSize:    100,
			MaxBackups: 10,
			MaxAge:     30,
		},
	}

	// 如果配置文件存在，则加载
	if _, err := os.Stat(configPath); err == nil {
		data, err := os.ReadFile(configPath)
		if err != nil {
			return nil, err
		}

		if err := yaml.Unmarshal(data, config); err != nil {
			return nil, err
		}
	}

	return config, nil
}
