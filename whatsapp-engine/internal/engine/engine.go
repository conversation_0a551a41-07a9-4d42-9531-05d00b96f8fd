package engine

import (
	"fmt"
	"sync"

	"whatsapp-engine/internal/config"
	"whatsapp-engine/internal/message"
	"whatsapp-engine/internal/proxy"
	"whatsapp-engine/internal/session"
	"whatsapp-engine/internal/storage"

	"github.com/sirupsen/logrus"
	"gorm.io/gorm"
)

// WhatsAppEngine WhatsApp 引擎
type WhatsAppEngine struct {
	config         *config.Config
	db             *gorm.DB
	cache          storage.CacheInterface
	
	// 存储层
	dbStorage      *storage.DatabaseStorage
	
	// 管理器
	proxyManager   *proxy.Manager
	sessionManager *session.Manager
	messageManager *message.Manager
	
	// 状态
	running        bool
	mutex          sync.RWMutex
	
	logger         *logrus.Logger
}

// NewWhatsAppEngine 创建 WhatsApp 引擎
func NewWhatsAppEngine(cfg *config.Config, db *gorm.DB, cache storage.CacheInterface) (*WhatsAppEngine, error) {
	engine := &WhatsAppEngine{
		config: cfg,
		db:     db,
		cache:  cache,
		logger: logrus.New(),
	}
	
	// 初始化存储层
	engine.dbStorage = storage.NewDatabaseStorage(db)
	
	// 初始化代理管理器
	proxyManager := proxy.NewManager(&cfg.Proxy, engine.dbStorage, cache)
	engine.proxyManager = proxyManager
	
	// 初始化会话管理器
	sessionManager, err := session.NewManager(&cfg.Session, db, engine.dbStorage, cache, proxyManager)
	if err != nil {
		return nil, fmt.Errorf("创建会话管理器失败: %v", err)
	}
	engine.sessionManager = sessionManager
	
	// 初始化消息管理器
	messageManager := message.NewManager(&cfg.Message, engine.dbStorage, cache, sessionManager)
	engine.messageManager = messageManager
	
	return engine, nil
}

// Start 启动引擎
func (e *WhatsAppEngine) Start() error {
	e.mutex.Lock()
	defer e.mutex.Unlock()
	
	if e.running {
		return fmt.Errorf("引擎已在运行")
	}
	
	e.logger.Info("启动 WhatsApp 引擎...")
	
	// 启动代理管理器
	if err := e.proxyManager.Start(); err != nil {
		return fmt.Errorf("启动代理管理器失败: %v", err)
	}
	
	// 启动会话管理器
	if err := e.sessionManager.Start(); err != nil {
		return fmt.Errorf("启动会话管理器失败: %v", err)
	}
	
	// 启动消息管理器
	if err := e.messageManager.Start(); err != nil {
		return fmt.Errorf("启动消息管理器失败: %v", err)
	}
	
	e.running = true
	e.logger.Info("WhatsApp 引擎启动成功")
	
	return nil
}

// Stop 停止引擎
func (e *WhatsAppEngine) Stop() error {
	e.mutex.Lock()
	defer e.mutex.Unlock()
	
	if !e.running {
		return nil
	}
	
	e.logger.Info("停止 WhatsApp 引擎...")
	
	// 停止消息管理器
	if err := e.messageManager.Stop(); err != nil {
		e.logger.Errorf("停止消息管理器失败: %v", err)
	}
	
	// 停止会话管理器
	if err := e.sessionManager.Stop(); err != nil {
		e.logger.Errorf("停止会话管理器失败: %v", err)
	}
	
	// 停止代理管理器
	if err := e.proxyManager.Stop(); err != nil {
		e.logger.Errorf("停止代理管理器失败: %v", err)
	}
	
	e.running = false
	e.logger.Info("WhatsApp 引擎已停止")
	
	return nil
}

// IsRunning 检查引擎是否运行
func (e *WhatsAppEngine) IsRunning() bool {
	e.mutex.RLock()
	defer e.mutex.RUnlock()
	return e.running
}

// GetProxyManager 获取代理管理器
func (e *WhatsAppEngine) GetProxyManager() *proxy.Manager {
	return e.proxyManager
}

// GetSessionManager 获取会话管理器
func (e *WhatsAppEngine) GetSessionManager() *session.Manager {
	return e.sessionManager
}

// GetMessageManager 获取消息管理器
func (e *WhatsAppEngine) GetMessageManager() *message.Manager {
	return e.messageManager
}

// GetStats 获取引擎统计信息
func (e *WhatsAppEngine) GetStats() *EngineStats {
	stats := &EngineStats{
		Running: e.IsRunning(),
	}
	
	// 获取代理统计
	if e.proxyManager != nil {
		stats.ProxyStats = e.proxyManager.GetStats()
	}
	
	// 获取会话统计
	if e.sessionManager != nil {
		stats.SessionStats = e.getSessionStats()
	}
	
	// 获取消息统计
	if e.messageManager != nil {
		stats.MessageStats = e.messageManager.GetStats()
	}
	
	return stats
}

// getSessionStats 获取会话统计
func (e *WhatsAppEngine) getSessionStats() *SessionStats {
	stats := &SessionStats{
		TotalSessions:  0,
		OnlineSessions: 0,
		StatusStats:    make(map[string]int),
	}
	
	// 这里需要实现会话统计逻辑
	// 由于会话管理器中的 sessions 是私有的，我们需要添加公共方法
	
	return stats
}

// HealthCheck 健康检查
func (e *WhatsAppEngine) HealthCheck() *HealthStatus {
	status := &HealthStatus{
		Status:    "healthy",
		Timestamp: fmt.Sprintf("%d", e.config.Server.ReadTimeout),
		Components: make(map[string]string),
	}
	
	if !e.IsRunning() {
		status.Status = "unhealthy"
		status.Components["engine"] = "stopped"
		return status
	}
	
	// 检查各组件状态
	status.Components["engine"] = "running"
	status.Components["proxy_manager"] = "running"
	status.Components["session_manager"] = "running"
	status.Components["message_manager"] = "running"
	
	// 检查数据库连接
	if sqlDB, err := e.db.DB(); err != nil {
		status.Status = "unhealthy"
		status.Components["database"] = "error"
	} else if err := sqlDB.Ping(); err != nil {
		status.Status = "unhealthy"
		status.Components["database"] = "unreachable"
	} else {
		status.Components["database"] = "connected"
	}
	
	// 检查缓存
	if e.cache.Size() >= 0 {
		status.Components["cache"] = "available"
	} else {
		status.Components["cache"] = "error"
	}
	
	return status
}

// EngineStats 引擎统计信息
type EngineStats struct {
	Running      bool                `json:"running"`
	ProxyStats   *proxy.ProxyStats   `json:"proxy_stats,omitempty"`
	SessionStats *SessionStats       `json:"session_stats,omitempty"`
	MessageStats *message.Stats      `json:"message_stats,omitempty"`
}

// SessionStats 会话统计信息
type SessionStats struct {
	TotalSessions  int            `json:"total_sessions"`
	OnlineSessions int            `json:"online_sessions"`
	StatusStats    map[string]int `json:"status_stats"`
}

// HealthStatus 健康状态
type HealthStatus struct {
	Status     string            `json:"status"`
	Timestamp  string            `json:"timestamp"`
	Components map[string]string `json:"components"`
}
