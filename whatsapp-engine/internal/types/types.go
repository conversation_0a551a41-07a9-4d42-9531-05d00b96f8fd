package types

import (
	"time"

	"go.mau.fi/whatsmeow"
	"go.mau.fi/whatsmeow/store"
)

// SessionStatus 会话状态
type SessionStatus string

const (
	SessionStatusInitializing SessionStatus = "initializing"
	SessionStatusConnecting   SessionStatus = "connecting"
	SessionStatusConnected    SessionStatus = "connected"
	SessionStatusDisconnected SessionStatus = "disconnected"
	SessionStatusError        SessionStatus = "error"
	SessionStatusSuspended    SessionStatus = "suspended"
)

// WhatsAppSession WhatsApp 会话
type WhatsAppSession struct {
	ID              string                 `json:"session_id" gorm:"primaryKey"`
	TenantID        uint                   `json:"tenant_id" gorm:"index"`
	PhoneNumber     string                 `json:"phone_number" gorm:"index"`
	Status          SessionStatus          `json:"status"`
	Client          *whatsmeow.Client      `json:"-" gorm:"-"`
	Device          *store.Device          `json:"-" gorm:"-"`
	ProxyID         string                 `json:"proxy_id" gorm:"index"`
	ProxyRegion     string                 `json:"proxy_region"`
	CreatedAt       time.Time              `json:"created_at"`
	UpdatedAt       time.Time              `json:"updated_at"`
	LastActivity    time.Time              `json:"last_activity"`
	ClientInfo      *ClientInfo            `json:"client_info,omitempty" gorm:"embedded"`
	ConnectionCount int                    `json:"connection_count"`
	ErrorCount      int                    `json:"error_count"`
	MessageCount    int64                  `json:"message_count"`
	QRCode          string                 `json:"qr_code,omitempty" gorm:"-"`
	Metadata        string                 `json:"metadata,omitempty"` // JSON string
}

// ClientInfo 客户端信息
type ClientInfo struct {
	WID          string    `json:"wid"`
	PhoneNumber  string    `json:"phone_number"`
	Platform     string    `json:"platform"`
	PushName     string    `json:"push_name"`
	BusinessName string    `json:"business_name"`
	IsBusiness   bool      `json:"is_business"`
	IsEnterprise bool      `json:"is_enterprise"`
	IsVerified   bool      `json:"is_verified"`
	ProfilePicURL string   `json:"profile_pic_url"`
	LastSeen     time.Time `json:"last_seen"`
}

// ProxyType 代理类型
type ProxyType string

const (
	ProxyTypeHTTP   ProxyType = "http"
	ProxyTypeSOCKS5 ProxyType = "socks5"
)

// ProxyQuality 代理质量
type ProxyQuality string

const (
	ProxyQualityHigh   ProxyQuality = "high"
	ProxyQualityMedium ProxyQuality = "medium"
	ProxyQualityLow    ProxyQuality = "low"
)

// ProxyStatus 代理状态
type ProxyStatus string

const (
	ProxyStatusActive      ProxyStatus = "active"
	ProxyStatusInactive    ProxyStatus = "inactive"
	ProxyStatusMaintenance ProxyStatus = "maintenance"
	ProxyStatusError       ProxyStatus = "error"
)

// ProxyInfo 代理信息
type ProxyInfo struct {
	ID              string                 `json:"id" gorm:"primaryKey"`
	GroupID         string                 `json:"group_id" gorm:"index"`
	Type            ProxyType              `json:"type"`
	Host            string                 `json:"host"`
	Port            int                    `json:"port"`
	Username        string                 `json:"username,omitempty"`
	Password        string                 `json:"password,omitempty"`
	Region          string                 `json:"region" gorm:"index"`
	Quality         ProxyQuality           `json:"quality" gorm:"index"`
	Status          ProxyStatus            `json:"status" gorm:"index"`
	MaxConnections  int                    `json:"max_connections"`
	CurrentLoad     int                    `json:"current_load"`
	ResponseTime    int64                  `json:"response_time"` // 毫秒
	SuccessRate     float64                `json:"success_rate"`
	LastCheck       time.Time              `json:"last_check"`
	CreatedAt       time.Time              `json:"created_at"`
	UpdatedAt       time.Time              `json:"updated_at"`
	Tags            string                 `json:"tags"` // JSON array string
	Metadata        string                 `json:"metadata,omitempty"` // JSON string
}

// LoadBalanceType 负载均衡类型
type LoadBalanceType string

const (
	LoadBalanceRoundRobin    LoadBalanceType = "round_robin"
	LoadBalanceLeastLoaded   LoadBalanceType = "least_loaded"
	LoadBalanceWeightedRound LoadBalanceType = "weighted_round"
	LoadBalanceConsistentHash LoadBalanceType = "consistent_hash"
)

// ProxyGroup 代理分组
type ProxyGroup struct {
	ID                   string          `json:"id" gorm:"primaryKey"`
	Name                 string          `json:"name"`
	Region               string          `json:"region" gorm:"index"`
	Quality              ProxyQuality    `json:"quality" gorm:"index"`
	LoadBalanceType      LoadBalanceType `json:"load_balance_type"`
	HealthThreshold      float64         `json:"health_threshold"`
	MaxProxiesPerSession int             `json:"max_proxies_per_session"`
	CreatedAt            time.Time       `json:"created_at"`
	UpdatedAt            time.Time       `json:"updated_at"`
	ProxyIDs             string          `json:"proxy_ids"` // JSON array string
}

// MessageType 消息类型
type MessageType string

const (
	MessageTypeText     MessageType = "text"
	MessageTypeImage    MessageType = "image"
	MessageTypeDocument MessageType = "document"
	MessageTypeAudio    MessageType = "audio"
	MessageTypeVideo    MessageType = "video"
	MessageTypeLocation MessageType = "location"
	MessageTypeContact  MessageType = "contact"
)

// MessagePriority 消息优先级
type MessagePriority int

const (
	PriorityLow    MessagePriority = 1
	PriorityNormal MessagePriority = 5
	PriorityHigh   MessagePriority = 8
	PriorityUrgent MessagePriority = 10
)

// MessageStatus 消息状态
type MessageStatus string

const (
	MessageStatusPending   MessageStatus = "pending"
	MessageStatusSent      MessageStatus = "sent"
	MessageStatusDelivered MessageStatus = "delivered"
	MessageStatusRead      MessageStatus = "read"
	MessageStatusFailed    MessageStatus = "failed"
)

// MessageRequest 消息请求
type MessageRequest struct {
	SessionID   string                 `json:"session_id"`
	To          string                 `json:"to"`
	MessageType MessageType            `json:"message_type"`
	Content     string                 `json:"content,omitempty"`
	MediaData   []byte                 `json:"media_data,omitempty"`
	MediaType   string                 `json:"media_type,omitempty"`
	Filename    string                 `json:"filename,omitempty"`
	Caption     string                 `json:"caption,omitempty"`
	Priority    MessagePriority        `json:"priority"`
	ScheduledAt *time.Time             `json:"scheduled_at,omitempty"`
	RetryCount  int                    `json:"retry_count"`
	Metadata    map[string]interface{} `json:"metadata,omitempty"`
}

// MessageResult 消息结果
type MessageResult struct {
	MessageID   string        `json:"message_id"`
	Status      MessageStatus `json:"status"`
	Error       string        `json:"error,omitempty"`
	SentAt      time.Time     `json:"sent_at"`
	DeliveredAt *time.Time    `json:"delivered_at,omitempty"`
	ReadAt      *time.Time    `json:"read_at,omitempty"`
	Timestamp   time.Time     `json:"timestamp"`
}

// MessageRecord 消息记录
type MessageRecord struct {
	ID          string        `json:"id" gorm:"primaryKey"`
	SessionID   string        `json:"session_id" gorm:"index"`
	ChatJID     string        `json:"chat_jid" gorm:"index"`
	MessageID   string        `json:"message_id" gorm:"index"`
	FromJID     string        `json:"from_jid"`
	ToJID       string        `json:"to_jid"`
	MessageType MessageType   `json:"message_type"`
	Content     string        `json:"content"`
	MediaURL    string        `json:"media_url,omitempty"`
	Status      MessageStatus `json:"status"`
	IsFromMe    bool          `json:"is_from_me"`
	Timestamp   time.Time     `json:"timestamp" gorm:"index"`
	CreatedAt   time.Time     `json:"created_at"`
	UpdatedAt   time.Time     `json:"updated_at"`
}

// ProxyAllocationRequest 代理分配请求
type ProxyAllocationRequest struct {
	SessionID       string                 `json:"session_id"`
	TenantID        uint                   `json:"tenant_id"`
	PhoneNumber     string                 `json:"phone_number"`
	PreferredRegion string                 `json:"preferred_region,omitempty"`
	RequiredQuality ProxyQuality           `json:"required_quality,omitempty"`
	ExcludeProxies  []string               `json:"exclude_proxies,omitempty"`
	Sticky          bool                   `json:"sticky"`
	Metadata        map[string]interface{} `json:"metadata,omitempty"`
}

// AllocationStrategy 分配策略
type AllocationStrategy string

const (
	StrategyRegionAffinity AllocationStrategy = "region_affinity"
	StrategyLoadBalance    AllocationStrategy = "load_balance"
	StrategyQualityFirst   AllocationStrategy = "quality_first"
	StrategySticky         AllocationStrategy = "sticky"
)

// NodeServiceRequest Node.js 服务兼容请求
type NodeServiceRequest struct {
	SessionID   string                 `json:"session_id"`
	PhoneNumber string                 `json:"phone_number"`
	TenantID    uint                   `json:"tenant_id"`
	Action      string                 `json:"action"`
	Data        map[string]interface{} `json:"data,omitempty"`
}

// NodeServiceResponse Node.js 服务兼容响应
type NodeServiceResponse struct {
	Success bool                   `json:"success"`
	Message string                 `json:"message"`
	Data    map[string]interface{} `json:"data,omitempty"`
	Error   string                 `json:"error,omitempty"`
}

// CreateSessionRequest 创建会话请求
type CreateSessionRequest struct {
	SessionID   string                 `json:"session_id"`
	TenantID    uint                   `json:"tenant_id"`
	PhoneNumber string                 `json:"phone_number"`
	ProxyID     string                 `json:"proxy_id"`
	ProxyRegion string                 `json:"proxy_region"`
	Config      map[string]interface{} `json:"config,omitempty"`
}

// HealthCheckResult 健康检查结果
type HealthCheckResult struct {
	ProxyID      string        `json:"proxy_id"`
	Success      bool          `json:"success"`
	ResponseTime time.Duration `json:"response_time"`
	Error        string        `json:"error,omitempty"`
	Timestamp    time.Time     `json:"timestamp"`
}
