package main

import (
	"context"
	"flag"
	"fmt"
	"log"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"whatsapp-engine/internal/api"
	"whatsapp-engine/internal/config"
	"whatsapp-engine/internal/engine"
	"whatsapp-engine/internal/storage"

	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
)

var (
	configPath = flag.String("config", "config/config.yaml", "配置文件路径")
	port       = flag.String("port", "3000", "服务端口")
)

func main() {
	flag.Parse()

	// 加载配置
	cfg, err := config.Load(*configPath)
	if err != nil {
		log.Fatalf("加载配置失败: %v", err)
	}

	// 设置日志级别
	level, err := logrus.ParseLevel(cfg.Logging.Level)
	if err != nil {
		level = logrus.InfoLevel
	}
	logrus.SetLevel(level)

	if cfg.Logging.Format == "json" {
		logrus.SetFormatter(&logrus.JSONFormatter{})
	}

	logrus.Info("启动 WhatsApp 引擎...")

	// 初始化存储
	db, err := storage.InitDatabase(cfg.Database)
	if err != nil {
		logrus.Fatalf("初始化数据库失败: %v", err)
	}

	cache, err := storage.InitCache(cfg.Cache)
	if err != nil {
		logrus.Fatalf("初始化缓存失败: %v", err)
	}

	// 初始化 WhatsApp 引擎
	waEngine, err := engine.NewWhatsAppEngine(cfg, db, cache)
	if err != nil {
		logrus.Fatalf("初始化 WhatsApp 引擎失败: %v", err)
	}

	// 启动引擎
	if err := waEngine.Start(); err != nil {
		logrus.Fatalf("启动 WhatsApp 引擎失败: %v", err)
	}

	// 设置 Gin 模式
	if cfg.Server.Mode == "release" {
		gin.SetMode(gin.ReleaseMode)
	}

	// 创建 HTTP 服务器
	router := gin.Default()

	// 设置 API 路由
	api.SetupRoutes(router, waEngine)

	// 健康检查端点
	router.GET("/health", func(c *gin.Context) {
		c.JSON(200, gin.H{
			"status":    "healthy",
			"timestamp": time.Now(),
			"version":   "1.0.0",
		})
	})

	// 创建 HTTP 服务器
	server := &http.Server{
		Addr:         fmt.Sprintf(":%s", *port),
		Handler:      router,
		ReadTimeout:  cfg.Server.ReadTimeout,
		WriteTimeout: cfg.Server.WriteTimeout,
		IdleTimeout:  cfg.Server.IdleTimeout,
	}

	// 启动服务器
	go func() {
		logrus.Infof("WhatsApp 引擎启动在端口 %s", *port)
		if err := server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			logrus.Fatalf("启动服务器失败: %v", err)
		}
	}()

	// 等待中断信号
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit

	logrus.Info("正在关闭 WhatsApp 引擎...")

	// 优雅关闭
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// 关闭 HTTP 服务器
	if err := server.Shutdown(ctx); err != nil {
		logrus.Errorf("关闭服务器失败: %v", err)
	}

	// 关闭 WhatsApp 引擎
	if err := waEngine.Stop(); err != nil {
		logrus.Errorf("关闭 WhatsApp 引擎失败: %v", err)
	}

	logrus.Info("WhatsApp 引擎已关闭")
}
