server:
  port: "3000"
  host: "0.0.0.0"
  mode: "debug"
  read_timeout: 30s
  write_timeout: 30s
  idle_timeout: 120s

database:
  type: "sqlite"
  url: "./data/whatsapp.db"
  max_open_conns: 25
  max_idle_conns: 5
  conn_max_lifetime: 1h

cache:
  max_size: 10000
  default_ttl: 1h
  cleanup_interval: 1m

session:
  max_sessions: 10000
  session_timeout: 24h
  cleanup_interval: 1h
  storage_path: "./sessions"
  auto_restore: true

proxy:
  health_check:
    interval: 30s
    timeout: 10s
    retry_count: 3
    workers: 10
    failure_threshold: 3
  
  allocation:
    strategy: "region_affinity"  # region_affinity, load_balance, quality_first
    sticky_sessions: true
    max_retries: 3
    retry_delay: 5s
  
  failover:
    enabled: true
    workers: 5
    urgent_threshold: 10s
    max_failover_attempts: 3

message:
  rate_limit:
    enabled: true
    requests_per_minute: 60
    burst_size: 10
  
  queue:
    workers: 20
    buffer_size: 10000
    batch_size: 100
    batch_timeout: 5s
  
  retry:
    max_attempts: 3
    initial_delay: 1s
    max_delay: 30s
    backoff_multiplier: 2.0

history:
  auto_sync_on_connect: true
  sync_interval: 24h
  max_sync_messages: 10000
  retention_days: 90
  cache_size: 1000
  compression_enabled: true

monitoring:
  metrics_enabled: true
  metrics_interval: 30s
  alerts_enabled: true
  log_level: "info"

logging:
  level: "info"
  format: "json"
  output: "stdout"
  file_path: "./logs/whatsapp-engine.log"
  max_size: 100
  max_backups: 10
  max_age: 30
