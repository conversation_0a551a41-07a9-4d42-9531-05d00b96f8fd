#!/bin/bash

# WhatsApp 引擎启动脚本

set -e

echo "=== WhatsApp 引擎启动脚本 ==="

# 检查 Go 环境
if ! command -v go &> /dev/null; then
    echo "错误: 未找到 Go 环境，请先安装 Go 1.21+"
    exit 1
fi

# 检查 Go 版本
GO_VERSION=$(go version | awk '{print $3}' | sed 's/go//')
REQUIRED_VERSION="1.21"

if [ "$(printf '%s\n' "$REQUIRED_VERSION" "$GO_VERSION" | sort -V | head -n1)" != "$REQUIRED_VERSION" ]; then
    echo "错误: Go 版本过低，需要 $REQUIRED_VERSION+，当前版本: $GO_VERSION"
    exit 1
fi

echo "✓ Go 版本检查通过: $GO_VERSION"

# 创建必要目录
echo "创建必要目录..."
mkdir -p data sessions logs

# 检查配置文件
if [ ! -f "config/config.yaml" ]; then
    echo "错误: 配置文件不存在: config/config.yaml"
    exit 1
fi

echo "✓ 配置文件检查通过"

# 安装依赖
echo "安装 Go 依赖..."
go mod download
go mod tidy

echo "✓ 依赖安装完成"

# 构建应用
echo "构建应用..."
CGO_ENABLED=1 go build -o bin/whatsapp-engine .

echo "✓ 构建完成"

# 启动应用
echo "启动 WhatsApp 引擎..."
echo "访问地址: http://localhost:3000"
echo "健康检查: http://localhost:3000/health"
echo "API 文档: http://localhost:3000/api"
echo ""
echo "按 Ctrl+C 停止服务"
echo ""

# 启动应用
./bin/whatsapp-engine -config config/config.yaml
