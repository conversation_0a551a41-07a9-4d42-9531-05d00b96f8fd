#!/bin/bash

# WhatsApp 引擎 API 测试脚本

BASE_URL="http://localhost:3000"

echo "=== WhatsApp 引擎 API 测试 ==="

# 1. 健康检查
echo "1. 测试健康检查..."
curl -s "$BASE_URL/health" | jq '.'
echo ""

# 2. 获取引擎状态
echo "2. 测试引擎状态..."
curl -s "$BASE_URL/api/monitor/stats" | jq '.'
echo ""

# 3. 导入测试代理
echo "3. 导入测试代理..."
curl -s -X POST "$BASE_URL/api/proxy/import" \
  -H "Content-Type: application/json" \
  -d '{
    "proxies": [
      {
        "host": "127.0.0.1",
        "port": 8080,
        "type": "http",
        "region": "local",
        "quality": "high"
      }
    ],
    "group_id": "test-group"
  }' | jq '.'
echo ""

# 4. 获取代理状态
echo "4. 获取代理状态..."
curl -s "$BASE_URL/api/proxy/status" | jq '.'
echo ""

# 5. 创建测试会话
echo "5. 创建测试会话..."
curl -s -X POST "$BASE_URL/api/whatsapp/action" \
  -H "Content-Type: application/json" \
  -d '{
    "session_id": "test-session-1",
    "phone_number": "**********",
    "tenant_id": 1,
    "action": "create"
  }' | jq '.'
echo ""

# 6. 获取会话信息
echo "6. 获取会话信息..."
curl -s "$BASE_URL/api/session/test-session-1" | jq '.'
echo ""

# 7. 连接会话
echo "7. 连接会话..."
curl -s -X POST "$BASE_URL/api/whatsapp/action" \
  -H "Content-Type: application/json" \
  -d '{
    "session_id": "test-session-1",
    "phone_number": "**********",
    "tenant_id": 1,
    "action": "connect"
  }' | jq '.'
echo ""

# 8. 发送测试消息
echo "8. 发送测试消息..."
curl -s -X POST "$BASE_URL/api/whatsapp/action" \
  -H "Content-Type: application/json" \
  -d '{
    "session_id": "test-session-1",
    "phone_number": "**********",
    "tenant_id": 1,
    "action": "send_message",
    "data": {
      "to": "**********",
      "message": "Hello from WhatsApp Engine!"
    }
  }' | jq '.'
echo ""

# 9. 断开会话
echo "9. 断开会话..."
curl -s -X POST "$BASE_URL/api/whatsapp/action" \
  -H "Content-Type: application/json" \
  -d '{
    "session_id": "test-session-1",
    "phone_number": "**********",
    "tenant_id": 1,
    "action": "disconnect"
  }' | jq '.'
echo ""

echo "=== 测试完成 ==="
