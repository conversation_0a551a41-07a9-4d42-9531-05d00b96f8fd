# WhatsApp 引擎 Makefile

.PHONY: help build run test clean docker-build docker-run docker-stop deps fmt lint

# 默认目标
help:
	@echo "WhatsApp 引擎构建工具"
	@echo ""
	@echo "可用命令:"
	@echo "  build        构建二进制文件"
	@echo "  run          运行应用"
	@echo "  test         运行测试"
	@echo "  clean        清理构建文件"
	@echo "  docker-build 构建 Docker 镜像"
	@echo "  docker-run   运行 Docker 容器"
	@echo "  docker-stop  停止 Docker 容器"
	@echo "  deps         安装依赖"
	@echo "  fmt          格式化代码"
	@echo "  lint         代码检查"

# 构建二进制文件
build:
	@echo "构建 WhatsApp 引擎..."
	@mkdir -p bin
	@CGO_ENABLED=1 go build -o bin/whatsapp-engine .
	@echo "构建完成: bin/whatsapp-engine"

# 运行应用
run:
	@echo "启动 WhatsApp 引擎..."
	@mkdir -p data sessions logs
	@go run main.go -config config/config.yaml

# 运行测试
test:
	@echo "运行测试..."
	@go test -v ./...

# 清理构建文件
clean:
	@echo "清理构建文件..."
	@rm -rf bin/
	@rm -rf data/
	@rm -rf sessions/
	@rm -rf logs/
	@echo "清理完成"

# 构建 Docker 镜像
docker-build:
	@echo "构建 Docker 镜像..."
	@docker build -t whatsapp-engine:latest .
	@echo "Docker 镜像构建完成"

# 运行 Docker 容器
docker-run:
	@echo "启动 Docker 容器..."
	@docker-compose up -d
	@echo "Docker 容器已启动"

# 停止 Docker 容器
docker-stop:
	@echo "停止 Docker 容器..."
	@docker-compose down
	@echo "Docker 容器已停止"

# 安装依赖
deps:
	@echo "安装 Go 依赖..."
	@go mod download
	@go mod tidy
	@echo "依赖安装完成"

# 格式化代码
fmt:
	@echo "格式化代码..."
	@go fmt ./...
	@echo "代码格式化完成"

# 代码检查
lint:
	@echo "运行代码检查..."
	@go vet ./...
	@echo "代码检查完成"

# 创建必要目录
dirs:
	@mkdir -p data sessions logs config

# 开发环境设置
dev-setup: dirs deps
	@echo "开发环境设置完成"

# 生产环境构建
prod-build:
	@echo "生产环境构建..."
	@CGO_ENABLED=1 GOOS=linux go build -a -ldflags '-extldflags "-static"' -o bin/whatsapp-engine .
	@echo "生产环境构建完成"

# 运行 API 测试
test-api:
	@echo "运行 API 测试..."
	@chmod +x test/test_api.sh
	@./test/test_api.sh

# 查看日志
logs:
	@docker-compose logs -f whatsapp-engine

# 重启服务
restart: docker-stop docker-run

# 完整测试流程
full-test: build test test-api

# 部署
deploy: docker-build docker-run
	@echo "部署完成"
