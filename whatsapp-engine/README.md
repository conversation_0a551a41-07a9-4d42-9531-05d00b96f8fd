# WhatsApp 引擎

基于 whatsmeow 的高性能 WhatsApp 引擎，支持大规模账号管理、智能代理网络、消息发送等功能。

## 🚀 特性

- **高性能**: 基于 Go 和 whatsmeow，资源消耗极低
- **智能代理管理**: 支持代理分组、健康检查、自动故障转移
- **大规模会话管理**: 单节点支持数万个 WhatsApp 账号
- **消息管理**: 支持文本、图片、文档等多种消息类型
- **兼容性**: 与现有 Node.js 服务接口完全兼容
- **监控完善**: 实时监控和健康检查
- **易部署**: Docker 容器化，一键部署

## 📦 快速开始

### 使用 Docker Compose（推荐）

```bash
# 克隆项目
git clone <repository-url>
cd whatsapp-engine

# 启动服务
docker-compose up -d

# 查看日志
docker-compose logs -f
```

### 本地开发

```bash
# 安装依赖
go mod download

# 创建必要目录
mkdir -p data sessions logs

# 启动服务
go run main.go -config config/config.yaml
```

## 🔧 配置

主要配置文件位于 `config/config.yaml`，包含以下配置项：

- **服务器配置**: 端口、超时等
- **数据库配置**: SQLite 数据库路径
- **缓存配置**: 内存缓存设置
- **会话配置**: 最大会话数、超时时间等
- **代理配置**: 健康检查、分配策略、故障转移
- **消息配置**: 速率限制、队列、重试策略
- **日志配置**: 日志级别、格式等

## 📡 API 接口

### 兼容接口（与 Node.js 服务兼容）

#### 创建会话
```bash
POST /api/whatsapp/action
{
  "session_id": "test-session-1",
  "phone_number": "**********",
  "tenant_id": 1,
  "action": "create"
}
```

#### 连接会话
```bash
POST /api/whatsapp/action
{
  "session_id": "test-session-1",
  "action": "connect"
}
```

#### 发送消息
```bash
POST /api/whatsapp/action
{
  "session_id": "test-session-1",
  "action": "send_message",
  "data": {
    "to": "**********",
    "message": "Hello World!"
  }
}
```

### 代理管理接口

#### 导入代理
```bash
POST /api/proxy/import
{
  "proxies": [
    {
      "host": "proxy.example.com",
      "port": 8080,
      "type": "http",
      "username": "user",
      "password": "pass",
      "region": "us",
      "quality": "high"
    }
  ]
}
```

#### 获取代理状态
```bash
GET /api/proxy/status
```

### 监控接口

#### 健康检查
```bash
GET /health
```

#### 引擎统计
```bash
GET /api/monitor/stats
```

## 🧪 测试

运行 API 测试脚本：

```bash
chmod +x test/test_api.sh
./test/test_api.sh
```

## 📊 性能指标

| 指标 | 单节点容量 |
|------|-----------|
| 最大会话数 | 10,000+ |
| 内存使用 | 2-8GB |
| CPU 使用 | 1-4 核 |
| 消息吞吐量 | 1000+ msg/s |

## 🏗️ 架构设计

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   API 接口层    │    │   会话管理器    │    │   代理管理器    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   消息管理器    │    │   存储层        │    │   监控系统      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### 核心组件

1. **代理管理器**: 智能代理分配、健康检查、故障转移
2. **会话管理器**: WhatsApp 会话生命周期管理
3. **消息管理器**: 消息发送、队列、重试机制
4. **存储层**: SQLite 数据库 + 内存缓存
5. **API 层**: REST API 接口，兼容现有系统

## 🔍 故障排除

### 常见问题

1. **会话连接失败**
   - 检查代理配置是否正确
   - 确认网络连接正常
   - 查看日志获取详细错误信息

2. **代理健康检查失败**
   - 验证代理服务器是否可访问
   - 检查代理认证信息
   - 调整健康检查超时时间

3. **消息发送失败**
   - 确认会话已连接
   - 检查目标号码格式
   - 验证消息内容是否符合要求

### 日志查看

```bash
# Docker 环境
docker-compose logs -f whatsapp-engine

# 本地环境
tail -f logs/whatsapp-engine.log
```

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

## 📄 许可证

MIT License
