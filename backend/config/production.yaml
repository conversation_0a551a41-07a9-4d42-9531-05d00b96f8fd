server:
  port: "8081"
  host: "0.0.0.0"
  mode: "release"
  read_timeout: 60s
  write_timeout: 60s
  idle_timeout: 300s

database:
  type: "sqlite"
  url: "../storage/database/hive.db"
  max_open_conns: 50
  max_idle_conns: 10
  conn_max_lifetime: 2h

cache:
  max_size: 50000
  default_ttl: 2h
  cleanup_interval: 5m

session:
  max_sessions: 50000
  session_timeout: 48h
  cleanup_interval: 30m
  storage_path: "./sessions"
  auto_restore: true

proxy:
  health_check:
    interval: 60s
    timeout: 30s
    retry_count: 3
    workers: 20
    failure_threshold: 5
  
  allocation:
    strategy: "region_affinity"  # region_affinity, load_balance, quality_first
    sticky_sessions: true
    max_retries: 5
    retry_delay: 10s
  
  failover:
    enabled: true
    workers: 10
    urgent_threshold: 30s
    max_failover_attempts: 5

message:
  rate_limit:
    enabled: true
    requests_per_minute: 120
    burst_size: 20
  
  queue:
    workers: 50
    buffer_size: 50000
    batch_size: 200
    batch_timeout: 10s
  
  retry:
    max_attempts: 5
    initial_delay: 2s
    max_delay: 60s
    backoff_multiplier: 2.0

history:
  auto_sync_on_connect: true
  sync_interval: 12h
  max_sync_messages: 50000
  retention_days: 180
  cache_size: 5000
  compression_enabled: true

monitoring:
  metrics_enabled: true
  metrics_interval: 60s
  alerts_enabled: true
  log_level: "info"

logging:
  level: "info"
  format: "json"
  output: "file"
  file_path: "./logs/whatsapp-engine-production.log"
  max_size: 500
  max_backups: 30
  max_age: 90
