#!/bin/bash

# WhatsApp 引擎生产环境监控脚本

BASE_URL="http://localhost:8081"
TOKEN=""
ALERT_WEBHOOK=""  # 可以设置为 Slack/Discord webhook
LOG_FILE="./logs/monitor.log"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 日志函数
log_with_timestamp() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$LOG_FILE"
}

log_info() {
    log_with_timestamp "[INFO] $1"
}

log_warning() {
    log_with_timestamp "[WARNING] $1"
}

log_error() {
    log_with_timestamp "[ERROR] $1"
}

log_success() {
    log_with_timestamp "[SUCCESS] $1"
}

# 发送告警
send_alert() {
    local message="$1"
    local level="$2"
    
    log_error "$message"
    
    if [ -n "$ALERT_WEBHOOK" ]; then
        curl -s -X POST "$ALERT_WEBHOOK" \
            -H "Content-Type: application/json" \
            -d "{\"text\": \"🚨 WhatsApp Engine Alert [$level]: $message\"}" > /dev/null
    fi
}

# 获取访问令牌
get_token() {
    TOKEN=$(curl -s -X POST "$BASE_URL/api/auth/login" \
        -H "Content-Type: application/json" \
        -d '{"username": "admin", "password": "admin123"}' | \
        jq -r '.data.token // empty')
    
    if [ -z "$TOKEN" ]; then
        send_alert "无法获取访问令牌" "CRITICAL"
        return 1
    fi
    
    return 0
}

# 检查服务健康状态
check_service_health() {
    local response=$(curl -s -w "%{http_code}" "$BASE_URL/health")
    local http_code="${response: -3}"
    
    if [ "$http_code" != "200" ]; then
        send_alert "服务健康检查失败，HTTP状态码: $http_code" "CRITICAL"
        return 1
    fi
    
    log_success "服务健康检查通过"
    return 0
}

# 检查引擎统计
check_engine_stats() {
    if [ -z "$TOKEN" ]; then
        get_token || return 1
    fi
    
    local stats=$(curl -s "$BASE_URL/api/whatsapp/engine/stats" \
        -H "Authorization: Bearer $TOKEN")
    
    if [ $? -ne 0 ]; then
        send_alert "无法获取引擎统计信息" "HIGH"
        return 1
    fi
    
    # 解析统计信息
    local running=$(echo "$stats" | jq -r '.data.running // false')
    local total_sessions=$(echo "$stats" | jq -r '.data.session_stats.total_sessions // 0')
    local online_sessions=$(echo "$stats" | jq -r '.data.session_stats.online_sessions // 0')
    local total_proxies=$(echo "$stats" | jq -r '.data.proxy_stats.total_proxies // 0')
    local active_proxies=$(echo "$stats" | jq -r '.data.proxy_stats.active_proxies // 0')
    local failed_proxies=$(echo "$stats" | jq -r '.data.proxy_stats.failed_proxies // 0')
    local message_success_rate=$(echo "$stats" | jq -r '.data.message_stats.success_rate // 0')
    local queue_size=$(echo "$stats" | jq -r '.data.message_stats.queue_size // 0')
    
    # 检查引擎运行状态
    if [ "$running" != "true" ]; then
        send_alert "WhatsApp 引擎未运行" "CRITICAL"
        return 1
    fi
    
    # 检查代理状态
    if [ "$total_proxies" -gt 0 ]; then
        local proxy_failure_rate=$(echo "scale=2; $failed_proxies * 100 / $total_proxies" | bc -l)
        if (( $(echo "$proxy_failure_rate > 50" | bc -l) )); then
            send_alert "代理失败率过高: ${proxy_failure_rate}%" "HIGH"
        fi
    fi
    
    # 检查消息成功率
    if (( $(echo "$message_success_rate < 0.8" | bc -l) )); then
        send_alert "消息成功率过低: $(echo "$message_success_rate * 100" | bc -l)%" "MEDIUM"
    fi
    
    # 检查消息队列
    if [ "$queue_size" -gt 10000 ]; then
        send_alert "消息队列积压严重: $queue_size 条消息" "HIGH"
    fi
    
    # 记录正常状态
    log_info "引擎统计 - 运行: $running, 会话: $online_sessions/$total_sessions, 代理: $active_proxies/$total_proxies, 消息成功率: $(echo "$message_success_rate * 100" | bc -l)%, 队列: $queue_size"
    
    return 0
}

# 检查系统资源
check_system_resources() {
    # 检查内存使用
    local pid=$(pgrep hive-backend || echo "")
    local memory_usage=""
    local cpu_usage=""

    if [ -n "$pid" ]; then
        memory_usage=$(ps -o %mem -p "$pid" | tail -n +2 | tr -d ' ')
        cpu_usage=$(ps -o %cpu -p "$pid" | tail -n +2 | tr -d ' ')

        if [ -n "$memory_usage" ] && (( $(echo "$memory_usage > 80" | bc -l 2>/dev/null || echo 0) )); then
            send_alert "内存使用率过高: ${memory_usage}%" "MEDIUM"
        fi

        if [ -n "$cpu_usage" ] && (( $(echo "$cpu_usage > 90" | bc -l 2>/dev/null || echo 0) )); then
            send_alert "CPU使用率过高: ${cpu_usage}%" "MEDIUM"
        fi
    else
        memory_usage="N/A"
        cpu_usage="N/A"
    fi
    
    # 检查磁盘空间
    local disk_usage=$(df -h . | tail -1 | awk '{print $5}' | sed 's/%//')
    if [ "$disk_usage" -gt 85 ]; then
        send_alert "磁盘空间不足: ${disk_usage}%" "HIGH"
    fi
    
    log_info "系统资源 - 内存: ${memory_usage}%, CPU: ${cpu_usage}%, 磁盘: ${disk_usage}%"
}

# 检查日志错误
check_logs() {
    local log_file="./logs/whatsapp-engine-production.log"
    
    if [ -f "$log_file" ]; then
        # 检查最近5分钟的错误日志
        local recent_errors=$(tail -n 1000 "$log_file" | grep -c "ERROR\|FATAL" || true)
        
        if [ "$recent_errors" -gt 10 ]; then
            send_alert "最近检测到 $recent_errors 个错误日志" "MEDIUM"
        fi
        
        # 检查特定错误模式
        local connection_errors=$(tail -n 1000 "$log_file" | grep -c "connection.*failed\|timeout\|refused" || true)
        if [ "$connection_errors" -gt 5 ]; then
            send_alert "检测到 $connection_errors 个连接错误" "HIGH"
        fi
    fi
}

# 性能基准测试
performance_benchmark() {
    if [ -z "$TOKEN" ]; then
        get_token || return 1
    fi
    
    log_info "开始性能基准测试..."
    
    # 测试API响应时间
    local start_time=$(date +%s%N)
    curl -s "$BASE_URL/api/whatsapp/engine/stats" \
        -H "Authorization: Bearer $TOKEN" > /dev/null
    local end_time=$(date +%s%N)
    
    local response_time=$(( (end_time - start_time) / 1000000 ))  # 转换为毫秒
    
    if [ "$response_time" -gt 5000 ]; then  # 5秒
        send_alert "API响应时间过长: ${response_time}ms" "MEDIUM"
    fi
    
    log_info "API响应时间: ${response_time}ms"
}

# 生成监控报告
generate_report() {
    local report_file="./logs/monitor_report_$(date +%Y%m%d_%H%M%S).json"
    
    if [ -z "$TOKEN" ]; then
        get_token || return 1
    fi
    
    # 获取详细统计
    local stats=$(curl -s "$BASE_URL/api/whatsapp/engine/stats" \
        -H "Authorization: Bearer $TOKEN")
    
    # 获取系统信息
    local system_info=$(cat << EOF
{
    "timestamp": "$(date -Iseconds)",
    "system": {
        "memory_usage": "$(ps -o pid,ppid,cmd,%mem,%cpu -p $(pgrep hive-backend) | tail -n +2 | awk '{print $4}' || echo '0')",
        "cpu_usage": "$(ps -o pid,ppid,cmd,%mem,%cpu -p $(pgrep hive-backend) | tail -n +2 | awk '{print $5}' || echo '0')",
        "disk_usage": "$(df -h . | tail -1 | awk '{print $5}' | sed 's/%//' || echo '0')",
        "uptime": "$(uptime | awk '{print $3,$4}' | sed 's/,//')"
    },
    "engine_stats": $stats
}
EOF
)
    
    echo "$system_info" | jq . > "$report_file"
    log_info "监控报告已生成: $report_file"
}

# 主监控循环
monitor_loop() {
    log_info "开始监控 WhatsApp 引擎..."
    
    while true; do
        echo "=== 监控检查 $(date) ==="
        
        # 基础健康检查
        if check_service_health; then
            # 详细检查
            check_engine_stats
            check_system_resources
            check_logs
            performance_benchmark
        else
            log_error "服务不可用，跳过详细检查"
        fi
        
        echo ""
        sleep 60  # 每分钟检查一次
    done
}

# 一次性检查
single_check() {
    log_info "执行一次性监控检查..."
    
    check_service_health
    check_engine_stats
    check_system_resources
    check_logs
    performance_benchmark
    generate_report
    
    log_success "监控检查完成"
}

# 显示帮助
show_help() {
    echo "WhatsApp 引擎生产环境监控脚本"
    echo ""
    echo "使用方法:"
    echo "  $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --help       显示帮助信息"
    echo "  -l, --loop       持续监控模式"
    echo "  -c, --check      一次性检查"
    echo "  -r, --report     生成监控报告"
    echo "  -w, --webhook    设置告警webhook"
    echo "  -u, --url        设置服务器地址"
    echo ""
    echo "示例:"
    echo "  $0 --loop                    # 持续监控"
    echo "  $0 --check                   # 一次性检查"
    echo "  $0 --webhook https://...     # 设置告警"
    echo ""
}

# 创建日志目录
mkdir -p ./logs

# 解析命令行参数
case "${1:-}" in
    -h|--help)
        show_help
        exit 0
        ;;
    -l|--loop)
        monitor_loop
        ;;
    -c|--check)
        single_check
        ;;
    -r|--report)
        generate_report
        ;;
    -w|--webhook)
        ALERT_WEBHOOK="$2"
        single_check
        ;;
    -u|--url)
        BASE_URL="$2"
        single_check
        ;;
    "")
        single_check
        ;;
    *)
        echo "未知参数: $1"
        show_help
        exit 1
        ;;
esac
