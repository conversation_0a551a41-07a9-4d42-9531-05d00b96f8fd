package main

import (
	"fmt"
	"log"
	"net/http"
	"sync"
	"time"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
	"gorm.io/driver/sqlite"
)

// User模型现在在models.go中定义

// 数据库实例
var db *gorm.DB

// 数据库文件路径
var dbFile string = "../storage/database/hive.db"

// 清理任务互斥锁
var cleanupMutex sync.Mutex
var isCleanupRunning bool

// 初始化数据库
func initDatabase() error {
	var err error
	db, err = gorm.Open(sqlite.Open(dbFile+"?_journal_mode=WAL&_timeout=5000&_busy_timeout=5000"), &gorm.Config{})
	if err != nil {
		return fmt.Errorf("failed to connect to database: %v", err)
	}

	// 自动迁移数据库
	err = db.AutoMigrate(
		&Tenant{},
		&Role{},
		&Permission{},
		&UserRole{},
		&User{},
		&CustomerServiceProfile{},
		&CustomerServiceGroup{},
		&SystemConfig{},
		&TenantSwitchHistory{},
		&WhatsAppGroup{},
		&WhatsAppSession{},
		&WhatsAppAccount{},
		&SessionCleanupLog{},
		&NodeServiceStatus{},
		&GroupSendingTask{},
		&GroupSendingStatement{},
		&GroupSendingLog{},
		&WhatsAppSendHistory{},
	)
	if err != nil {
		return fmt.Errorf("failed to migrate database: %v", err)
	}

	// 初始化租户系统
	err = initTenantSystem()
	if err != nil {
		return fmt.Errorf("failed to initialize tenant system: %v", err)
	}

	// 初始化权限系统
	err = initPermissionSystem()
	if err != nil {
		return fmt.Errorf("failed to initialize permission system: %v", err)
	}

	// 初始化日志系统
	err = initLogSystem()
	if err != nil {
		return fmt.Errorf("failed to initialize log system: %v", err)
	}

	// 初始化系统配置
	err = initSystemConfig()
	if err != nil {
		return fmt.Errorf("failed to initialize system config: %v", err)
	}

	// 初始化租户切换历史系统
	err = initTenantSwitchHistory()
	if err != nil {
		return fmt.Errorf("failed to initialize tenant switch history: %v", err)
	}

	// 初始化文件上传系统
	err = initUploadSystem()
	if err != nil {
		return fmt.Errorf("failed to initialize upload system: %v", err)
	}

	// 初始化测试用户
	err = initTestUsers()
	if err != nil {
		return fmt.Errorf("failed to initialize test users: %v", err)
	}

	// 初始化WhatsApp系统
	err = initWhatsAppSystem()
	if err != nil {
		return fmt.Errorf("failed to initialize WhatsApp system: %v", err)
	}

	// 初始化群发任务系统
	err = initGroupSendingSystem()
	if err != nil {
		return fmt.Errorf("failed to initialize group sending system: %v", err)
	}

	log.Println("数据库文件: hive.db")
	log.Println("数据库连接成功")
	return nil
}

// CORS中间件
func corsMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Header("Access-Control-Allow-Origin", "*")
		c.Header("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		c.Header("Access-Control-Allow-Headers", "Content-Type, Authorization")

		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(204)
			return
		}

		c.Next()
	}
}

// 日志中间件
func loggerMiddleware() gin.HandlerFunc {
	return gin.LoggerWithFormatter(func(param gin.LogFormatterParams) string {
		return fmt.Sprintf("%s - [%s] \"%s %s %s %d %s \"%s\" %s\"\n",
			param.ClientIP,
			param.TimeStamp.Format("2006/01/02 - 15:04:05"),
			param.Method,
			param.Path,
			param.Request.Proto,
			param.StatusCode,
			param.Latency,
			param.Request.UserAgent(),
			param.ErrorMessage,
		)
	})
}

// API响应格式
type ApiResponse struct {
	Code    int         `json:"code"`
	Message string      `json:"message"`
	Data    interface{} `json:"data"`
}

// 成功响应
func successResponse(c *gin.Context, data interface{}) {
	c.JSON(http.StatusOK, ApiResponse{
		Code:    200,
		Message: "success",
		Data:    data,
	})
}

// 错误响应
func errorResponse(c *gin.Context, code int, message string) {
	c.JSON(code, ApiResponse{
		Code:    code,
		Message: message,
		Data:    nil,
	})
}

func main() {
	// 设置端口
	port := ":8081"
	
	// 初始化数据库
	if err := initDatabase(); err != nil {
		log.Fatal("数据库初始化失败:", err)
	}

	// 初始化 WhatsApp 引擎 (暂时禁用)
	// if err := InitWhatsAppEngine(db); err != nil {
	// 	log.Fatal("WhatsApp 引擎初始化失败:", err)
	// }

	// 启动定时清理任务
	go startCleanupTask()

	// 创建Gin引擎
	r := gin.Default()

	// 添加CORS中间件
	r.Use(corsMiddleware())

	// 健康检查
	r.GET("/health", func(c *gin.Context) {
		c.Status(204)
	})

	// API测试
	r.GET("/api/test", func(c *gin.Context) {
		c.JSON(200, gin.H{
			"message": "API服务正常运行",
			"time":    time.Now().Format("2006-01-02 15:04:05"),
		})
	})

	// 公开API（不需要认证）
	public := r.Group("/api")
	{
		public.POST("/auth/login", login)
		public.POST("/auth/register", register)
		public.POST("/auth/logout", logout)
		public.GET("/config/public", getPublicSystemConfigs)
		
		// 内部服务通信API（不需要认证）
		public.PUT("/whatsapp/sessions/:session_id/status", updateSessionStatus)
		public.GET("/whatsapp/sessions/internal", getAllSessionsStatusInternal) // Node服务获取Session状态
		public.POST("/whatsapp/node-service/restart", handleNodeServiceRestart) // Node服务重启通知
		public.POST("/whatsapp/node-service/heartbeat", handleNodeServiceHeartbeat) // Node服务心跳
		public.DELETE("/whatsapp/sessions/:session_id/cleanup", handleNodeSessionCleanup) // Node服务清理通知（不需要认证）
	}

	// 需要认证的API
	api := r.Group("/api")
	api.Use(authMiddleware())
	{
		// 用户相关
		api.GET("/me", getCurrentUser)
		api.PUT("/me", updateProfile)
		api.POST("/change-password", changePassword)
		api.GET("/my-permissions", getUserPermissionsAPI)

		// 文件上传
		api.POST("/files/upload", uploadFile)
		api.GET("/files", getFileList)
		api.GET("/files/stats", getFileStats)
		api.DELETE("/files/:id", deleteFile)
		api.GET("/uploads/:filename", serveFile)

		// 系统监控
		api.GET("/system/monitor", getSystemMonitor)

		// 租户相关
		api.GET("/tenant/current", getCurrentTenant)
		api.GET("/tenant/accessible", getAccessibleTenants)
		api.GET("/tenant/switch-history", getTenantSwitchHistory)
		api.GET("/tenant/all-switch-history", getAllTenantSwitchHistory)
		api.GET("/tenant/current-with-history", getCurrentTenantWithHistory)
		api.POST("/tenant/switch", switchTenant)

		// 系统信息
		api.GET("/system/info", getSystemInfo)

		// 导出功能
		api.GET("/export/users", exportUsersCSV)
		api.GET("/export/roles", exportRolesCSV)
		api.GET("/export/permissions", exportPermissionsCSV)

		// 导入功能
		api.POST("/import/users", importUsersCSV)
		api.POST("/import/roles", importRolesCSV)
		api.POST("/import/permissions", importPermissionsCSV)

		// WhatsApp相关
		whatsapp := api.Group("/whatsapp")
		{
			// 基础CRUD API
			whatsapp.GET("/accounts", getWhatsAppAccountList)
			whatsapp.GET("/accounts/:id", getWhatsAppAccountById)
			whatsapp.POST("/accounts", createWhatsAppAccount)
			whatsapp.PUT("/accounts/:id", updateWhatsAppAccount)
			whatsapp.DELETE("/accounts/:id", deleteWhatsAppAccount)
			whatsapp.GET("/accounts/:id/qr-code", getWhatsAppAccountQRCode)
			whatsapp.POST("/accounts/:id/connect", connectWhatsAppAccount)
			whatsapp.POST("/accounts/:id/disconnect", disconnectWhatsAppAccount)

			// 分组管理
			whatsapp.GET("/groups", getWhatsAppGroupList)
			whatsapp.GET("/groups/:id", getWhatsAppGroupById)
			whatsapp.POST("/groups", createWhatsAppGroup)
			whatsapp.PUT("/groups/:id", updateWhatsAppGroup)
			whatsapp.DELETE("/groups/:id", deleteWhatsAppGroup)
			whatsapp.GET("/groups/:id/accounts", getGroupAccounts)
			whatsapp.POST("/groups/:id/move-accounts", moveAccountsToGroup)
			whatsapp.POST("/groups/reorder", reorderGroups)

			// 其他功能
			whatsapp.GET("/accounts/qr-code", getQRCodeForLogin)
			whatsapp.GET("/accounts/:id/login-status", getAccountLoginStatus)
			whatsapp.PUT("/accounts/:id/status", updateAccountStatus)
			whatsapp.GET("/accounts/:id/avatar", getAccountAvatar)

			// Node服务集成API
			whatsapp.POST("/sessions", createWhatsAppSession)
			whatsapp.GET("/sessions", getAllSessionsStatus) // 获取所有session状态
			whatsapp.GET("/sessions/:session_id/status", checkSessionStatus)
			whatsapp.POST("/accounts/from-session", createWhatsAppAccountFromSession)

			// WhatsApp 引擎 API（函数接口）- 暂时禁用
			// whatsapp.POST("/engine/action", createWhatsAppSessionEngine)
			// whatsapp.POST("/engine/send-message", sendWhatsAppMessageEngine)
			// whatsapp.GET("/engine/stats", getWhatsAppEngineStatsAPI)
			// whatsapp.POST("/engine/proxies/import", importProxiesAPI)

			// 发送消息API
			whatsapp.POST("/accounts/:id/send-message", sendWhatsAppMessageAPI)
			whatsapp.GET("/accounts/:id/send-history", getSendHistoryAPI)
			whatsapp.POST("/sessions/cleanup", cleanupExpiredSessions)
			whatsapp.POST("/accounts/:id/connect-session", connectWhatsAppAccountNode)
			whatsapp.POST("/accounts/:id/disconnect-session", disconnectWhatsAppAccountNode)
			whatsapp.POST("/accounts/:id/reconnect-session", reconnectWhatsAppAccountNode)
			whatsapp.GET("/accounts/:id/info", getWhatsAppAccountInfo)
			whatsapp.POST("/accounts/:id/cleanup", cleanupWhatsAppSession)
			whatsapp.GET("/node-service/status", getNodeServiceStatus) // 获取Node服务状态

		}

		// 群发任务相关
		groupSending := api.Group("/group-sending")
		{
			// 基础CRUD API
			groupSending.GET("/tasks", getGroupSendingTasksAPI)
			groupSending.GET("/tasks/:id", getGroupSendingTaskDetailAPI)
			groupSending.POST("/tasks", createGroupSendingTaskAPI)
			groupSending.PUT("/tasks/:id", updateGroupSendingTaskAPI)
			
			// 任务控制API
			groupSending.POST("/tasks/:id/start", startGroupSendingTaskAPI)
			groupSending.POST("/tasks/:id/pause", pauseGroupSendingTaskAPI)
			groupSending.POST("/tasks/:id/stop", stopGroupSendingTaskAPI)
			groupSending.DELETE("/tasks/:id", deleteGroupSendingTaskAPI)
			groupSending.POST("/tasks/:id/restart", restartGroupSendingTaskAPI)
			
			// 批量操作API
			groupSending.POST("/tasks/batch/start", batchStartGroupSendingTasksAPI)
			groupSending.POST("/tasks/batch/pause", batchPauseGroupSendingTasksAPI)
			groupSending.POST("/tasks/batch/terminate", batchTerminateGroupSendingTasksAPI)
			groupSending.POST("/tasks/batch/restart", batchRestartGroupSendingTasksAPI)
			groupSending.POST("/tasks/batch/delete", batchDeleteGroupSendingTasksAPI)
		}

		// 管理员API
		admin := api.Group("/admin")
		admin.Use(adminMiddleware())
		{
			// 用户管理
			admin.GET("/users", getUserList)
			admin.GET("/users/:id", getUserById)
			admin.POST("/users", createUser)
			admin.PUT("/users/:id", updateUser)
			admin.DELETE("/users/:id", deleteUser)
			admin.POST("/users/:id/roles", assignUserRoles)

			// 角色管理
			admin.GET("/roles", getRoleList)
			admin.GET("/roles/:id", getRoleById)
			admin.POST("/roles", createRole)
			admin.PUT("/roles/:id", updateRole)
			admin.DELETE("/roles/:id", deleteRole)

			// 权限管理
			admin.GET("/permissions", getPermissionList)
			admin.POST("/permissions/assign", assignRolePermissions)

			// 操作日志
			admin.GET("/logs", getOperationLogs)
			admin.GET("/logs/:id", getOperationLogById)
			admin.DELETE("/logs/:id", deleteOperationLog)
			admin.POST("/logs/batch-delete", batchDeleteOperationLogs)
			admin.DELETE("/logs/clear-old", clearOldOperationLogs)
			admin.GET("/logs/stats", getOperationLogStats)

			// 租户管理
			admin.GET("/tenants", getTenantList)
			admin.GET("/tenants/:id", getTenantById)
			admin.POST("/tenants", createTenant)
			admin.PUT("/tenants/:id", updateTenant)
			admin.DELETE("/tenants/:id", deleteTenant)

			// 系统配置
			admin.GET("/config", getSystemConfigs)
			admin.GET("/config/:key", getSystemConfigByKey)
			admin.PUT("/config/:key", updateSystemConfig)
			admin.POST("/config/batch", batchUpdateSystemConfigs)
			admin.POST("/config", createSystemConfig)
			admin.DELETE("/config/:key", deleteSystemConfig)
			admin.POST("/config/:key/reset", resetSystemConfigToDefault)
			
			// WhatsApp数据修复
			admin.POST("/whatsapp/accounts/fix-orphaned", fixOrphanedAccounts)

			// 客服管理
			admin.GET("/customer-services", getCustomerServiceList)
			admin.GET("/customer-services/:id", getCustomerServiceById)
			admin.POST("/customer-services", createCustomerService)
			admin.PUT("/customer-services/:id", updateCustomerService)
			admin.DELETE("/customer-services/:id", deleteCustomerService)
			admin.POST("/customer-services/batch", batchUpdateCustomerServices)

			// 客服分组管理
			admin.GET("/customer-service-groups", getCustomerServiceGroupList)
			admin.GET("/customer-service-groups/:id", getCustomerServiceGroupById)
			admin.POST("/customer-service-groups", createCustomerServiceGroup)
			admin.PUT("/customer-service-groups/:id", updateCustomerServiceGroup)
			admin.DELETE("/customer-service-groups/:id", deleteCustomerServiceGroup)
			admin.POST("/customer-service-groups/batch", batchUpdateCustomerServiceGroups)
			admin.GET("/customer-service-groups/:id/services", getGroupCustomerServices)
		}
	}

	fmt.Printf("启动服务器在端口 :%s\n", port)
	fmt.Printf("数据库文件: %s\n", dbFile)
	fmt.Printf("健康检查: http://localhost%s/health\n", port)
	fmt.Printf("API测试: http://localhost%s/api/test\n", port)

	// 启动定时清理任务
	go startCleanupTask()

	r.Run(port)
}

// 启动定时清理任务
func startCleanupTask() {
	ticker := time.NewTicker(5 * time.Minute) // 每5分钟执行一次
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			go cleanupExpiredSessionsTask() // 使用goroutine避免阻塞
		}
	}
}

// 定时清理过期Session任务
func cleanupExpiredSessionsTask() {
	// 使用互斥锁防止重复执行
	cleanupMutex.Lock()
	defer cleanupMutex.Unlock()
	
	if isCleanupRunning {
		fmt.Println("清理任务已在运行中，跳过本次执行")
		return
	}
	
	isCleanupRunning = true
	defer func() {
		isCleanupRunning = false
	}()
	
	fmt.Println("开始执行定时清理任务...")
	
	// 查找过期的Session - 增加更严格的条件
	var expiredSessions []WhatsAppSession
	now := time.Now()
	
	// 只清理真正过期的session，增加时间缓冲
	expiredTime := now.Add(-30 * time.Minute) // 30分钟缓冲
	
	result := db.Where("(expires_at < ? AND expires_at IS NOT NULL) OR status = 'expired'", expiredTime).Find(&expiredSessions)
	if result.Error != nil {
		fmt.Printf("查询过期Session失败: %v\n", result.Error)
		return
	}

	if len(expiredSessions) == 0 {
		fmt.Println("没有找到过期的Session")
		return
	}

	fmt.Printf("找到 %d 个过期Session，开始清理...\n", len(expiredSessions))

	// 清理过期Session
	var cleanedCount int64
	for _, session := range expiredSessions {
		// 额外检查：确保session确实过期且没有活跃的账号关联
		var accountCount int64
		db.Model(&WhatsAppAccount{}).Where("session_id = ? AND (status = 'connected' OR connection_status = 'connected')", session.SessionID).Count(&accountCount)
		
		if accountCount > 0 {
			fmt.Printf("跳过Session %s：有活跃账号关联\n", session.SessionID)
			continue
		}
		
		// 调用Node服务清理Session
		nodeReq := NodeServiceRequest{
			SessionID:   session.SessionID,
			PhoneNumber: session.PhoneNumber,
			TenantID:    session.TenantID,
			Action:      "cleanup",
		}
		
		_, err := callNodeService(nodeReq)
		if err != nil {
			fmt.Printf("清理Session失败: %s, 错误: %v\n", session.SessionID, err)
			continue
		}
		
		// 更新关联的Account记录
		var accounts []WhatsAppAccount
		if err := db.Where("session_id = ?", session.SessionID).Find(&accounts).Error; err == nil {
			for _, account := range accounts {
				// 找到关联的Account，更新其session相关字段
				updates := map[string]interface{}{
					"session_storage_path": "",
					"session_created_at":   nil,
					"session_last_used":    nil,
					"session_file_size":    0,
					"session_file_count":   0,
					"is_session_valid":     false,
					"status":               "disconnected",
					"connection_status":    "disconnected",
				}
				
				// 先清空session_id，避免UNIQUE约束冲突
				db.Model(&account).Update("session_id", "")
				// 然后更新其他字段
				db.Model(&account).Updates(updates)
				fmt.Printf("已更新关联Account: %s\n", account.AccountName)
			}
		}
		
		// 删除Session记录
		db.Delete(&session)
		cleanedCount++
	}

	fmt.Printf("定时清理完成，成功清理 %d 个过期Session\n", cleanedCount)
} 