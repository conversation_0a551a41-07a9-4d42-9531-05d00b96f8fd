package main

import (
	"fmt"
	"net/http"
	"strconv"
	"time"

	"hive/backend/internal/types"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

// WhatsApp 引擎 API 处理器

// createWhatsAppSessionEngine 使用引擎创建 WhatsApp 会话
func createWhatsAppSessionEngine(c *gin.Context) {
	var req struct {
		SessionID   string `json:"session_id" binding:"required"`
		PhoneNumber string `json:"phone_number"`
		TenantID    uint   `json:"tenant_id"`
		Action      string `json:"action" binding:"required"`
		Data        map[string]interface{} `json:"data"`
	}

	if err := c.ShouldBindJ<PERSON>(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "Invalid request format: " + err.Error(),
		})
		return
	}

	switch req.Action {
	case "create":
		if req.PhoneNumber == "" || req.TenantID == 0 {
			c.J<PERSON>N(http.StatusBadRequest, gin.H{
				"success": false,
				"error":   "phone_number and tenant_id are required for create action",
			})
			return
		}
		handleCreateSessionEngine(c, req.SessionID, req.TenantID, req.PhoneNumber)
	case "connect":
		handleConnectSessionEngine(c, req.SessionID)
	case "disconnect":
		handleDisconnectSessionEngine(c, req.SessionID)
	case "send_message":
		handleSendMessageEngineWithData(c, req.SessionID, req.Data)
	case "get_info":
		handleGetSessionInfoEngine(c, req.SessionID)
	default:
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "Unsupported action: " + req.Action,
		})
	}
}

// handleCreateSessionEngine 处理创建会话
func handleCreateSessionEngine(c *gin.Context, sessionID string, tenantID uint, phoneNumber string) {
	// 创建会话
	session, err := CreateWhatsAppSession(sessionID, tenantID, phoneNumber)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"error":   fmt.Sprintf("Failed to create session: %v", err),
		})
		return
	}

	// 更新数据库中的会话记录
	var dbSession WhatsAppSession
	result := db.Where("session_id = ?", sessionID).First(&dbSession)
	if result.Error != nil {
		// 创建新的数据库记录
		dbSession = WhatsAppSession{
			SessionID:   sessionID,
			PhoneNumber: phoneNumber,
			TenantID:    tenantID,
			Status:      string(session.Status),
			CreatedAt:   time.Now(),
			UpdatedAt:   time.Now(),
		}
		db.Create(&dbSession)
	} else {
		// 更新现有记录
		dbSession.Status = string(session.Status)
		dbSession.UpdatedAt = time.Now()
		db.Save(&dbSession)
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Session created successfully",
		"data": gin.H{
			"session_id":   session.ID,
			"status":       session.Status,
			"proxy_id":     session.ProxyID,
			"proxy_region": session.ProxyRegion,
			"created_at":   session.CreatedAt,
		},
	})
}

// handleConnectSessionEngine 处理连接会话
func handleConnectSessionEngine(c *gin.Context, sessionID string) {
	err := ConnectWhatsAppSession(sessionID)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"error":   fmt.Sprintf("Failed to connect session: %v", err),
		})
		return
	}

	// 更新数据库状态
	var dbSession WhatsAppSession
	if result := db.Where("session_id = ?", sessionID).First(&dbSession); result.Error == nil {
		dbSession.Status = "connecting"
		dbSession.UpdatedAt = time.Now()
		db.Save(&dbSession)
	}

	session := GetWhatsAppSession(sessionID)
	data := gin.H{
		"session_id": sessionID,
		"status":     "connecting",
	}

	if session != nil {
		data["status"] = session.Status
		if session.ClientInfo != nil {
			data["client_info"] = session.ClientInfo
		}
		if session.QRCode != "" {
			data["qr_code"] = session.QRCode
		}
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Session connection initiated",
		"data":    data,
	})
}

// handleDisconnectSessionEngine 处理断开会话
func handleDisconnectSessionEngine(c *gin.Context, sessionID string) {
	err := DisconnectWhatsAppSession(sessionID)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"error":   fmt.Sprintf("Failed to disconnect session: %v", err),
		})
		return
	}

	// 更新数据库状态
	var dbSession WhatsAppSession
	if result := db.Where("session_id = ?", sessionID).First(&dbSession); result.Error == nil {
		dbSession.Status = "disconnected"
		dbSession.UpdatedAt = time.Now()
		db.Save(&dbSession)
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Session disconnected successfully",
		"data": gin.H{
			"session_id": sessionID,
			"status":     "disconnected",
		},
	})
}

// handleSendMessageEngineWithData 处理发送消息（使用传入的数据）
func handleSendMessageEngineWithData(c *gin.Context, sessionID string, data map[string]interface{}) {
	if data == nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "Missing data field",
		})
		return
	}

	to, ok := data["to"].(string)
	if !ok {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "Missing or invalid 'to' parameter",
		})
		return
	}

	message, ok := data["message"].(string)
	if !ok {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "Missing or invalid 'message' parameter",
		})
		return
	}

	// 发送消息
	result, err := SendWhatsAppMessage(sessionID, to, message)
	if err != nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"error":   fmt.Sprintf("Failed to send message: %v", err),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Message sent successfully",
		"data": gin.H{
			"message_id": result.MessageID,
			"status":     result.Status,
			"sent_at":    result.SentAt,
			"timestamp":  result.Timestamp,
		},
	})
}

// handleGetSessionInfoEngine 处理获取会话信息
func handleGetSessionInfoEngine(c *gin.Context, sessionID string) {
	session := GetWhatsAppSession(sessionID)
	if session == nil {
		c.JSON(http.StatusOK, gin.H{
			"success": false,
			"error":   "Session not found",
		})
		return
	}

	data := gin.H{
		"session_id":       session.ID,
		"status":           session.Status,
		"phone_number":     session.PhoneNumber,
		"proxy_id":         session.ProxyID,
		"proxy_region":     session.ProxyRegion,
		"connection_count": session.ConnectionCount,
		"message_count":    session.MessageCount,
		"last_activity":    session.LastActivity,
		"created_at":       session.CreatedAt,
	}

	if session.ClientInfo != nil {
		data["client_info"] = session.ClientInfo
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Session info retrieved successfully",
		"data":    data,
	})
}

// sendWhatsAppMessageEngine 发送 WhatsApp 消息（新接口）
func sendWhatsAppMessageEngine(c *gin.Context) {
	var req struct {
		AccountID   uint   `json:"account_id" binding:"required"`
		PhoneNumber string `json:"phone_number" binding:"required"`
		MessageType string `json:"message_type"`
		Message     string `json:"message" binding:"required"`
		FilePath    string `json:"file_path"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "Invalid request format: " + err.Error(),
		})
		return
	}

	sessionID := strconv.Itoa(int(req.AccountID))

	// 发送消息
	result, err := SendWhatsAppMessage(sessionID, req.PhoneNumber, req.Message)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   fmt.Sprintf("Failed to send message: %v", err),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    result,
	})
}

// getWhatsAppEngineStatsAPI 获取引擎统计信息
func getWhatsAppEngineStatsAPI(c *gin.Context) {
	stats := GetWhatsAppEngineStats()
	if stats == nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "Engine not initialized",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    stats,
	})
}

// importProxiesAPI 导入代理
func importProxiesAPI(c *gin.Context) {
	var req struct {
		Proxies []struct {
			Host     string   `json:"host" binding:"required"`
			Port     int      `json:"port" binding:"required"`
			Type     string   `json:"type" binding:"required"`
			Username string   `json:"username"`
			Password string   `json:"password"`
			Region   string   `json:"region" binding:"required"`
			Quality  string   `json:"quality" binding:"required"`
			Tags     []string `json:"tags"`
		} `json:"proxies" binding:"required"`
		GroupID string `json:"group_id"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "Invalid request format: " + err.Error(),
		})
		return
	}

	var proxies []*types.ProxyInfo
	for _, proxyData := range req.Proxies {
		proxy := &types.ProxyInfo{
			ID:             uuid.New().String(),
			GroupID:        req.GroupID,
			Host:           proxyData.Host,
			Port:           proxyData.Port,
			Type:           types.ProxyType(proxyData.Type),
			Username:       proxyData.Username,
			Password:       proxyData.Password,
			Region:         proxyData.Region,
			Quality:        types.ProxyQuality(proxyData.Quality),
			Status:         types.ProxyStatusActive,
			MaxConnections: 100,
			CreatedAt:      time.Now(),
		}
		proxies = append(proxies, proxy)
	}

	imported, failed, err := ImportProxies(proxies)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   fmt.Sprintf("Failed to import proxies: %v", err),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"imported": imported,
			"failed":   failed,
			"total":    len(req.Proxies),
		},
	})
}
