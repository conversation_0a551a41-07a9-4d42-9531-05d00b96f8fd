#!/bin/bash

# 真实 WhatsApp 会话测试脚本

set -e

BASE_URL="http://localhost:8081"
TOKEN=""
SESSION_ID="real-session-$(date +%s)"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 获取用户输入
get_user_input() {
    echo "=== 真实 WhatsApp 会话测试 ==="
    echo ""
    
    read -p "请输入您的手机号 (格式: +86xxxxxxxxxx): " PHONE_NUMBER
    if [ -z "$PHONE_NUMBER" ]; then
        log_error "手机号不能为空"
        exit 1
    fi
    
    echo ""
    echo "会话ID: $SESSION_ID"
    echo "手机号: $PHONE_NUMBER"
    echo ""
    
    read -p "确认开始测试? (y/N): " confirm
    if [ "$confirm" != "y" ] && [ "$confirm" != "Y" ]; then
        log_info "测试已取消"
        exit 0
    fi
}

# 登录获取 token
login() {
    log_info "登录获取访问令牌..."
    TOKEN=$(curl -s -X POST "$BASE_URL/api/auth/login" \
        -H "Content-Type: application/json" \
        -d '{"username": "admin", "password": "admin123"}' | \
        jq -r '.data.token')
    
    if [ "$TOKEN" != "null" ] && [ -n "$TOKEN" ]; then
        log_success "登录成功"
    else
        log_error "登录失败"
        exit 1
    fi
}

# 创建会话
create_session() {
    log_info "创建 WhatsApp 会话..."
    
    RESULT=$(curl -s -X POST "$BASE_URL/api/whatsapp/engine/action" \
        -H "Authorization: Bearer $TOKEN" \
        -H "Content-Type: application/json" \
        -d "{
            \"session_id\": \"$SESSION_ID\",
            \"phone_number\": \"$PHONE_NUMBER\",
            \"tenant_id\": 1,
            \"action\": \"create\"
        }")
    
    echo "$RESULT" | jq .
    
    if echo "$RESULT" | jq -e '.success' > /dev/null; then
        log_success "会话创建成功"
        return 0
    else
        log_error "会话创建失败"
        return 1
    fi
}

# 连接会话
connect_session() {
    log_info "连接 WhatsApp 会话..."
    log_warning "这个过程可能需要30秒，请耐心等待..."
    
    # 启动连接
    curl -s -X POST "$BASE_URL/api/whatsapp/engine/action" \
        -H "Authorization: Bearer $TOKEN" \
        -H "Content-Type: application/json" \
        -d "{
            \"session_id\": \"$SESSION_ID\",
            \"action\": \"connect\"
        }" > /dev/null &
    
    # 等待二维码生成
    sleep 3
    
    log_info "正在生成二维码..."
}

# 获取二维码
get_qr_code() {
    log_info "获取二维码..."
    
    for i in {1..10}; do
        RESULT=$(curl -s -X POST "$BASE_URL/api/whatsapp/engine/action" \
            -H "Authorization: Bearer $TOKEN" \
            -H "Content-Type: application/json" \
            -d "{
                \"session_id\": \"$SESSION_ID\",
                \"action\": \"get_info\"
            }")
        
        QR_CODE=$(echo "$RESULT" | jq -r '.data.qr_code // empty')
        STATUS=$(echo "$RESULT" | jq -r '.data.status')
        
        if [ -n "$QR_CODE" ] && [ "$QR_CODE" != "null" ]; then
            log_success "二维码已生成！"
            echo ""
            echo "二维码内容:"
            echo "$QR_CODE"
            echo ""
            
            # 如果有 qrencode，显示二维码
            if command -v qrencode > /dev/null; then
                echo "二维码图形:"
                echo "$QR_CODE" | qrencode -t UTF8
                echo ""
            else
                log_info "安装 qrencode 可以显示图形二维码: brew install qrencode"
            fi
            
            log_warning "请使用 WhatsApp 扫描上面的二维码"
            return 0
        fi
        
        log_info "等待二维码生成... ($i/10) 状态: $STATUS"
        sleep 2
    done
    
    log_error "二维码生成超时"
    return 1
}

# 等待连接
wait_for_connection() {
    log_info "等待 WhatsApp 连接..."
    log_warning "请在手机上扫描二维码并确认登录"
    
    for i in {1..60}; do
        RESULT=$(curl -s -X POST "$BASE_URL/api/whatsapp/engine/action" \
            -H "Authorization: Bearer $TOKEN" \
            -H "Content-Type: application/json" \
            -d "{
                \"session_id\": \"$SESSION_ID\",
                \"action\": \"get_info\"
            }")
        
        STATUS=$(echo "$RESULT" | jq -r '.data.status')
        
        case "$STATUS" in
            "connected")
                log_success "WhatsApp 连接成功！"
                
                # 显示客户端信息
                CLIENT_INFO=$(echo "$RESULT" | jq -r '.data.client_info // empty')
                if [ -n "$CLIENT_INFO" ] && [ "$CLIENT_INFO" != "null" ]; then
                    echo ""
                    echo "客户端信息:"
                    echo "$CLIENT_INFO" | jq .
                fi
                
                return 0
                ;;
            "connecting")
                log_info "连接中... ($i/60)"
                ;;
            "error")
                log_error "连接失败"
                echo "$RESULT" | jq .
                return 1
                ;;
            *)
                log_info "状态: $STATUS ($i/60)"
                ;;
        esac
        
        sleep 2
    done
    
    log_error "连接超时"
    return 1
}

# 发送测试消息
send_test_message() {
    echo ""
    read -p "是否发送测试消息? (y/N): " send_msg
    if [ "$send_msg" != "y" ] && [ "$send_msg" != "Y" ]; then
        log_info "跳过消息发送测试"
        return 0
    fi
    
    read -p "请输入目标手机号 (格式: +86xxxxxxxxxx): " TARGET_NUMBER
    if [ -z "$TARGET_NUMBER" ]; then
        log_warning "未输入目标号码，跳过消息发送"
        return 0
    fi
    
    read -p "请输入消息内容 (默认: Hello from WhatsApp Engine!): " MESSAGE
    if [ -z "$MESSAGE" ]; then
        MESSAGE="Hello from WhatsApp Engine! 测试时间: $(date)"
    fi
    
    log_info "发送消息到 $TARGET_NUMBER..."
    
    RESULT=$(curl -s -X POST "$BASE_URL/api/whatsapp/engine/action" \
        -H "Authorization: Bearer $TOKEN" \
        -H "Content-Type: application/json" \
        -d "{
            \"session_id\": \"$SESSION_ID\",
            \"action\": \"send_message\",
            \"data\": {
                \"to\": \"$TARGET_NUMBER\",
                \"message\": \"$MESSAGE\"
            }
        }")
    
    echo "$RESULT" | jq .
    
    if echo "$RESULT" | jq -e '.success' > /dev/null; then
        log_success "消息发送成功！"
    else
        log_error "消息发送失败"
    fi
}

# 保持会话活跃
keep_session_alive() {
    echo ""
    read -p "是否保持会话活跃以便继续测试? (y/N): " keep_alive
    if [ "$keep_alive" != "y" ] && [ "$keep_alive" != "Y" ]; then
        return 0
    fi
    
    log_info "会话将保持活跃，按 Ctrl+C 退出"
    log_info "会话ID: $SESSION_ID"
    
    while true; do
        # 每30秒检查一次状态
        RESULT=$(curl -s -X POST "$BASE_URL/api/whatsapp/engine/action" \
            -H "Authorization: Bearer $TOKEN" \
            -H "Content-Type: application/json" \
            -d "{
                \"session_id\": \"$SESSION_ID\",
                \"action\": \"get_info\"
            }")
        
        STATUS=$(echo "$RESULT" | jq -r '.data.status')
        MESSAGE_COUNT=$(echo "$RESULT" | jq -r '.data.message_count')
        
        log_info "$(date '+%H:%M:%S') - 状态: $STATUS, 消息数: $MESSAGE_COUNT"
        
        if [ "$STATUS" != "connected" ]; then
            log_warning "会话连接丢失，状态: $STATUS"
            break
        fi
        
        sleep 30
    done
}

# 清理会话
cleanup_session() {
    echo ""
    read -p "是否断开会话? (Y/n): " disconnect
    if [ "$disconnect" = "n" ] || [ "$disconnect" = "N" ]; then
        log_info "会话保持连接"
        log_info "会话ID: $SESSION_ID"
        log_info "可以稍后使用以下命令断开:"
        echo "curl -X POST '$BASE_URL/api/whatsapp/engine/action' \\"
        echo "  -H 'Authorization: Bearer $TOKEN' \\"
        echo "  -H 'Content-Type: application/json' \\"
        echo "  -d '{\"session_id\": \"$SESSION_ID\", \"action\": \"disconnect\"}'"
        return 0
    fi
    
    log_info "断开会话..."
    
    curl -s -X POST "$BASE_URL/api/whatsapp/engine/action" \
        -H "Authorization: Bearer $TOKEN" \
        -H "Content-Type: application/json" \
        -d "{
            \"session_id\": \"$SESSION_ID\",
            \"action\": \"disconnect\"
        }" > /dev/null
    
    log_success "会话已断开"
}

# 主测试流程
main() {
    # 检查服务
    if ! curl -s "$BASE_URL/health" > /dev/null; then
        log_error "服务未运行，请先启动后端服务"
        exit 1
    fi
    
    # 获取用户输入
    get_user_input
    
    # 登录
    login
    
    # 创建会话
    if ! create_session; then
        exit 1
    fi
    
    # 连接会话
    connect_session
    
    # 获取二维码
    if get_qr_code; then
        # 等待连接
        if wait_for_connection; then
            # 发送测试消息
            send_test_message
            
            # 保持会话活跃
            keep_session_alive
        fi
    fi
    
    # 清理
    cleanup_session
    
    echo ""
    log_success "真实 WhatsApp 测试完成！"
}

# 信号处理
trap 'echo ""; log_info "收到中断信号，正在清理..."; cleanup_session; exit 0' INT

# 检查依赖
if ! command -v curl > /dev/null; then
    log_error "需要安装 curl"
    exit 1
fi

if ! command -v jq > /dev/null; then
    log_error "需要安装 jq"
    exit 1
fi

# 运行测试
main
