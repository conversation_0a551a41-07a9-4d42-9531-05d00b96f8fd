#!/bin/bash

# WhatsApp 引擎性能测试脚本

set -e

BASE_URL="http://localhost:8081"
TOKEN=""
CONCURRENT_SESSIONS=10
TEST_DURATION=300  # 5分钟
RESULTS_DIR="./performance_results"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 创建结果目录
setup_results_dir() {
    mkdir -p "$RESULTS_DIR"
    TIMESTAMP=$(date +%Y%m%d_%H%M%S)
    TEST_ID="perf_test_$TIMESTAMP"
    RESULTS_FILE="$RESULTS_DIR/${TEST_ID}.json"
    LOG_FILE="$RESULTS_DIR/${TEST_ID}.log"
    
    log_info "测试ID: $TEST_ID"
    log_info "结果文件: $RESULTS_FILE"
}

# 获取访问令牌
get_token() {
    log_info "获取访问令牌..."
    TOKEN=$(curl -s -X POST "$BASE_URL/api/auth/login" \
        -H "Content-Type: application/json" \
        -d '{"username": "admin", "password": "admin123"}' | \
        jq -r '.data.token')
    
    if [ "$TOKEN" != "null" ] && [ -n "$TOKEN" ]; then
        log_success "令牌获取成功"
    else
        log_error "令牌获取失败"
        exit 1
    fi
}

# 基准测试 - API 响应时间
benchmark_api_response() {
    log_info "测试 API 响应时间..."
    
    local total_time=0
    local success_count=0
    local error_count=0
    local iterations=100
    
    for i in $(seq 1 $iterations); do
        local start_time=$(date +%s%N)
        
        if curl -s "$BASE_URL/api/whatsapp/engine/stats" \
            -H "Authorization: Bearer $TOKEN" > /dev/null; then
            success_count=$((success_count + 1))
        else
            error_count=$((error_count + 1))
        fi
        
        local end_time=$(date +%s%N)
        local response_time=$(( (end_time - start_time) / 1000000 ))
        total_time=$((total_time + response_time))
        
        if [ $((i % 10)) -eq 0 ]; then
            log_info "API测试进度: $i/$iterations"
        fi
    done
    
    local avg_response_time=$((total_time / iterations))
    local success_rate=$(echo "scale=2; $success_count * 100 / $iterations" | bc -l)
    
    echo "{
        \"api_response_test\": {
            \"iterations\": $iterations,
            \"avg_response_time_ms\": $avg_response_time,
            \"success_count\": $success_count,
            \"error_count\": $error_count,
            \"success_rate\": $success_rate
        }
    }" > "$RESULTS_FILE"
    
    log_success "API响应时间测试完成"
    log_info "平均响应时间: ${avg_response_time}ms"
    log_info "成功率: ${success_rate}%"
}

# 并发会话创建测试
concurrent_session_test() {
    log_info "开始并发会话创建测试..."
    log_info "并发数: $CONCURRENT_SESSIONS"
    
    local pids=()
    local start_time=$(date +%s)
    
    # 启动并发会话创建
    for i in $(seq 1 $CONCURRENT_SESSIONS); do
        {
            local session_id="perf_test_${TEST_ID}_${i}"
            local phone_number="perf${i}"
            
            # 创建会话
            local create_start=$(date +%s%N)
            local create_result=$(curl -s -X POST "$BASE_URL/api/whatsapp/engine/action" \
                -H "Authorization: Bearer $TOKEN" \
                -H "Content-Type: application/json" \
                -d "{
                    \"session_id\": \"$session_id\",
                    \"phone_number\": \"$phone_number\",
                    \"tenant_id\": 1,
                    \"action\": \"create\"
                }")
            local create_end=$(date +%s%N)
            local create_time=$(( (create_end - create_start) / 1000000 ))
            
            local create_success=$(echo "$create_result" | jq -r '.success')
            
            # 记录结果
            echo "{
                \"session_id\": \"$session_id\",
                \"create_time_ms\": $create_time,
                \"create_success\": $create_success,
                \"timestamp\": $(date +%s)
            }" >> "$RESULTS_DIR/session_${i}.json"
            
        } &
        pids+=($!)
    done
    
    # 等待所有会话创建完成
    log_info "等待所有会话创建完成..."
    for pid in "${pids[@]}"; do
        wait $pid
    done
    
    local end_time=$(date +%s)
    local total_time=$((end_time - start_time))
    
    # 统计结果
    local success_count=0
    local total_create_time=0
    
    for i in $(seq 1 $CONCURRENT_SESSIONS); do
        if [ -f "$RESULTS_DIR/session_${i}.json" ]; then
            local success=$(jq -r '.create_success' "$RESULTS_DIR/session_${i}.json")
            local create_time=$(jq -r '.create_time_ms' "$RESULTS_DIR/session_${i}.json")
            
            if [ "$success" = "true" ]; then
                success_count=$((success_count + 1))
            fi
            
            total_create_time=$((total_create_time + create_time))
        fi
    done
    
    local avg_create_time=$((total_create_time / CONCURRENT_SESSIONS))
    local success_rate=$(echo "scale=2; $success_count * 100 / $CONCURRENT_SESSIONS" | bc -l)
    
    # 更新结果文件
    local current_results=$(cat "$RESULTS_FILE")
    echo "$current_results" | jq ". + {
        \"concurrent_session_test\": {
            \"concurrent_sessions\": $CONCURRENT_SESSIONS,
            \"total_time_seconds\": $total_time,
            \"avg_create_time_ms\": $avg_create_time,
            \"success_count\": $success_count,
            \"success_rate\": $success_rate
        }
    }" > "$RESULTS_FILE"
    
    log_success "并发会话测试完成"
    log_info "总耗时: ${total_time}秒"
    log_info "平均创建时间: ${avg_create_time}ms"
    log_info "成功率: ${success_rate}%"
    
    # 清理测试会话文件
    rm -f "$RESULTS_DIR/session_"*.json
}

# 内存和CPU监控
monitor_resources() {
    log_info "开始资源监控 (${TEST_DURATION}秒)..."
    
    local monitor_file="$RESULTS_DIR/resource_monitor.log"
    local pid=$(pgrep hive-backend || echo "")
    
    if [ -z "$pid" ]; then
        log_warning "未找到 hive-backend 进程"
        return 1
    fi
    
    local start_time=$(date +%s)
    local end_time=$((start_time + TEST_DURATION))
    
    echo "timestamp,memory_percent,cpu_percent,memory_kb" > "$monitor_file"
    
    while [ $(date +%s) -lt $end_time ]; do
        local timestamp=$(date +%s)
        local memory_percent=$(ps -o %mem -p "$pid" | tail -n +2 | tr -d ' ')
        local cpu_percent=$(ps -o %cpu -p "$pid" | tail -n +2 | tr -d ' ')
        local memory_kb=$(ps -o rss -p "$pid" | tail -n +2 | tr -d ' ')
        
        echo "$timestamp,$memory_percent,$cpu_percent,$memory_kb" >> "$monitor_file"
        
        sleep 5
    done
    
    # 计算平均值
    local avg_memory=$(awk -F',' 'NR>1 {sum+=$2; count++} END {print sum/count}' "$monitor_file")
    local avg_cpu=$(awk -F',' 'NR>1 {sum+=$3; count++} END {print sum/count}' "$monitor_file")
    local max_memory=$(awk -F',' 'NR>1 {if($2>max) max=$2} END {print max}' "$monitor_file")
    local max_cpu=$(awk -F',' 'NR>1 {if($3>max) max=$3} END {print max}' "$monitor_file")
    
    # 更新结果文件
    local current_results=$(cat "$RESULTS_FILE")
    echo "$current_results" | jq ". + {
        \"resource_monitoring\": {
            \"duration_seconds\": $TEST_DURATION,
            \"avg_memory_percent\": $avg_memory,
            \"avg_cpu_percent\": $avg_cpu,
            \"max_memory_percent\": $max_memory,
            \"max_cpu_percent\": $max_cpu,
            \"monitor_file\": \"$monitor_file\"
        }
    }" > "$RESULTS_FILE"
    
    log_success "资源监控完成"
    log_info "平均内存使用: ${avg_memory}%"
    log_info "平均CPU使用: ${avg_cpu}%"
    log_info "峰值内存使用: ${max_memory}%"
    log_info "峰值CPU使用: ${max_cpu}%"
}

# 压力测试
stress_test() {
    log_info "开始压力测试..."
    
    local stress_sessions=50
    local pids=()
    local start_time=$(date +%s)
    
    # 创建大量会话
    for i in $(seq 1 $stress_sessions); do
        {
            local session_id="stress_${TEST_ID}_${i}"
            curl -s -X POST "$BASE_URL/api/whatsapp/engine/action" \
                -H "Authorization: Bearer $TOKEN" \
                -H "Content-Type: application/json" \
                -d "{
                    \"session_id\": \"$session_id\",
                    \"phone_number\": \"stress${i}\",
                    \"tenant_id\": 1,
                    \"action\": \"create\"
                }" > /dev/null
        } &
        pids+=($!)
        
        # 控制并发数，避免过载
        if [ ${#pids[@]} -ge 10 ]; then
            for pid in "${pids[@]}"; do
                wait $pid
            done
            pids=()
        fi
    done
    
    # 等待剩余任务完成
    for pid in "${pids[@]}"; do
        wait $pid
    done
    
    local end_time=$(date +%s)
    local total_time=$((end_time - start_time))
    
    # 获取最终统计
    local final_stats=$(curl -s "$BASE_URL/api/whatsapp/engine/stats" \
        -H "Authorization: Bearer $TOKEN")
    
    # 更新结果文件
    local current_results=$(cat "$RESULTS_FILE")
    echo "$current_results" | jq ". + {
        \"stress_test\": {
            \"stress_sessions\": $stress_sessions,
            \"total_time_seconds\": $total_time,
            \"final_stats\": $final_stats
        }
    }" > "$RESULTS_FILE"
    
    log_success "压力测试完成"
    log_info "创建会话数: $stress_sessions"
    log_info "总耗时: ${total_time}秒"
}

# 生成性能报告
generate_report() {
    log_info "生成性能报告..."
    
    local report_file="$RESULTS_DIR/${TEST_ID}_report.html"
    
    cat > "$report_file" << EOF
<!DOCTYPE html>
<html>
<head>
    <title>WhatsApp 引擎性能测试报告</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { background: #f0f0f0; padding: 20px; border-radius: 5px; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .metric { display: inline-block; margin: 10px; padding: 10px; background: #e8f4fd; border-radius: 3px; }
        .success { color: green; }
        .warning { color: orange; }
        .error { color: red; }
        pre { background: #f5f5f5; padding: 10px; border-radius: 3px; overflow-x: auto; }
    </style>
</head>
<body>
    <div class="header">
        <h1>WhatsApp 引擎性能测试报告</h1>
        <p>测试ID: $TEST_ID</p>
        <p>测试时间: $(date)</p>
        <p>测试环境: $(uname -a)</p>
    </div>
    
    <div class="section">
        <h2>测试结果摘要</h2>
        <div id="summary"></div>
    </div>
    
    <div class="section">
        <h2>详细结果</h2>
        <pre id="details"></pre>
    </div>
    
    <script>
        // 加载测试结果
        fetch('${TEST_ID}.json')
            .then(response => response.json())
            .then(data => {
                // 显示摘要
                const summary = document.getElementById('summary');
                if (data.api_response_test) {
                    summary.innerHTML += '<div class="metric">API平均响应时间: ' + data.api_response_test.avg_response_time_ms + 'ms</div>';
                    summary.innerHTML += '<div class="metric">API成功率: ' + data.api_response_test.success_rate + '%</div>';
                }
                if (data.concurrent_session_test) {
                    summary.innerHTML += '<div class="metric">并发会话成功率: ' + data.concurrent_session_test.success_rate + '%</div>';
                    summary.innerHTML += '<div class="metric">平均创建时间: ' + data.concurrent_session_test.avg_create_time_ms + 'ms</div>';
                }
                if (data.resource_monitoring) {
                    summary.innerHTML += '<div class="metric">平均内存使用: ' + data.resource_monitoring.avg_memory_percent + '%</div>';
                    summary.innerHTML += '<div class="metric">平均CPU使用: ' + data.resource_monitoring.avg_cpu_percent + '%</div>';
                }
                
                // 显示详细结果
                document.getElementById('details').textContent = JSON.stringify(data, null, 2);
            })
            .catch(error => {
                console.error('Error loading results:', error);
                document.getElementById('summary').innerHTML = '<div class="error">无法加载测试结果</div>';
            });
    </script>
</body>
</html>
EOF
    
    log_success "性能报告已生成: $report_file"
}

# 主测试流程
main() {
    echo "=== WhatsApp 引擎性能测试 ==="
    echo ""
    
    # 检查服务
    if ! curl -s "$BASE_URL/health" > /dev/null; then
        log_error "服务未运行，请先启动后端服务"
        exit 1
    fi
    
    # 设置
    setup_results_dir
    get_token
    
    # 执行测试
    echo ""
    echo "=== 1. API 响应时间测试 ==="
    benchmark_api_response
    
    echo ""
    echo "=== 2. 并发会话创建测试 ==="
    concurrent_session_test
    
    echo ""
    echo "=== 3. 资源监控测试 ==="
    monitor_resources &
    MONITOR_PID=$!
    
    echo ""
    echo "=== 4. 压力测试 ==="
    stress_test
    
    # 等待监控完成
    wait $MONITOR_PID
    
    # 生成报告
    echo ""
    echo "=== 5. 生成报告 ==="
    generate_report
    
    echo ""
    log_success "性能测试完成！"
    log_info "结果文件: $RESULTS_FILE"
    log_info "报告文件: $RESULTS_DIR/${TEST_ID}_report.html"
}

# 显示帮助
show_help() {
    echo "WhatsApp 引擎性能测试脚本"
    echo ""
    echo "使用方法:"
    echo "  $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --help           显示帮助信息"
    echo "  -c, --concurrent N   设置并发会话数 (默认: 10)"
    echo "  -d, --duration N     设置测试持续时间(秒) (默认: 300)"
    echo "  -u, --url URL        设置服务器地址"
    echo ""
    echo "示例:"
    echo "  $0                           # 使用默认参数"
    echo "  $0 -c 20 -d 600             # 20并发，10分钟测试"
    echo ""
}

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        -c|--concurrent)
            CONCURRENT_SESSIONS="$2"
            shift 2
            ;;
        -d|--duration)
            TEST_DURATION="$2"
            shift 2
            ;;
        -u|--url)
            BASE_URL="$2"
            shift 2
            ;;
        *)
            log_error "未知参数: $1"
            show_help
            exit 1
            ;;
    esac
done

# 检查依赖
if ! command -v curl > /dev/null; then
    log_error "需要安装 curl"
    exit 1
fi

if ! command -v jq > /dev/null; then
    log_error "需要安装 jq"
    exit 1
fi

if ! command -v bc > /dev/null; then
    log_error "需要安装 bc"
    exit 1
fi

# 运行测试
main
