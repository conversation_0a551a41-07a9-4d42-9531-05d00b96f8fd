#!/bin/bash

# WhatsApp 引擎生产环境测试脚本

set -e

BASE_URL="http://localhost:8081"
TOKEN=""
SESSION_ID="prod-test-$(date +%s)"
PHONE_NUMBER="your-phone-number"  # 替换为你的手机号
TARGET_NUMBER="target-phone-number"  # 替换为目标手机号

echo "=== WhatsApp 引擎生产环境测试 ==="
echo "会话ID: $SESSION_ID"
echo "测试手机号: $PHONE_NUMBER"
echo ""

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查服务是否运行
check_service() {
    log_info "检查服务状态..."
    if curl -s "$BASE_URL/health" > /dev/null; then
        log_success "服务运行正常"
    else
        log_error "服务未运行，请先启动后端服务"
        exit 1
    fi
}

# 登录获取 token
login() {
    log_info "登录获取访问令牌..."
    TOKEN=$(curl -s -X POST "$BASE_URL/api/auth/login" \
        -H "Content-Type: application/json" \
        -d '{"username": "admin", "password": "admin123"}' | \
        jq -r '.data.token')
    
    if [ "$TOKEN" != "null" ] && [ -n "$TOKEN" ]; then
        log_success "登录成功，获取到令牌"
    else
        log_error "登录失败"
        exit 1
    fi
}

# 导入生产代理
import_proxies() {
    log_info "导入生产代理..."
    
    # 使用你提供的代理地址
    PROXY_DATA='{
        "proxies": [
            {
                "host": "127.0.0.1",
                "port": 7890,
                "type": "http",
                "region": "local",
                "quality": "high",
                "tags": ["production", "local"]
            }
        ],
        "group_id": "production-group"
    }'
    
    RESULT=$(curl -s -X POST "$BASE_URL/api/whatsapp/engine/proxies/import" \
        -H "Authorization: Bearer $TOKEN" \
        -H "Content-Type: application/json" \
        -d "$PROXY_DATA")
    
    echo "$RESULT" | jq .
    
    if echo "$RESULT" | jq -e '.success' > /dev/null; then
        log_success "代理导入成功"
    else
        log_warning "代理导入失败，但可以继续测试（使用直连）"
    fi
}

# 获取引擎统计
get_engine_stats() {
    log_info "获取引擎统计信息..."
    curl -s "$BASE_URL/api/whatsapp/engine/stats" \
        -H "Authorization: Bearer $TOKEN" | jq .
}

# 创建 WhatsApp 会话
create_session() {
    log_info "创建 WhatsApp 会话..."
    
    RESULT=$(curl -s -X POST "$BASE_URL/api/whatsapp/engine/action" \
        -H "Authorization: Bearer $TOKEN" \
        -H "Content-Type: application/json" \
        -d "{
            \"session_id\": \"$SESSION_ID\",
            \"phone_number\": \"$PHONE_NUMBER\",
            \"tenant_id\": 1,
            \"action\": \"create\"
        }")
    
    echo "$RESULT" | jq .
    
    if echo "$RESULT" | jq -e '.success' > /dev/null; then
        log_success "会话创建成功"
        return 0
    else
        log_error "会话创建失败"
        return 1
    fi
}

# 连接 WhatsApp 会话
connect_session() {
    log_info "连接 WhatsApp 会话..."
    
    RESULT=$(curl -s -X POST "$BASE_URL/api/whatsapp/engine/action" \
        -H "Authorization: Bearer $TOKEN" \
        -H "Content-Type: application/json" \
        -d "{
            \"session_id\": \"$SESSION_ID\",
            \"action\": \"connect\"
        }")
    
    echo "$RESULT" | jq .
    
    # 连接可能超时，这是正常的
    log_info "连接已启动，请检查二维码..."
}

# 获取会话信息（包括二维码）
get_session_info() {
    log_info "获取会话信息..."
    
    RESULT=$(curl -s -X POST "$BASE_URL/api/whatsapp/engine/action" \
        -H "Authorization: Bearer $TOKEN" \
        -H "Content-Type: application/json" \
        -d "{
            \"session_id\": \"$SESSION_ID\",
            \"action\": \"get_info\"
        }")
    
    echo "$RESULT" | jq .
    
    # 检查是否有二维码
    QR_CODE=$(echo "$RESULT" | jq -r '.data.qr_code // empty')
    if [ -n "$QR_CODE" ]; then
        log_success "获取到二维码，请使用 WhatsApp 扫描"
        echo "二维码: $QR_CODE"
        
        # 可以使用 qrencode 生成二维码图片
        if command -v qrencode > /dev/null; then
            echo "$QR_CODE" | qrencode -t UTF8
        fi
    fi
}

# 等待会话连接
wait_for_connection() {
    log_info "等待会话连接..."
    
    for i in {1..30}; do
        RESULT=$(curl -s -X POST "$BASE_URL/api/whatsapp/engine/action" \
            -H "Authorization: Bearer $TOKEN" \
            -H "Content-Type: application/json" \
            -d "{
                \"session_id\": \"$SESSION_ID\",
                \"action\": \"get_info\"
            }")
        
        STATUS=$(echo "$RESULT" | jq -r '.data.status')
        
        case "$STATUS" in
            "connected")
                log_success "会话已连接！"
                return 0
                ;;
            "connecting")
                log_info "连接中... ($i/30)"
                ;;
            "error")
                log_error "连接失败"
                return 1
                ;;
            *)
                log_info "状态: $STATUS ($i/30)"
                ;;
        esac
        
        sleep 2
    done
    
    log_warning "连接超时，但可以继续测试"
    return 1
}

# 发送测试消息
send_test_message() {
    if [ -z "$TARGET_NUMBER" ] || [ "$TARGET_NUMBER" = "target-phone-number" ]; then
        log_warning "未设置目标号码，跳过消息发送测试"
        return 0
    fi
    
    log_info "发送测试消息到 $TARGET_NUMBER..."
    
    MESSAGE="Hello from WhatsApp Engine! 测试时间: $(date)"
    
    RESULT=$(curl -s -X POST "$BASE_URL/api/whatsapp/engine/action" \
        -H "Authorization: Bearer $TOKEN" \
        -H "Content-Type: application/json" \
        -d "{
            \"session_id\": \"$SESSION_ID\",
            \"action\": \"send_message\",
            \"data\": {
                \"to\": \"$TARGET_NUMBER\",
                \"message\": \"$MESSAGE\"
            }
        }")
    
    echo "$RESULT" | jq .
    
    if echo "$RESULT" | jq -e '.success' > /dev/null; then
        log_success "消息发送成功"
    else
        log_error "消息发送失败"
    fi
}

# 压力测试
stress_test() {
    log_info "开始压力测试..."
    
    # 创建多个会话
    for i in {1..5}; do
        SESSION_ID_STRESS="stress-test-$i-$(date +%s)"
        log_info "创建压力测试会话 $i: $SESSION_ID_STRESS"
        
        curl -s -X POST "$BASE_URL/api/whatsapp/engine/action" \
            -H "Authorization: Bearer $TOKEN" \
            -H "Content-Type: application/json" \
            -d "{
                \"session_id\": \"$SESSION_ID_STRESS\",
                \"phone_number\": \"stress$i\",
                \"tenant_id\": 1,
                \"action\": \"create\"
            }" > /dev/null
        
        if [ $? -eq 0 ]; then
            log_success "压力测试会话 $i 创建成功"
        else
            log_error "压力测试会话 $i 创建失败"
        fi
    done
    
    # 获取最终统计
    log_info "压力测试后的引擎统计:"
    get_engine_stats
}

# 清理测试会话
cleanup() {
    log_info "清理测试会话..."
    
    curl -s -X POST "$BASE_URL/api/whatsapp/engine/action" \
        -H "Authorization: Bearer $TOKEN" \
        -H "Content-Type: application/json" \
        -d "{
            \"session_id\": \"$SESSION_ID\",
            \"action\": \"disconnect\"
        }" > /dev/null
    
    log_success "测试会话已断开"
}

# 主测试流程
main() {
    echo "开始生产环境测试..."
    echo ""
    
    # 基础检查
    check_service
    login
    
    # 引擎测试
    echo ""
    echo "=== 引擎基础测试 ==="
    import_proxies
    get_engine_stats
    
    # 会话测试
    echo ""
    echo "=== 会话管理测试 ==="
    if create_session; then
        connect_session
        sleep 3
        get_session_info
        
        # 等待连接（可选）
        echo ""
        echo "=== 连接测试 ==="
        if wait_for_connection; then
            # 消息测试
            echo ""
            echo "=== 消息发送测试 ==="
            send_test_message
        fi
    fi
    
    # 压力测试
    echo ""
    echo "=== 压力测试 ==="
    stress_test
    
    # 清理
    echo ""
    echo "=== 清理 ==="
    cleanup
    
    echo ""
    log_success "生产环境测试完成！"
}

# 检查依赖
check_dependencies() {
    if ! command -v curl > /dev/null; then
        log_error "需要安装 curl"
        exit 1
    fi
    
    if ! command -v jq > /dev/null; then
        log_error "需要安装 jq"
        exit 1
    fi
}

# 显示帮助
show_help() {
    echo "WhatsApp 引擎生产环境测试脚本"
    echo ""
    echo "使用方法:"
    echo "  $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --help     显示帮助信息"
    echo "  -p PHONE       设置测试手机号"
    echo "  -t TARGET      设置目标手机号"
    echo "  -u URL         设置服务器地址 (默认: http://localhost:8081)"
    echo ""
    echo "示例:"
    echo "  $0 -p 1234567890 -t 0987654321"
    echo ""
}

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        -p|--phone)
            PHONE_NUMBER="$2"
            shift 2
            ;;
        -t|--target)
            TARGET_NUMBER="$2"
            shift 2
            ;;
        -u|--url)
            BASE_URL="$2"
            shift 2
            ;;
        *)
            log_error "未知参数: $1"
            show_help
            exit 1
            ;;
    esac
done

# 运行测试
check_dependencies
main
