#!/bin/bash

# 多账户测试页面功能验证脚本

API_BASE="http://localhost:3001"
WEB_BASE="http://localhost:8765"

echo "🧪 多账户测试页面功能验证"
echo "=================================="

# 1. 检查测试服务器
echo "📡 1. 检查测试服务器状态"
if curl -s "$WEB_BASE" > /dev/null; then
    echo "✅ 测试服务器运行正常 ($WEB_BASE)"
else
    echo "❌ 测试服务器未运行，请先启动: python3 server.py"
    exit 1
fi

# 2. 检查多账户 API 服务
echo "📡 2. 检查多账户 API 服务状态"
if curl -s "$API_BASE/api/system/status" > /dev/null; then
    echo "✅ 多账户 API 服务运行正常 ($API_BASE)"
else
    echo "❌ 多账户 API 服务未运行，请先启动多账户服务"
    exit 1
fi

# 3. 测试系统状态 API
echo "📊 3. 测试系统状态 API"
SYSTEM_STATUS=$(curl -s "$API_BASE/api/system/status")
if echo "$SYSTEM_STATUS" | jq -e '.code == "SUCCESS"' > /dev/null; then
    echo "✅ 系统状态 API 正常"
    echo "   版本: $(echo "$SYSTEM_STATUS" | jq -r '.results.system_info.version')"
    echo "   运行时间: $(echo "$SYSTEM_STATUS" | jq -r '.results.system_info.uptime')"
    echo "   实例数: $(echo "$SYSTEM_STATUS" | jq -r '.results.total_instances')"
else
    echo "❌ 系统状态 API 异常"
fi

# 4. 测试创建实例 API
echo "➕ 4. 测试创建实例 API"
CREATE_RESPONSE=$(curl -s -X POST "$API_BASE/api/instances" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "UI-Test-Instance",
    "config": {
      "auto_reply": "这是UI测试实例的自动回复",
      "debug": true,
      "chat_storage": true
    },
    "proxy_config": {
      "enabled": false
    }
  }')

if echo "$CREATE_RESPONSE" | jq -e '.code == "SUCCESS"' > /dev/null; then
    INSTANCE_ID=$(echo "$CREATE_RESPONSE" | jq -r '.results.id')
    echo "✅ 实例创建成功"
    echo "   实例ID: $INSTANCE_ID"
    echo "   实例名称: $(echo "$CREATE_RESPONSE" | jq -r '.results.name')"
else
    echo "❌ 实例创建失败"
    echo "$CREATE_RESPONSE" | jq '.'
    exit 1
fi

# 5. 测试获取实例列表 API
echo "📋 5. 测试获取实例列表 API"
INSTANCES_RESPONSE=$(curl -s "$API_BASE/api/instances")
if echo "$INSTANCES_RESPONSE" | jq -e '.code == "SUCCESS"' > /dev/null; then
    INSTANCE_COUNT=$(echo "$INSTANCES_RESPONSE" | jq '.results | length')
    echo "✅ 实例列表获取成功"
    echo "   实例数量: $INSTANCE_COUNT"
else
    echo "❌ 实例列表获取失败"
fi

# 6. 测试获取实例详情 API
echo "🔍 6. 测试获取实例详情 API"
INSTANCE_DETAIL=$(curl -s "$API_BASE/api/instances/$INSTANCE_ID")
if echo "$INSTANCE_DETAIL" | jq -e '.code == "SUCCESS"' > /dev/null; then
    echo "✅ 实例详情获取成功"
    echo "   状态: $(echo "$INSTANCE_DETAIL" | jq -r '.results.status')"
    echo "   自动回复: $(echo "$INSTANCE_DETAIL" | jq -r '.results.config.auto_reply')"
else
    echo "❌ 实例详情获取失败"
fi

# 7. 测试实例状态 API
echo "📊 7. 测试实例状态 API"
INSTANCE_STATUS=$(curl -s "$API_BASE/api/instances/$INSTANCE_ID/status")
if echo "$INSTANCE_STATUS" | jq -e '.code == "SUCCESS"' > /dev/null; then
    echo "✅ 实例状态获取成功"
    echo "   连接状态: $(echo "$INSTANCE_STATUS" | jq -r '.results.is_connected')"
    echo "   登录状态: $(echo "$INSTANCE_STATUS" | jq -r '.results.is_logged_in')"
else
    echo "❌ 实例状态获取失败"
fi

# 8. 测试代理测试 API
echo "🌐 8. 测试代理测试 API"
PROXY_TEST=$(curl -s -X POST "$API_BASE/api/proxy/test" \
  -H "Content-Type: application/json" \
  -d '{
    "type": "socks5",
    "host": "127.0.0.1",
    "port": 7890
  }')

if echo "$PROXY_TEST" | jq -e '.code' > /dev/null; then
    echo "✅ 代理测试 API 响应正常"
    echo "   测试结果: $(echo "$PROXY_TEST" | jq -r '.results.success // "N/A"')"
else
    echo "❌ 代理测试 API 异常"
fi

# 9. 测试页面可访问性
echo "🌐 9. 测试页面可访问性"
if curl -s "$WEB_BASE/multiaccount.html" | grep -q "多账户 API 测试工具"; then
    echo "✅ 多账户测试页面可正常访问"
else
    echo "❌ 多账户测试页面访问异常"
fi

if curl -s "$WEB_BASE/index.html" | grep -q "单账户版本"; then
    echo "✅ 版本选择器正常工作"
else
    echo "❌ 版本选择器可能有问题"
fi

# 10. 清理测试实例
echo "🗑️ 10. 清理测试实例"
DELETE_RESPONSE=$(curl -s -X DELETE "$API_BASE/api/instances/$INSTANCE_ID")
if echo "$DELETE_RESPONSE" | jq -e '.code == "SUCCESS"' > /dev/null; then
    echo "✅ 测试实例删除成功"
else
    echo "❌ 测试实例删除失败"
fi

# 11. 验证最终状态
echo "📊 11. 验证最终系统状态"
FINAL_STATUS=$(curl -s "$API_BASE/api/system/status")
FINAL_COUNT=$(echo "$FINAL_STATUS" | jq -r '.results.total_instances')
echo "   最终实例数: $FINAL_COUNT"

echo ""
echo "✅ 多账户测试页面功能验证完成！"
echo ""
echo "📝 测试总结："
echo "- ✅ 测试服务器运行正常"
echo "- ✅ 多账户 API 服务正常"
echo "- ✅ 系统状态 API 正常"
echo "- ✅ 实例管理 API 正常"
echo "- ✅ 代理测试 API 正常"
echo "- ✅ 测试页面可访问"
echo ""
echo "🎉 现在可以在浏览器中测试多账户功能了！"
echo "   多账户测试页面: $WEB_BASE/multiaccount.html"
