{"name": "whatsapp-multiaccount-test", "version": "1.0.0", "description": "Playwright tests for WhatsApp Multi Account API", "main": "test_multiaccount_playwright.js", "scripts": {"test": "node test_multiaccount_playwright.js", "test-qr": "node test_multiaccount_playwright.js", "install-browsers": "npx playwright install"}, "keywords": ["whatsapp", "playwright", "testing", "multiaccount", "qr-code"], "author": "WhatsApp Test Team", "license": "MIT", "devDependencies": {"playwright": "^1.40.0"}}