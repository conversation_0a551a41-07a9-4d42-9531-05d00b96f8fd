const { chromium } = require('playwright');

async function simpleQRTest() {
    console.log('🎭 简化版 Playwright QR 码测试');
    console.log('='.repeat(40));

    const browser = await chromium.launch({ 
        headless: false,
        slowMo: 2000
    });
    
    const context = await browser.newContext({
        viewport: { width: 1280, height: 720 }
    });
    
    const page = await context.newPage();

    try {
        // 1. 访问页面
        console.log('📱 1. 访问多账户测试页面');
        await page.goto('http://localhost:8765/multiaccount.html');
        await page.waitForLoadState('networkidle');
        console.log('   ✅ 页面加载完成');

        // 2. 等待页面初始化
        console.log('⏳ 2. 等待页面初始化');
        await page.waitForTimeout(5000);

        // 3. 切换到实例管理
        console.log('📱 3. 切换到实例管理标签页');
        await page.click('text=📱 实例管理');
        await page.waitForTimeout(3000);

        // 4. 截图查看当前状态
        console.log('📸 4. 截图查看当前状态');
        await page.screenshot({ 
            path: 'step1_instances_page.png',
            fullPage: true 
        });

        // 5. 使用 JavaScript 直接查找和点击登录按钮
        console.log('🔐 5. 使用 JavaScript 查找登录按钮');
        const loginButtonInfo = await page.evaluate(() => {
            const buttons = document.querySelectorAll('button');
            const loginButtons = [];
            
            buttons.forEach((btn, index) => {
                if (btn.textContent.includes('🔐 登录')) {
                    loginButtons.push({
                        index: index,
                        text: btn.textContent,
                        visible: btn.offsetParent !== null,
                        onclick: btn.getAttribute('onclick'),
                        className: btn.className
                    });
                }
            });
            
            return {
                totalButtons: buttons.length,
                loginButtons: loginButtons
            };
        });

        console.log('   按钮信息:', JSON.stringify(loginButtonInfo, null, 2));

        if (loginButtonInfo.loginButtons.length > 0) {
            console.log('🎯 6. 直接调用登录函数');
            
            // 提取实例ID
            const firstButton = loginButtonInfo.loginButtons[0];
            const onclickAttr = firstButton.onclick;
            const instanceIdMatch = onclickAttr.match(/loginInstance\('([^']+)'\)/);
            
            if (instanceIdMatch) {
                const instanceId = instanceIdMatch[1];
                console.log(`   实例ID: ${instanceId}`);
                
                // 直接调用 JavaScript 函数
                const result = await page.evaluate((id) => {
                    // 检查函数是否存在
                    if (typeof loginInstance === 'function') {
                        loginInstance(id);
                        return { success: true, message: 'loginInstance 函数调用成功' };
                    } else {
                        return { success: false, message: 'loginInstance 函数不存在' };
                    }
                }, instanceId);
                
                console.log('   调用结果:', result);
                
                if (result.success) {
                    // 7. 等待并检查模态框
                    console.log('📱 7. 检查 QR 码模态框');
                    await page.waitForTimeout(5000);
                    
                    const modalInfo = await page.evaluate(() => {
                        const modal = document.getElementById('qrModal');
                        if (modal) {
                            return {
                                exists: true,
                                visible: modal.classList.contains('show'),
                                display: window.getComputedStyle(modal).display,
                                innerHTML: modal.innerHTML.substring(0, 200) + '...'
                            };
                        }
                        return { exists: false };
                    });
                    
                    console.log('   模态框信息:', JSON.stringify(modalInfo, null, 2));
                    
                    // 8. 截图查看模态框状态
                    console.log('📸 8. 截图查看模态框状态');
                    await page.screenshot({ 
                        path: 'step2_modal_state.png',
                        fullPage: true 
                    });
                    
                    if (modalInfo.visible) {
                        console.log('   ✅ QR 码模态框已显示');
                        
                        // 9. 检查 QR 码图片
                        console.log('🔍 9. 检查 QR 码图片');
                        const qrInfo = await page.evaluate(() => {
                            const qrImage = document.querySelector('.qr-code-image');
                            if (qrImage) {
                                return {
                                    exists: true,
                                    src: qrImage.src.substring(0, 50) + '...',
                                    srcLength: qrImage.src.length,
                                    visible: qrImage.offsetParent !== null
                                };
                            }
                            
                            const qrContainer = document.getElementById('qrCodeContainer');
                            return {
                                exists: false,
                                containerHTML: qrContainer ? qrContainer.innerHTML.substring(0, 200) + '...' : 'Container not found'
                            };
                        });
                        
                        console.log('   QR 码信息:', JSON.stringify(qrInfo, null, 2));
                        
                        if (qrInfo.exists) {
                            console.log('   ✅ QR 码图片已加载');
                        } else {
                            console.log('   ⚠️ QR 码图片未找到');
                        }
                        
                        // 10. 测试关闭模态框
                        console.log('❌ 10. 测试关闭模态框');
                        await page.evaluate(() => {
                            if (typeof closeQRModal === 'function') {
                                closeQRModal();
                            }
                        });
                        
                        await page.waitForTimeout(2000);
                        
                        const modalClosed = await page.evaluate(() => {
                            const modal = document.getElementById('qrModal');
                            return modal ? !modal.classList.contains('show') : true;
                        });
                        
                        if (modalClosed) {
                            console.log('   ✅ 模态框关闭成功');
                        } else {
                            console.log('   ❌ 模态框关闭失败');
                        }
                        
                    } else {
                        console.log('   ❌ QR 码模态框未显示');
                    }
                }
            } else {
                console.log('   ❌ 无法提取实例ID');
            }
        } else {
            console.log('   ❌ 没有找到登录按钮');
        }

        // 11. 最终截图
        console.log('📸 11. 保存最终截图');
        await page.screenshot({ 
            path: 'step3_final_state.png',
            fullPage: true 
        });

        console.log('\n🎉 简化测试完成！');
        
    } catch (error) {
        console.error('❌ 测试错误:', error.message);
        await page.screenshot({ 
            path: 'simple_test_error.png',
            fullPage: true 
        });
    } finally {
        console.log('⏳ 保持浏览器打开 15 秒以便观察...');
        await page.waitForTimeout(15000);
        await browser.close();
    }
}

simpleQRTest().catch(console.error);
