#!/usr/bin/env python3
"""
WhatsApp API 测试服务器 - 包含代理功能解决 CORS 问题
"""

import http.server
import socketserver
import webbrowser
import os
import sys
import urllib.request
import urllib.parse
import json
import base64
from pathlib import Path

class WhatsAppProxyHandler(http.server.SimpleHTTPRequestHandler):
    """支持 CORS 和代理功能的 HTTP 请求处理器"""

    def end_headers(self):
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Requested-With')
        super().end_headers()

    def do_OPTIONS(self):
        self.send_response(200)
        self.end_headers()

    def do_GET(self):
        # 检查是否是 API 代理请求
        if self.path.startswith('/api/'):
            self.handle_api_request('GET')
        else:
            # 处理静态文件
            super().do_GET()

    def do_POST(self):
        # 检查是否是 API 代理请求
        if self.path.startswith('/api/'):
            self.handle_api_request('POST')
        else:
            self.send_error(404)

    def handle_api_request(self, method):
        """处理 API 代理请求"""
        try:
            # 移除 /api 前缀，构造真实的 API URL
            api_path = self.path[4:]  # 移除 '/api'
            api_url = f"http://localhost:3000{api_path}"
            print(f"[DEBUG] Proxying {method} {self.path} -> {api_url}")

            # 准备请求头
            headers = {
                'Authorization': 'Basic ' + base64.b64encode(b'admin:admin').decode('ascii'),
                'User-Agent': 'WhatsApp-Test-Proxy/1.0'
            }
            print(f"[DEBUG] Request headers: {headers}")

            # 处理请求体（对于 POST 请求）
            data = None
            if method == 'POST':
                content_length = int(self.headers.get('Content-Length', 0))
                if content_length > 0:
                    data = self.rfile.read(content_length)
                    headers['Content-Type'] = self.headers.get('Content-Type', 'application/json')

            # 创建请求
            req = urllib.request.Request(api_url, data=data, headers=headers, method=method)

            # 发送请求
            with urllib.request.urlopen(req, timeout=30) as response:
                # 读取响应
                response_data = response.read()

                # 发送响应
                self.send_response(response.status)
                self.send_header('Content-Type', 'application/json')
                self.end_headers()
                self.wfile.write(response_data)

        except urllib.error.HTTPError as e:
            # HTTP 错误
            print(f"[DEBUG] HTTP Error: {e.code} {e.reason}")
            print(f"[DEBUG] Error details: {e}")
            self.send_response(e.code)
            self.send_header('Content-Type', 'application/json')
            self.end_headers()
            error_response = {
                'error': f'API Error: {e.code} {e.reason}',
                'message': str(e)
            }
            self.wfile.write(json.dumps(error_response).encode())

        except Exception as e:
            # 其他错误
            print(f"[DEBUG] Exception: {type(e).__name__}: {e}")
            self.send_response(500)
            self.send_header('Content-Type', 'application/json')
            self.end_headers()
            error_response = {
                'error': 'Proxy Error',
                'message': str(e)
            }
            self.wfile.write(json.dumps(error_response).encode())

def main():
    # 设置端口
    PORT = 8765
    
    # 确保在正确的目录
    script_dir = Path(__file__).parent
    os.chdir(script_dir)
    
    print(f"🚀 启动 WhatsApp API 测试服务器...")
    print(f"📁 工作目录: {script_dir}")
    print(f"🌐 服务器地址: http://localhost:{PORT}")
    print(f"📄 测试页面: http://localhost:{PORT}/index.html")
    print("=" * 50)
    
    try:
        # 创建服务器
        with socketserver.TCPServer(("", PORT), WhatsAppProxyHandler) as httpd:
            print(f"✅ 服务器已启动在端口 {PORT}")
            print("💡 提示:")
            print("   1. 确保 WhatsApp API 服务运行在 http://localhost:3000")
            print("   2. 在浏览器中访问测试页面")
            print("   3. 按 Ctrl+C 停止服务器")
            print("=" * 50)
            
            # 自动打开浏览器
            try:
                webbrowser.open(f'http://localhost:{PORT}/index.html')
                print("🌐 已自动打开浏览器")
            except:
                print("⚠️  无法自动打开浏览器，请手动访问上述地址")
            
            # 启动服务器
            httpd.serve_forever()
            
    except KeyboardInterrupt:
        print("\n🛑 服务器已停止")
    except OSError as e:
        if e.errno == 48:  # Address already in use
            print(f"❌ 端口 {PORT} 已被占用，请检查是否有其他服务在运行")
            print("💡 你可以修改 PORT 变量使用其他端口")
        else:
            print(f"❌ 启动服务器时出错: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
