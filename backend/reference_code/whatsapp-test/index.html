<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WhatsApp API 完整测试工具</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #25D366, #128C7E);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }

        .header {
            background: #075E54;
            color: white;
            padding: 20px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.1em;
            opacity: 0.9;
        }

        .version-selector {
            background: #128C7E;
            padding: 15px;
            text-align: center;
        }

        .version-btn {
            background: #25D366;
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 600;
            margin: 0 10px;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
        }

        .version-btn:hover {
            background: #1da851;
            transform: translateY(-2px);
        }

        .version-btn.current {
            background: #075E54;
        }

        .nav-tabs {
            display: flex;
            background: #f8f9fa;
            border-bottom: 1px solid #ddd;
            overflow-x: auto;
        }

        .nav-tab {
            padding: 15px 25px;
            cursor: pointer;
            border: none;
            background: none;
            font-size: 16px;
            font-weight: 600;
            color: #666;
            transition: all 0.3s;
            white-space: nowrap;
        }

        .nav-tab.active {
            background: #25D366;
            color: white;
        }

        .nav-tab:hover {
            background: #e9ecef;
        }

        .nav-tab.active:hover {
            background: #128C7E;
        }

        .tab-content {
            display: none;
            padding: 30px;
        }

        .tab-content.active {
            display: block;
        }

        .main-content {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 30px;
        }

        .section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 25px;
            border: 1px solid #e9ecef;
        }

        .section h3 {
            color: #075E54;
            margin-bottom: 20px;
            font-size: 1.3em;
            border-bottom: 2px solid #25D366;
            padding-bottom: 10px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #333;
        }

        .form-group input,
        .form-group textarea,
        .form-group select {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.3s;
        }

        .form-group input:focus,
        .form-group textarea:focus,
        .form-group select:focus {
            outline: none;
            border-color: #25D366;
        }

        .btn {
            background: #25D366;
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 600;
            transition: all 0.3s;
            width: 100%;
            margin-top: 10px;
        }

        .btn:hover {
            background: #128C7E;
            transform: translateY(-2px);
        }

        .btn:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
        }

        .btn-secondary {
            background: #6c757d;
        }

        .btn-secondary:hover {
            background: #5a6268;
        }

        .btn-danger {
            background: #dc3545;
        }

        .btn-danger:hover {
            background: #c82333;
        }

        .status {
            margin-top: 20px;
            padding: 15px;
            border-radius: 8px;
            font-weight: 600;
        }

        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }

        .qr-container {
            text-align: center;
            margin-top: 20px;
        }

        .qr-container img {
            max-width: 100%;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .config-section {
            grid-column: 1 / -1;
            background: #fff3cd;
            border: 1px solid #ffeaa7;
        }

        .config-section h3 {
            color: #856404;
            border-bottom-color: #ffc107;
        }

        .config-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
        }

        .file-input {
            position: relative;
            overflow: hidden;
            display: inline-block;
            width: 100%;
        }

        .file-input input[type=file] {
            position: absolute;
            left: -9999px;
        }

        .file-input-label {
            display: block;
            padding: 12px;
            border: 2px dashed #ddd;
            border-radius: 8px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s;
        }

        .file-input-label:hover {
            border-color: #25D366;
            background: #f8f9fa;
        }

        .contacts-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }

        .contacts-table th,
        .contacts-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }

        .contacts-table th {
            background: #f8f9fa;
            font-weight: 600;
        }

        .contacts-table tr:hover {
            background: #f8f9fa;
        }

        @media (max-width: 768px) {
            .main-content {
                grid-template-columns: 1fr;
                gap: 20px;
            }

            .header h1 {
                font-size: 2em;
            }

            .nav-tabs {
                flex-wrap: wrap;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 WhatsApp API 测试工具</h1>
            <p>基于 go-whatsapp-web-multidevice 项目的功能测试界面</p>
        </div>

        <div class="version-selector">
            <a href="index.html" class="version-btn current">📱 单账户版本</a>
            <a href="multiaccount.html" class="version-btn">🚀 多账户版本</a>
            <a href="app.html" class="version-btn">🔧 完整功能版本</a>
        </div>
        
        <div class="main-content">
            <!-- 配置区域 -->
            <div class="section config-section">
                <h2>⚙️ API 配置</h2>
                <div class="config-grid">
                    <div class="form-group">
                        <label for="apiUrl">API 基础地址:</label>
                        <input type="text" id="apiUrl" value="http://localhost:3000" placeholder="http://localhost:3000">
                    </div>
                    <div class="form-group">
                        <label for="username">用户名 (Basic Auth):</label>
                        <input type="text" id="username" placeholder="admin">
                    </div>
                    <div class="form-group">
                        <label for="password">密码 (Basic Auth):</label>
                        <input type="password" id="password" placeholder="admin">
                    </div>
                </div>
                <button class="btn" onclick="testConnection()">🔗 测试连接</button>
                <div id="connectionStatus"></div>
            </div>
            
            <!-- 登录区域 -->
            <div class="section">
                <h2>📱 WhatsApp 登录</h2>
                <button class="btn" onclick="loginWithQR()">📷 扫码登录</button>
                <button class="btn" onclick="loginWithCode()">🔢 配对码登录</button>
                <button class="btn" onclick="logout()">🚪 退出登录</button>
                <button class="btn" onclick="checkStatus()">📊 检查状态</button>
                
                <div class="form-group" style="margin-top: 20px;">
                    <label for="phoneNumber">手机号 (配对码登录用):</label>
                    <input type="text" id="phoneNumber" placeholder="628912344551">
                </div>
                
                <div id="loginStatus"></div>
                <div class="qr-container" id="qrContainer"></div>
            </div>
            
            <!-- 发送消息区域 -->
            <div class="section">
                <h2>💬 发送消息</h2>
                <div class="form-group">
                    <label for="recipient">接收者 (手机号或群组ID):</label>
                    <input type="text" id="recipient" placeholder="<EMAIL>">
                </div>
                <div class="form-group">
                    <label for="message">消息内容:</label>
                    <textarea id="message" rows="4" placeholder="输入要发送的消息..."></textarea>
                </div>
                <button class="btn" onclick="sendMessage()">📤 发送文本消息</button>
                <div id="sendStatus"></div>
            </div>
        </div>
    </div>

    <script>
        // 全局配置
        let config = {
            apiUrl: 'http://localhost:3000',
            username: '',
            password: ''
        };

        // 更新配置
        function updateConfig() {
            config.apiUrl = document.getElementById('apiUrl').value;
            config.username = document.getElementById('username').value;
            config.password = document.getElementById('password').value;
        }

        // 创建认证头
        function getAuthHeaders() {
            updateConfig();
            const headers = {
                'Content-Type': 'application/json'
            };
            
            if (config.username && config.password) {
                headers['Authorization'] = 'Basic ' + btoa(config.username + ':' + config.password);
            }
            
            return headers;
        }

        // 显示状态消息
        function showStatus(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.innerHTML = `<div class="status ${type}">${message}</div>`;
        }

        // 测试连接
        async function testConnection() {
            showStatus('connectionStatus', '🔄 正在测试连接...', 'info');
            
            try {
                const response = await fetch(`${config.apiUrl}/app/devices`, {
                    headers: getAuthHeaders()
                });
                
                if (response.ok) {
                    showStatus('connectionStatus', '✅ 连接成功！API 服务正常运行', 'success');
                } else {
                    showStatus('connectionStatus', `❌ 连接失败: ${response.status} ${response.statusText}`, 'error');
                }
            } catch (error) {
                showStatus('connectionStatus', `❌ 连接错误: ${error.message}`, 'error');
            }
        }

        // 扫码登录
        async function loginWithQR() {
            showStatus('loginStatus', '🔄 正在获取二维码...', 'info');
            
            try {
                const response = await fetch(`${config.apiUrl}/app/login`, {
                    headers: getAuthHeaders()
                });
                
                const data = await response.json();
                
                if (response.ok && data.code === 200) {
                    showStatus('loginStatus', '📷 请扫描二维码登录', 'success');
                    document.getElementById('qrContainer').innerHTML = 
                        `<img src="data:image/png;base64,${data.results.qr_code}" alt="QR Code">`;
                } else {
                    showStatus('loginStatus', `❌ 获取二维码失败: ${data.message}`, 'error');
                }
            } catch (error) {
                showStatus('loginStatus', `❌ 登录错误: ${error.message}`, 'error');
            }
        }

        // 配对码登录
        async function loginWithCode() {
            const phone = document.getElementById('phoneNumber').value;
            if (!phone) {
                showStatus('loginStatus', '❌ 请输入手机号', 'error');
                return;
            }
            
            showStatus('loginStatus', '🔄 正在获取配对码...', 'info');
            
            try {
                const response = await fetch(`${config.apiUrl}/app/login-with-code?phone=${phone}`, {
                    headers: getAuthHeaders()
                });
                
                const data = await response.json();
                
                if (response.ok && data.code === 200) {
                    showStatus('loginStatus', `🔢 配对码: ${data.results.code}`, 'success');
                } else {
                    showStatus('loginStatus', `❌ 获取配对码失败: ${data.message}`, 'error');
                }
            } catch (error) {
                showStatus('loginStatus', `❌ 登录错误: ${error.message}`, 'error');
            }
        }

        // 退出登录
        async function logout() {
            showStatus('loginStatus', '🔄 正在退出登录...', 'info');
            
            try {
                const response = await fetch(`${config.apiUrl}/app/logout`, {
                    headers: getAuthHeaders()
                });
                
                const data = await response.json();
                
                if (response.ok) {
                    showStatus('loginStatus', '✅ 已成功退出登录', 'success');
                    document.getElementById('qrContainer').innerHTML = '';
                } else {
                    showStatus('loginStatus', `❌ 退出登录失败: ${data.message}`, 'error');
                }
            } catch (error) {
                showStatus('loginStatus', `❌ 退出错误: ${error.message}`, 'error');
            }
        }

        // 检查状态
        async function checkStatus() {
            showStatus('loginStatus', '🔄 正在检查状态...', 'info');
            
            try {
                const response = await fetch(`${config.apiUrl}/user/info`, {
                    headers: getAuthHeaders()
                });
                
                const data = await response.json();
                
                if (response.ok && data.code === 200) {
                    showStatus('loginStatus', `✅ 已登录: ${data.results.name} (${data.results.phone})`, 'success');
                } else {
                    showStatus('loginStatus', '❌ 未登录或连接失败', 'error');
                }
            } catch (error) {
                showStatus('loginStatus', `❌ 状态检查错误: ${error.message}`, 'error');
            }
        }

        // 发送消息
        async function sendMessage() {
            const recipient = document.getElementById('recipient').value;
            const message = document.getElementById('message').value;
            
            if (!recipient || !message) {
                showStatus('sendStatus', '❌ 请填写接收者和消息内容', 'error');
                return;
            }
            
            showStatus('sendStatus', '🔄 正在发送消息...', 'info');
            
            try {
                const response = await fetch(`${config.apiUrl}/send/message`, {
                    method: 'POST',
                    headers: getAuthHeaders(),
                    body: JSON.stringify({
                        phone: recipient,
                        message: message
                    })
                });
                
                const data = await response.json();
                
                if (response.ok && data.code === 200) {
                    showStatus('sendStatus', '✅ 消息发送成功！', 'success');
                    document.getElementById('message').value = '';
                } else {
                    showStatus('sendStatus', `❌ 发送失败: ${data.message}`, 'error');
                }
            } catch (error) {
                showStatus('sendStatus', `❌ 发送错误: ${error.message}`, 'error');
            }
        }

        // 页面加载时自动测试连接
        window.onload = function() {
            setTimeout(testConnection, 1000);
        };
    </script>
</body>
</html>
