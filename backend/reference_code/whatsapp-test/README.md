# WhatsApp API 测试工具

这是一个用于测试 WhatsApp API 的 Web 界面工具，支持单账户和多账户版本。

## 🚀 版本说明

### 📱 单账户版本 (`index.html`)
- 基于 [go-whatsapp-web-multidevice](https://github.com/aldinokemal/go-whatsapp-web-multidevice) 项目
- 简化的测试界面
- 基本的登录和消息发送功能

### 🚀 多账户版本 (`multiaccount.html`) ⭐ **新增**
- 基于 [go-whatsapp-web-multiaccount](https://github.com/jlu/go-whatsapp-web-multiaccount) 项目
- 支持多个 WhatsApp 账户同时管理
- 每个账户可配置独立的网络代理
- 实例管理、状态监控、代理测试等功能

### 🔧 完整功能版本 (`app.html`)
- 包含所有 WhatsApp API 功能的完整测试界面
- 支持媒体文件、群组管理、联系人等高级功能

## 项目结构

```
whatsapp-test/
├── index.html          # 单账户版本测试页面
├── multiaccount.html   # 多账户版本测试页面 ⭐
├── app.html            # 完整功能测试页面
├── server.py           # Python HTTP 服务器
├── README.md           # 项目说明
└── start.sh            # 启动脚本
```

## 功能特性

### 单账户版本功能
- 🔐 WhatsApp 登录/登出
- 📱 二维码扫描登录
- 💬 发送文本消息
- 📊 连接状态检测

### 多账户版本功能 ⭐
- 📊 系统状态监控
- 📱 多实例管理 (创建、删除、配置)
- 🌐 独立代理配置和测试
- 🔐 实例登录/登出管理
- 💬 基于实例的消息发送
- ⚙️ 实时状态监控和自动刷新

### 完整功能版本
- 📎 媒体文件发送 (图片、音频、视频、文档)
- 👥 联系人管理
- 👨‍👩‍👧‍👦 群组管理
- 📍 位置分享
- 💬 聊天记录管理

## 使用方法

### 1. 启动对应的 WhatsApp API 服务

#### 单账户版本
```bash
# 使用 Docker
docker run --publish 3000:3000 --name whatsapp-go-multidevice aldinokemal2104/go-whatsapp-web-multidevice

# 或者从源码运行
go run main.go --autoreply="感谢您的消息" --webhook="http://localhost:5000/webhook"
```

#### 多账户版本 ⭐
```bash
# 进入多账户项目目录
cd go-whatsapp-web-multiaccount

# 使用启动脚本
./start.sh

# 或者手动启动
cd src
go build -o ../bin/multiaccount main.go
../bin/multiaccount rest --port=3001 --debug=true
```

### 2. 启动测试工具

```bash
# 使用 Python 启动 HTTP 服务器
python3 server.py

# 或者使用 start.sh 脚本
./start.sh
```

### 3. 访问测试界面

打开浏览器访问 http://localhost:8080，然后选择对应的版本：

- **📱 单账户版本** - 适合测试基本功能
- **🚀 多账户版本** - 适合测试多账户管理功能 ⭐
- **🔧 完整功能版本** - 适合测试所有 API 功能

## 多账户版本使用指南 ⭐

### 1. 系统状态监控
- 查看总实例数、活跃实例数、已连接实例数
- 监控系统运行时间
- 实时刷新状态信息

### 2. 创建和管理实例
- 设置实例名称和自动回复消息
- 配置独立的 SOCKS5/HTTP 代理
- 启用聊天存储和调试模式

### 3. 代理配置和测试
- 支持 SOCKS5 和 HTTP 代理
- 代理认证 (用户名/密码)
- 实时代理连接测试

### 4. 实例操作
- 扫码登录 WhatsApp 账户
- 查看实例连接状态
- 重新连接和登出操作
- 删除不需要的实例

### 5. 消息发送
- 选择特定实例发送消息
- 支持文本消息发送
- 基于实例的消息路由

## 单账户版本测试步骤

### 第一步：配置 API
1. 确认 API 地址为 `http://localhost:3000`
2. 输入用户名和密码（如果设置了 Basic Auth）
3. 点击"测试连接"确认 API 服务正常

### 第二步：登录 WhatsApp
选择以下方式之一：

**扫码登录：**
1. 点击"扫码登录"
2. 使用手机 WhatsApp 扫描显示的二维码

**配对码登录：**
1. 输入手机号（格式：628912344551）
2. 点击"配对码登录"
3. 在手机 WhatsApp 中输入显示的配对码

### 第三步：发送测试消息
1. 输入接收者号码（格式：<EMAIL>）
2. 输入消息内容
3. 点击"发送文本消息"

## API 端点说明

### 认证相关
- `GET /app/login` - 获取登录二维码
- `GET /app/login-with-code?phone=xxx` - 获取配对码
- `GET /app/logout` - 退出登录
- `GET /app/devices` - 获取设备信息

### 用户信息
- `GET /user/info` - 获取用户信息
- `GET /user/check` - 检查用户状态

### 消息发送
- `POST /send/message` - 发送文本消息
- `POST /send/image` - 发送图片
- `POST /send/file` - 发送文件
- `POST /send/video` - 发送视频

## 配置说明

### 单账户版本
- API 服务器地址: `http://localhost:3000`
- 支持基本认证配置

### 多账户版本 ⭐
- API 服务器地址: `http://localhost:3001`
- 支持自动刷新间隔设置
- 可配置多个实例的独立代理

## 故障排除

### 单账户版本
- 检查 API 服务器地址 (默认 3000 端口)
- 确认服务正在运行

### 多账户版本 ⭐
- 检查 API 服务器地址 (默认 3001 端口)
- 确认多账户服务正在运行
- 检查实例状态和代理配置
- 查看系统状态监控信息

### 通用问题
- 确保防火墙允许相应端口访问
- 检查网络连接稳定性
- 重新生成二维码进行登录

## 文件说明

- `index.html` - 单账户版本测试界面
- `multiaccount.html` - 多账户版本测试界面 ⭐
- `app.html` - 完整功能测试界面
- `server.py` - Python HTTP 服务器
- `start.sh` - 启动脚本

## 开发和扩展

1. 修改对应的 HTML/CSS/JavaScript 文件
2. 测试新功能
3. 提交更改

## 技术栈

- **前端**: HTML5 + CSS3 + JavaScript (原生)
- **后端**: Python HTTP Server
- **单账户 API**: go-whatsapp-web-multidevice REST API
- **多账户 API**: go-whatsapp-web-multiaccount REST API ⭐
- **协议**: WhatsApp Web Multi-Device Protocol

## 许可证

MIT License
