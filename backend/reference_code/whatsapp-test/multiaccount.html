<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WhatsApp 多账户 API 测试工具</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #25D366, #128C7E);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1600px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }
        
        .header {
            background: #075E54;
            color: white;
            padding: 20px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .header p {
            font-size: 1.1em;
            opacity: 0.9;
        }

        .version-selector {
            background: #128C7E;
            padding: 15px;
            text-align: center;
        }

        .version-btn {
            background: #25D366;
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 600;
            margin: 0 10px;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
        }

        .version-btn:hover {
            background: #1da851;
            transform: translateY(-2px);
        }

        .version-btn.current {
            background: #075E54;
        }
        
        .nav-tabs {
            display: flex;
            background: #f8f9fa;
            border-bottom: 1px solid #ddd;
            overflow-x: auto;
        }
        
        .nav-tab {
            padding: 15px 25px;
            cursor: pointer;
            border: none;
            background: none;
            font-size: 16px;
            color: #666;
            border-bottom: 3px solid transparent;
            transition: all 0.3s ease;
            white-space: nowrap;
        }
        
        .nav-tab.active {
            color: #25D366;
            border-bottom-color: #25D366;
            background: white;
        }
        
        .nav-tab:hover {
            background: #e9ecef;
        }
        
        .tab-content {
            display: none;
            padding: 30px;
        }
        
        .tab-content.active {
            display: block;
        }
        
        .section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 25px;
            margin-bottom: 25px;
            border-left: 5px solid #25D366;
        }
        
        .section h3 {
            color: #075E54;
            margin-bottom: 20px;
            font-size: 1.4em;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #333;
        }
        
        .form-group input,
        .form-group textarea,
        .form-group select {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s ease;
        }
        
        .form-group input:focus,
        .form-group textarea:focus,
        .form-group select:focus {
            outline: none;
            border-color: #25D366;
        }
        
        .btn {
            background: #25D366;
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 600;
            transition: all 0.3s ease;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        
        .btn:hover {
            background: #128C7E;
            transform: translateY(-2px);
        }
        
        .btn-secondary {
            background: #6c757d;
        }
        
        .btn-secondary:hover {
            background: #5a6268;
        }
        
        .btn-danger {
            background: #dc3545;
        }
        
        .btn-danger:hover {
            background: #c82333;
        }
        
        .status {
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            font-weight: 600;
        }
        
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        
        .instance-card {
            background: white;
            border: 2px solid #ddd;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            transition: all 0.3s ease;
        }
        
        .instance-card:hover {
            border-color: #25D366;
            box-shadow: 0 5px 15px rgba(37, 211, 102, 0.2);
        }
        
        .instance-card.active {
            border-color: #25D366;
            background: #f8fff9;
        }
        
        .instance-header {
            display: flex;
            justify-content: between;
            align-items: center;
            margin-bottom: 15px;
        }
        
        .instance-name {
            font-size: 1.3em;
            font-weight: bold;
            color: #075E54;
        }
        
        .instance-status {
            padding: 5px 12px;
            border-radius: 20px;
            font-size: 0.9em;
            font-weight: 600;
        }
        
        .status-connected {
            background: #d4edda;
            color: #155724;
        }
        
        .status-disconnected {
            background: #f8d7da;
            color: #721c24;
        }
        
        .status-connecting {
            background: #fff3cd;
            color: #856404;
        }
        
        .status-created {
            background: #d1ecf1;
            color: #0c5460;
        }
        
        .instance-info {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 15px;
        }
        
        .info-item {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
        }
        
        .info-label {
            font-size: 0.9em;
            color: #666;
            margin-bottom: 5px;
        }
        
        .info-value {
            font-weight: 600;
            color: #333;
        }
        
        .instance-actions {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
        }
        
        .grid-2 {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        
        .grid-3 {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 20px;
        }
        
        @media (max-width: 768px) {
            .grid-2,
            .grid-3 {
                grid-template-columns: 1fr;
            }
            
            .nav-tabs {
                flex-direction: column;
            }
            
            .instance-header {
                flex-direction: column;
                align-items: flex-start;
                gap: 10px;
            }
        }
        
        .qr-code {
            text-align: center;
            padding: 20px;
        }
        
        .qr-code img {
            max-width: 300px;
            border: 2px solid #ddd;
            border-radius: 10px;
        }
        
        .system-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            border: 2px solid #ddd;
        }
        
        .stat-number {
            font-size: 2.5em;
            font-weight: bold;
            color: #25D366;
            margin-bottom: 10px;
        }
        
        .stat-label {
            color: #666;
            font-weight: 600;
        }
        
        .hidden {
            display: none;
        }
        
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #25D366;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* QR 码模态框样式 */
        .qr-modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            backdrop-filter: blur(5px);
        }

        .qr-modal.show {
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .qr-modal-content {
            background: white;
            border-radius: 15px;
            padding: 30px;
            max-width: 500px;
            width: 90%;
            text-align: center;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
            animation: modalSlideIn 0.3s ease-out;
        }

        @keyframes modalSlideIn {
            from {
                opacity: 0;
                transform: translateY(-50px) scale(0.9);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        .qr-modal-header {
            margin-bottom: 20px;
        }

        .qr-modal-title {
            font-size: 1.5em;
            color: #075E54;
            margin-bottom: 10px;
        }

        .qr-modal-subtitle {
            color: #666;
            font-size: 1em;
        }

        .qr-code-container {
            margin: 20px 0;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
            border: 2px dashed #ddd;
        }

        .qr-code-image {
            max-width: 300px;
            width: 100%;
            height: auto;
            border-radius: 8px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .qr-loading {
            padding: 40px;
            color: #666;
        }

        .qr-error {
            padding: 20px;
            color: #dc3545;
            background: #f8d7da;
            border-radius: 8px;
            margin: 10px 0;
        }

        .qr-instructions {
            background: #e7f3ff;
            border: 1px solid #b3d9ff;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
            color: #0066cc;
        }

        .qr-instructions ol {
            text-align: left;
            margin: 10px 0;
            padding-left: 20px;
        }

        .qr-instructions li {
            margin: 5px 0;
        }

        .qr-modal-actions {
            display: flex;
            gap: 10px;
            justify-content: center;
            margin-top: 20px;
        }

        .qr-close-btn {
            position: absolute;
            top: 15px;
            right: 20px;
            background: none;
            border: none;
            font-size: 24px;
            color: #999;
            cursor: pointer;
            padding: 5px;
            border-radius: 50%;
            width: 35px;
            height: 35px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .qr-close-btn:hover {
            background: #f0f0f0;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 WhatsApp 多账户 API 测试工具</h1>
            <p>支持多个 WhatsApp 账户同时管理，每个账户可配置独立的网络代理</p>
        </div>

        <div class="version-selector">
            <a href="index.html" class="version-btn">📱 单账户版本</a>
            <a href="multiaccount.html" class="version-btn current">🚀 多账户版本</a>
            <a href="app.html" class="version-btn">🔧 完整功能版本</a>
        </div>

        <div class="nav-tabs">
            <button class="nav-tab active" onclick="showTab('dashboard')">📊 仪表板</button>
            <button class="nav-tab" onclick="showTab('instances')">📱 实例管理</button>
            <button class="nav-tab" onclick="showTab('create')">➕ 创建实例</button>
            <button class="nav-tab" onclick="showTab('proxy')">🌐 代理测试</button>
            <button class="nav-tab" onclick="showTab('messages')">💬 消息发送</button>
            <button class="nav-tab" onclick="showTab('settings')">⚙️ 设置</button>
        </div>

        <!-- 仪表板标签页 -->
        <div id="dashboard" class="tab-content active">
            <div class="section">
                <h3>📊 系统状态</h3>
                <div id="systemStats" class="system-stats">
                    <div class="stat-card">
                        <div class="stat-number" id="totalInstances">-</div>
                        <div class="stat-label">总实例数</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" id="activeInstances">-</div>
                        <div class="stat-label">活跃实例</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" id="connectedInstances">-</div>
                        <div class="stat-label">已连接实例</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-number" id="systemUptime">-</div>
                        <div class="stat-label">系统运行时间</div>
                    </div>
                </div>
                <button class="btn" onclick="refreshSystemStatus()">🔄 刷新状态</button>
                <div id="systemStatusResult" class="status hidden"></div>
            </div>

            <div class="section">
                <h3>📱 实例概览</h3>
                <div id="instancesOverview">
                    <p>正在加载实例信息...</p>
                </div>
                <button class="btn" onclick="refreshInstances()">🔄 刷新实例</button>
            </div>
        </div>

        <!-- 实例管理标签页 -->
        <div id="instances" class="tab-content">
            <div class="section">
                <h3>📱 实例管理</h3>
                <div id="instancesList">
                    <p>正在加载实例列表...</p>
                </div>
                <button class="btn" onclick="loadInstances()">🔄 刷新列表</button>
            </div>
        </div>

        <!-- 创建实例标签页 -->
        <div id="create" class="tab-content">
            <div class="section">
                <h3>➕ 创建新实例</h3>
                <div class="grid-2">
                    <div>
                        <div class="form-group">
                            <label for="instanceName">实例名称</label>
                            <input type="text" id="instanceName" placeholder="例如: 客服账户1">
                        </div>
                        <div class="form-group">
                            <label for="autoReply">自动回复消息</label>
                            <textarea id="autoReply" rows="3" placeholder="感谢您的消息，我们会尽快回复"></textarea>
                        </div>
                        <div class="form-group">
                            <label>
                                <input type="checkbox" id="autoMarkRead"> 自动标记已读
                            </label>
                        </div>
                        <div class="form-group">
                            <label>
                                <input type="checkbox" id="chatStorage" checked> 启用聊天存储
                            </label>
                        </div>
                        <div class="form-group">
                            <label>
                                <input type="checkbox" id="debugMode"> 调试模式
                            </label>
                        </div>
                    </div>
                    <div>
                        <div class="form-group">
                            <label>
                                <input type="checkbox" id="enableProxy"> 启用代理
                            </label>
                        </div>
                        <div id="proxyConfig" class="hidden">
                            <div class="form-group">
                                <label for="proxyType">代理类型</label>
                                <select id="proxyType">
                                    <option value="socks5">SOCKS5</option>
                                    <option value="http">HTTP</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="proxyHost">代理主机</label>
                                <input type="text" id="proxyHost" placeholder="127.0.0.1">
                            </div>
                            <div class="form-group">
                                <label for="proxyPort">代理端口</label>
                                <input type="number" id="proxyPort" placeholder="1080">
                            </div>
                            <div class="form-group">
                                <label for="proxyUsername">用户名 (可选)</label>
                                <input type="text" id="proxyUsername" placeholder="用户名">
                            </div>
                            <div class="form-group">
                                <label for="proxyPassword">密码 (可选)</label>
                                <input type="password" id="proxyPassword" placeholder="密码">
                            </div>
                        </div>
                    </div>
                </div>
                <button class="btn" onclick="createInstance()">🚀 创建实例</button>
                <div id="createInstanceResult" class="status hidden"></div>
            </div>
        </div>

        <!-- 代理测试标签页 -->
        <div id="proxy" class="tab-content">
            <div class="section">
                <h3>🌐 代理连接测试</h3>
                <div class="grid-2">
                    <div>
                        <div class="form-group">
                            <label for="testProxyType">代理类型</label>
                            <select id="testProxyType">
                                <option value="socks5">SOCKS5</option>
                                <option value="http">HTTP</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="testProxyHost">代理主机</label>
                            <input type="text" id="testProxyHost" placeholder="127.0.0.1">
                        </div>
                        <div class="form-group">
                            <label for="testProxyPort">代理端口</label>
                            <input type="number" id="testProxyPort" placeholder="1080">
                        </div>
                    </div>
                    <div>
                        <div class="form-group">
                            <label for="testProxyUsername">用户名 (可选)</label>
                            <input type="text" id="testProxyUsername" placeholder="用户名">
                        </div>
                        <div class="form-group">
                            <label for="testProxyPassword">密码 (可选)</label>
                            <input type="password" id="testProxyPassword" placeholder="密码">
                        </div>
                    </div>
                </div>
                <button class="btn" onclick="testProxy()">🧪 测试代理</button>
                <div id="proxyTestResult" class="status hidden"></div>
            </div>
        </div>

        <!-- 消息发送标签页 -->
        <div id="messages" class="tab-content">
            <div class="section">
                <h3>💬 消息发送</h3>
                <div class="form-group">
                    <label for="messageInstance">选择实例</label>
                    <select id="messageInstance">
                        <option value="">请选择实例</option>
                    </select>
                </div>
                <div class="grid-2">
                    <div>
                        <div class="form-group">
                            <label for="messagePhone">目标手机号</label>
                            <input type="text" id="messagePhone" placeholder="1234567890">
                        </div>
                        <div class="form-group">
                            <label for="messageText">消息内容</label>
                            <textarea id="messageText" rows="4" placeholder="输入要发送的消息"></textarea>
                        </div>
                    </div>
                    <div>
                        <div class="form-group">
                            <label>消息类型</label>
                            <select id="messageType">
                                <option value="text">文本消息</option>
                                <option value="image">图片消息</option>
                                <option value="file">文件消息</option>
                            </select>
                        </div>
                        <div class="form-group" id="fileUpload" style="display: none;">
                            <label for="messageFile">选择文件</label>
                            <input type="file" id="messageFile">
                        </div>
                    </div>
                </div>
                <button class="btn" onclick="sendMessage()">📤 发送消息</button>
                <div id="sendMessageResult" class="status hidden"></div>
            </div>
        </div>

        <!-- 设置标签页 -->
        <div id="settings" class="tab-content">
            <div class="section">
                <h3>⚙️ 系统设置</h3>
                <div class="form-group">
                    <label for="apiUrl">API 服务器地址</label>
                    <input type="text" id="apiUrl" value="http://localhost:3001">
                </div>
                <div class="form-group">
                    <label for="refreshInterval">自动刷新间隔 (秒)</label>
                    <input type="number" id="refreshInterval" value="30" min="5" max="300">
                </div>
                <div class="form-group">
                    <label>
                        <input type="checkbox" id="autoRefresh" checked> 启用自动刷新
                    </label>
                </div>
                <button class="btn" onclick="saveSettings()">💾 保存设置</button>
                <button class="btn btn-secondary" onclick="resetSettings()">🔄 重置设置</button>
                <div id="settingsResult" class="status hidden"></div>
            </div>
        </div>
    </div>

    <!-- QR 码模态框 -->
    <div id="qrModal" class="qr-modal">
        <div class="qr-modal-content">
            <button class="qr-close-btn" onclick="closeQRModal()">&times;</button>

            <div class="qr-modal-header">
                <h3 class="qr-modal-title">📱 WhatsApp 登录</h3>
                <p class="qr-modal-subtitle" id="qrInstanceName">请使用手机扫描二维码登录</p>
            </div>

            <div class="qr-code-container" id="qrCodeContainer">
                <div class="qr-loading">
                    <div class="loading"></div>
                    <p>正在生成二维码...</p>
                </div>
            </div>

            <div class="qr-instructions">
                <strong>📋 扫码步骤：</strong>
                <ol>
                    <li>打开手机上的 WhatsApp</li>
                    <li>点击右上角的三个点 ⋮</li>
                    <li>选择"已连接的设备"</li>
                    <li>点击"连接设备"</li>
                    <li>扫描上方的二维码</li>
                </ol>
            </div>

            <div class="qr-modal-actions">
                <button class="btn btn-secondary" onclick="refreshQRCode()">🔄 刷新二维码</button>
                <button class="btn btn-danger" onclick="closeQRModal()">❌ 取消登录</button>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let apiUrl = 'http://localhost:3001';
        let instances = [];
        let autoRefreshInterval = null;

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadSettings();
            refreshSystemStatus();
            refreshInstances();
            setupEventListeners();
            startAutoRefresh();

            // 设置 QR 码模态框事件
            const modal = document.getElementById('qrModal');
            modal.addEventListener('click', function(e) {
                if (e.target === modal) {
                    closeQRModal();
                }
            });

            // ESC 键关闭模态框
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Escape' && modal.classList.contains('show')) {
                    closeQRModal();
                }
            });
        });

        // 设置事件监听器
        function setupEventListeners() {
            // 代理启用复选框
            document.getElementById('enableProxy').addEventListener('change', function() {
                const proxyConfig = document.getElementById('proxyConfig');
                if (this.checked) {
                    proxyConfig.classList.remove('hidden');
                } else {
                    proxyConfig.classList.add('hidden');
                }
            });

            // 消息类型选择
            document.getElementById('messageType').addEventListener('change', function() {
                const fileUpload = document.getElementById('fileUpload');
                if (this.value === 'image' || this.value === 'file') {
                    fileUpload.style.display = 'block';
                } else {
                    fileUpload.style.display = 'none';
                }
            });

            // 自动刷新设置
            document.getElementById('autoRefresh').addEventListener('change', function() {
                if (this.checked) {
                    startAutoRefresh();
                } else {
                    stopAutoRefresh();
                }
            });
        }

        // 显示标签页
        function showTab(tabName) {
            // 隐藏所有标签页内容
            const tabContents = document.querySelectorAll('.tab-content');
            tabContents.forEach(content => content.classList.remove('active'));

            // 移除所有标签页的活跃状态
            const navTabs = document.querySelectorAll('.nav-tab');
            navTabs.forEach(tab => tab.classList.remove('active'));

            // 显示选中的标签页
            document.getElementById(tabName).classList.add('active');
            event.target.classList.add('active');

            // 根据标签页加载相应数据
            if (tabName === 'dashboard') {
                refreshSystemStatus();
                refreshInstances();
            } else if (tabName === 'instances') {
                loadInstances();
            } else if (tabName === 'messages') {
                loadInstancesForMessage();
            }
        }

        // API 请求函数
        async function apiRequest(endpoint, method = 'GET', data = null) {
            try {
                const options = {
                    method: method,
                    headers: {
                        'Content-Type': 'application/json',
                    }
                };

                if (data) {
                    options.body = JSON.stringify(data);
                }

                const response = await fetch(`${apiUrl}${endpoint}`, options);
                const result = await response.json();

                if (!response.ok) {
                    throw new Error(result.message || `HTTP ${response.status}`);
                }

                return result;
            } catch (error) {
                console.error('API 请求失败:', error);
                throw error;
            }
        }

        // 刷新系统状态
        async function refreshSystemStatus() {
            try {
                const result = await apiRequest('/api/system/status');

                if (result.code === 'SUCCESS') {
                    const stats = result.results;

                    document.getElementById('totalInstances').textContent = stats.total_instances;
                    document.getElementById('activeInstances').textContent = stats.active_instances;
                    document.getElementById('connectedInstances').textContent = stats.connected_instances;
                    document.getElementById('systemUptime').textContent = formatUptime(stats.system_info.uptime);

                    showStatus('systemStatusResult', '系统状态已更新', 'success');
                } else {
                    showStatus('systemStatusResult', '获取系统状态失败: ' + result.message, 'error');
                }
            } catch (error) {
                showStatus('systemStatusResult', '获取系统状态失败: ' + error.message, 'error');
            }
        }

        // 刷新实例列表
        async function refreshInstances() {
            try {
                const result = await apiRequest('/api/instances');

                if (result.code === 'SUCCESS') {
                    instances = result.results;
                    updateInstancesOverview();
                    showStatus('systemStatusResult', '实例列表已更新', 'success');
                } else {
                    showStatus('systemStatusResult', '获取实例列表失败: ' + result.message, 'error');
                }
            } catch (error) {
                showStatus('systemStatusResult', '获取实例列表失败: ' + error.message, 'error');
            }
        }

        // 更新实例概览
        function updateInstancesOverview() {
            const container = document.getElementById('instancesOverview');

            if (instances.length === 0) {
                container.innerHTML = '<p>暂无实例，请先创建实例。</p>';
                return;
            }

            let html = '';
            instances.forEach(instance => {
                html += `
                    <div class="instance-card">
                        <div class="instance-header">
                            <div class="instance-name">${instance.name}</div>
                            <div class="instance-status status-${instance.status}">${getStatusText(instance.status)}</div>
                        </div>
                        <div class="instance-info">
                            <div class="info-item">
                                <div class="info-label">实例ID</div>
                                <div class="info-value">${instance.id.substring(0, 8)}...</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">创建时间</div>
                                <div class="info-value">${formatDateTime(instance.created_at)}</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">最后活跃</div>
                                <div class="info-value">${formatDateTime(instance.last_active)}</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">代理状态</div>
                                <div class="info-value">${instance.proxy_config.enabled ? '已启用' : '未启用'}</div>
                            </div>
                        </div>
                        <div class="instance-actions">
                            <button class="btn" onclick="loginInstance('${instance.id}')">🔐 登录</button>
                            <button class="btn btn-secondary" onclick="getInstanceStatus('${instance.id}')">📊 状态</button>
                            <button class="btn btn-secondary" onclick="reconnectInstance('${instance.id}')">🔄 重连</button>
                            <button class="btn btn-danger" onclick="deleteInstance('${instance.id}')">🗑️ 删除</button>
                        </div>
                    </div>
                `;
            });

            container.innerHTML = html;
        }

        // 创建实例
        async function createInstance() {
            const instanceData = {
                name: document.getElementById('instanceName').value,
                config: {
                    auto_reply: document.getElementById('autoReply').value,
                    auto_mark_read: document.getElementById('autoMarkRead').checked,
                    chat_storage: document.getElementById('chatStorage').checked,
                    debug: document.getElementById('debugMode').checked
                },
                proxy_config: {
                    enabled: document.getElementById('enableProxy').checked,
                    type: document.getElementById('proxyType').value,
                    host: document.getElementById('proxyHost').value,
                    port: parseInt(document.getElementById('proxyPort').value) || 0,
                    username: document.getElementById('proxyUsername').value,
                    password: document.getElementById('proxyPassword').value
                }
            };

            if (!instanceData.name) {
                showStatus('createInstanceResult', '请输入实例名称', 'error');
                return;
            }

            try {
                const result = await apiRequest('/api/instances', 'POST', instanceData);

                if (result.code === 'SUCCESS') {
                    showStatus('createInstanceResult', '实例创建成功！ID: ' + result.results.id, 'success');
                    // 清空表单
                    document.getElementById('instanceName').value = '';
                    document.getElementById('autoReply').value = '';
                    document.getElementById('autoMarkRead').checked = false;
                    document.getElementById('chatStorage').checked = true;
                    document.getElementById('debugMode').checked = false;
                    document.getElementById('enableProxy').checked = false;
                    document.getElementById('proxyConfig').classList.add('hidden');
                    // 刷新实例列表
                    refreshInstances();
                } else {
                    showStatus('createInstanceResult', '创建实例失败: ' + result.message, 'error');
                }
            } catch (error) {
                showStatus('createInstanceResult', '创建实例失败: ' + error.message, 'error');
            }
        }

        // 测试代理
        async function testProxy() {
            const proxyData = {
                type: document.getElementById('testProxyType').value,
                host: document.getElementById('testProxyHost').value,
                port: parseInt(document.getElementById('testProxyPort').value),
                username: document.getElementById('testProxyUsername').value,
                password: document.getElementById('testProxyPassword').value
            };

            if (!proxyData.host || !proxyData.port) {
                showStatus('proxyTestResult', '请输入代理主机和端口', 'error');
                return;
            }

            try {
                const result = await apiRequest('/api/proxy/test', 'POST', proxyData);

                if (result.code === 'SUCCESS') {
                    showStatus('proxyTestResult',
                        `代理测试成功！延迟: ${result.results.latency}ms`, 'success');
                } else {
                    showStatus('proxyTestResult', '代理测试失败: ' + result.message, 'error');
                }
            } catch (error) {
                showStatus('proxyTestResult', '代理测试失败: ' + error.message, 'error');
            }
        }

        // 实例登录
        async function loginInstance(instanceId) {
            try {
                // 获取实例信息
                const instanceResult = await apiRequest(`/api/instances/${instanceId}`);
                const instanceName = instanceResult.results?.name || '未知实例';

                // 显示 QR 码模态框
                showQRModal(instanceId, instanceName);

                // 请求登录
                const result = await apiRequest(`/api/instances/${instanceId}/login`, 'POST');

                if (result.code === 'SUCCESS') {
                    // 显示 QR 码
                    if (result.results && result.results.qr_code) {
                        displayQRCode(result.results.qr_code);
                    } else {
                        showQRError('未收到二维码数据，请重试');
                    }
                } else {
                    showQRError('登录失败: ' + result.message);
                }
            } catch (error) {
                showQRError('登录失败: ' + error.message);
            }
        }

        // 实例登出
        async function logoutInstance(instanceId) {
            if (!confirm('确定要登出此实例吗？')) return;

            try {
                const result = await apiRequest(`/api/instances/${instanceId}/logout`, 'POST');

                if (result.code === 'SUCCESS') {
                    alert('实例已登出');
                    refreshInstances();
                } else {
                    alert('登出失败: ' + result.message);
                }
            } catch (error) {
                alert('登出失败: ' + error.message);
            }
        }

        // 获取实例状态
        async function getInstanceStatus(instanceId) {
            try {
                const result = await apiRequest(`/api/instances/${instanceId}/status`);

                if (result.code === 'SUCCESS') {
                    const status = result.results;
                    alert(`实例状态:\n状态: ${status.status}\n已登录: ${status.is_logged_in ? '是' : '否'}\n已连接: ${status.is_connected ? '是' : '否'}`);
                } else {
                    alert('获取状态失败: ' + result.message);
                }
            } catch (error) {
                alert('获取状态失败: ' + error.message);
            }
        }

        // 重连实例
        async function reconnectInstance(instanceId) {
            try {
                const result = await apiRequest(`/api/instances/${instanceId}/reconnect`, 'POST');

                if (result.code === 'SUCCESS') {
                    alert('重连请求已发送');
                    refreshInstances();
                } else {
                    alert('重连失败: ' + result.message);
                }
            } catch (error) {
                alert('重连失败: ' + error.message);
            }
        }

        // 删除实例
        async function deleteInstance(instanceId) {
            if (!confirm('确定要删除此实例吗？此操作不可撤销！')) return;

            try {
                const result = await apiRequest(`/api/instances/${instanceId}`, 'DELETE');

                if (result.code === 'SUCCESS') {
                    alert('实例已删除');
                    refreshInstances();
                    loadInstances();
                } else {
                    alert('删除失败: ' + result.message);
                }
            } catch (error) {
                alert('删除失败: ' + error.message);
            }
        }

        // 加载实例列表（详细页面）
        async function loadInstances() {
            try {
                const result = await apiRequest('/api/instances');

                if (result.code === 'SUCCESS') {
                    instances = result.results;
                    updateInstancesList();
                } else {
                    document.getElementById('instancesList').innerHTML =
                        `<p class="status error">获取实例列表失败: ${result.message}</p>`;
                }
            } catch (error) {
                document.getElementById('instancesList').innerHTML =
                    `<p class="status error">获取实例列表失败: ${error.message}</p>`;
            }
        }

        // 更新实例列表显示
        function updateInstancesList() {
            const container = document.getElementById('instancesList');

            if (instances.length === 0) {
                container.innerHTML = '<p>暂无实例，请先创建实例。</p>';
                return;
            }

            let html = '';
            instances.forEach(instance => {
                html += `
                    <div class="instance-card">
                        <div class="instance-header">
                            <div class="instance-name">${instance.name}</div>
                            <div class="instance-status status-${instance.status}">${getStatusText(instance.status)}</div>
                        </div>
                        <div class="instance-info">
                            <div class="info-item">
                                <div class="info-label">实例ID</div>
                                <div class="info-value">${instance.id}</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">自动回复</div>
                                <div class="info-value">${instance.config.auto_reply || '未设置'}</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">自动标记已读</div>
                                <div class="info-value">${instance.config.auto_mark_read ? '是' : '否'}</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">聊天存储</div>
                                <div class="info-value">${instance.config.chat_storage ? '已启用' : '未启用'}</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">调试模式</div>
                                <div class="info-value">${instance.config.debug ? '已启用' : '未启用'}</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">代理类型</div>
                                <div class="info-value">${instance.proxy_config.enabled ? instance.proxy_config.type.toUpperCase() : '未启用'}</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">代理地址</div>
                                <div class="info-value">${instance.proxy_config.enabled ? `${instance.proxy_config.host}:${instance.proxy_config.port}` : '未配置'}</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">创建时间</div>
                                <div class="info-value">${formatDateTime(instance.created_at)}</div>
                            </div>
                        </div>
                        <div class="instance-actions">
                            <button class="btn" onclick="loginInstance('${instance.id}')">🔐 登录</button>
                            <button class="btn btn-secondary" onclick="logoutInstance('${instance.id}')">🚪 登出</button>
                            <button class="btn btn-secondary" onclick="getInstanceStatus('${instance.id}')">📊 状态</button>
                            <button class="btn btn-secondary" onclick="reconnectInstance('${instance.id}')">🔄 重连</button>
                            <button class="btn btn-danger" onclick="deleteInstance('${instance.id}')">🗑️ 删除</button>
                        </div>
                    </div>
                `;
            });

            container.innerHTML = html;
        }

        // 加载实例列表用于消息发送
        function loadInstancesForMessage() {
            const select = document.getElementById('messageInstance');
            select.innerHTML = '<option value="">请选择实例</option>';

            instances.forEach(instance => {
                const option = document.createElement('option');
                option.value = instance.id;
                option.textContent = `${instance.name} (${getStatusText(instance.status)})`;
                select.appendChild(option);
            });
        }

        // 发送消息
        async function sendMessage() {
            const instanceId = document.getElementById('messageInstance').value;
            const phone = document.getElementById('messagePhone').value;
            const message = document.getElementById('messageText').value;
            const messageType = document.getElementById('messageType').value;

            if (!instanceId) {
                showStatus('sendMessageResult', '请选择实例', 'error');
                return;
            }

            if (!phone) {
                showStatus('sendMessageResult', '请输入目标手机号', 'error');
                return;
            }

            if (!message && messageType === 'text') {
                showStatus('sendMessageResult', '请输入消息内容', 'error');
                return;
            }

            try {
                const messageData = {
                    phone: phone,
                    message: message
                };

                const result = await apiRequest(`/api/instances/${instanceId}/send/message`, 'POST', messageData);

                if (result.code === 'SUCCESS') {
                    showStatus('sendMessageResult', '消息发送成功！', 'success');
                    document.getElementById('messageText').value = '';
                } else {
                    showStatus('sendMessageResult', '消息发送失败: ' + result.message, 'error');
                }
            } catch (error) {
                showStatus('sendMessageResult', '消息发送失败: ' + error.message, 'error');
            }
        }

        // 工具函数
        function getStatusText(status) {
            const statusMap = {
                'created': '已创建',
                'connecting': '连接中',
                'connected': '已连接',
                'disconnected': '已断开',
                'error': '错误'
            };
            return statusMap[status] || status;
        }

        function formatDateTime(dateString) {
            const date = new Date(dateString);
            return date.toLocaleString('zh-CN');
        }

        function formatUptime(uptime) {
            if (!uptime) return '-';

            // 解析 Go 的 duration 格式，如 "2h30m15s"
            const match = uptime.match(/(?:(\d+)h)?(?:(\d+)m)?(?:(\d+(?:\.\d+)?)s)?/);
            if (!match) return uptime;

            const hours = parseInt(match[1]) || 0;
            const minutes = parseInt(match[2]) || 0;
            const seconds = parseFloat(match[3]) || 0;

            if (hours > 0) {
                return `${hours}小时${minutes}分钟`;
            } else if (minutes > 0) {
                return `${minutes}分钟${Math.floor(seconds)}秒`;
            } else {
                return `${Math.floor(seconds)}秒`;
            }
        }

        function showStatus(elementId, message, type) {
            const element = document.getElementById(elementId);
            element.textContent = message;
            element.className = `status ${type}`;
            element.classList.remove('hidden');

            // 3秒后自动隐藏成功消息
            if (type === 'success') {
                setTimeout(() => {
                    element.classList.add('hidden');
                }, 3000);
            }
        }

        // 设置相关函数
        function loadSettings() {
            const savedApiUrl = localStorage.getItem('apiUrl');
            const savedRefreshInterval = localStorage.getItem('refreshInterval');
            const savedAutoRefresh = localStorage.getItem('autoRefresh');

            if (savedApiUrl) {
                apiUrl = savedApiUrl;
                document.getElementById('apiUrl').value = savedApiUrl;
            }

            if (savedRefreshInterval) {
                document.getElementById('refreshInterval').value = savedRefreshInterval;
            }

            if (savedAutoRefresh !== null) {
                document.getElementById('autoRefresh').checked = savedAutoRefresh === 'true';
            }
        }

        function saveSettings() {
            apiUrl = document.getElementById('apiUrl').value;
            const refreshInterval = document.getElementById('refreshInterval').value;
            const autoRefresh = document.getElementById('autoRefresh').checked;

            localStorage.setItem('apiUrl', apiUrl);
            localStorage.setItem('refreshInterval', refreshInterval);
            localStorage.setItem('autoRefresh', autoRefresh);

            showStatus('settingsResult', '设置已保存', 'success');

            // 重新启动自动刷新
            if (autoRefresh) {
                startAutoRefresh();
            } else {
                stopAutoRefresh();
            }
        }

        function resetSettings() {
            localStorage.removeItem('apiUrl');
            localStorage.removeItem('refreshInterval');
            localStorage.removeItem('autoRefresh');

            document.getElementById('apiUrl').value = 'http://localhost:3001';
            document.getElementById('refreshInterval').value = '30';
            document.getElementById('autoRefresh').checked = true;

            apiUrl = 'http://localhost:3001';

            showStatus('settingsResult', '设置已重置', 'success');
            startAutoRefresh();
        }

        // 自动刷新功能
        function startAutoRefresh() {
            stopAutoRefresh();

            const interval = parseInt(document.getElementById('refreshInterval').value) * 1000;
            const autoRefresh = document.getElementById('autoRefresh').checked;

            if (autoRefresh && interval >= 5000) {
                autoRefreshInterval = setInterval(() => {
                    refreshSystemStatus();
                    refreshInstances();
                }, interval);
            }
        }

        function stopAutoRefresh() {
            if (autoRefreshInterval) {
                clearInterval(autoRefreshInterval);
                autoRefreshInterval = null;
            }
        }

        // QR 码相关变量
        let currentInstanceId = null;
        let qrRefreshTimer = null;

        // 显示 QR 码模态框
        function showQRModal(instanceId, instanceName) {
            currentInstanceId = instanceId;

            // 设置实例名称
            document.getElementById('qrInstanceName').textContent = `实例: ${instanceName}`;

            // 重置容器内容
            document.getElementById('qrCodeContainer').innerHTML = `
                <div class="qr-loading">
                    <div class="loading"></div>
                    <p>正在生成二维码...</p>
                </div>
            `;

            // 显示模态框
            document.getElementById('qrModal').classList.add('show');

            // 设置自动刷新定时器 (QR 码通常 30 秒过期)
            qrRefreshTimer = setTimeout(() => {
                if (currentInstanceId) {
                    refreshQRCode();
                }
            }, 25000); // 25秒后自动刷新
        }

        // 显示 QR 码
        function displayQRCode(qrCodeData) {
            const container = document.getElementById('qrCodeContainer');

            // 检查 QR 码数据格式
            let qrImageSrc = qrCodeData;
            if (!qrCodeData.startsWith('data:image/')) {
                // 如果不是 data URL，假设是 base64 编码
                qrImageSrc = `data:image/png;base64,${qrCodeData}`;
            }

            container.innerHTML = `
                <img src="${qrImageSrc}" alt="WhatsApp QR Code" class="qr-code-image" />
                <p style="margin-top: 10px; color: #666; font-size: 0.9em;">
                    ⏰ 二维码将在 30 秒后过期
                </p>
            `;
        }

        // 显示 QR 码错误
        function showQRError(message) {
            const container = document.getElementById('qrCodeContainer');
            container.innerHTML = `
                <div class="qr-error">
                    <strong>❌ 错误</strong><br>
                    ${message}
                </div>
            `;
        }

        // 刷新 QR 码
        async function refreshQRCode() {
            if (!currentInstanceId) return;

            // 显示加载状态
            document.getElementById('qrCodeContainer').innerHTML = `
                <div class="qr-loading">
                    <div class="loading"></div>
                    <p>正在刷新二维码...</p>
                </div>
            `;

            try {
                const result = await apiRequest(`/api/instances/${currentInstanceId}/login`, 'POST');

                if (result.code === 'SUCCESS' && result.results && result.results.qr_code) {
                    displayQRCode(result.results.qr_code);

                    // 重新设置定时器
                    if (qrRefreshTimer) {
                        clearTimeout(qrRefreshTimer);
                    }
                    qrRefreshTimer = setTimeout(() => {
                        if (currentInstanceId) {
                            refreshQRCode();
                        }
                    }, 25000);
                } else {
                    showQRError('刷新二维码失败: ' + (result.message || '未知错误'));
                }
            } catch (error) {
                showQRError('刷新二维码失败: ' + error.message);
            }
        }

        // 关闭 QR 码模态框
        function closeQRModal() {
            document.getElementById('qrModal').classList.remove('show');
            currentInstanceId = null;

            // 清除定时器
            if (qrRefreshTimer) {
                clearTimeout(qrRefreshTimer);
                qrRefreshTimer = null;
            }
        }


    </script>
</body>
</html>
