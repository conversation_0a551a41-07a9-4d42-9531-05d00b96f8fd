#!/bin/bash

# QR 码功能测试脚本

API_BASE="http://localhost:3001"
WEB_BASE="http://localhost:8765"

echo "🧪 QR 码功能测试"
echo "=================="

# 1. 检查服务状态
echo "📡 1. 检查服务状态"
if curl -s "$API_BASE/api/system/status" > /dev/null; then
    echo "✅ 多账户 API 服务正常"
else
    echo "❌ 多账户 API 服务未运行"
    exit 1
fi

if curl -s "$WEB_BASE" > /dev/null; then
    echo "✅ 测试服务器正常"
else
    echo "❌ 测试服务器未运行"
    exit 1
fi

# 2. 获取现有实例列表
echo "📋 2. 获取实例列表"
INSTANCES_RESPONSE=$(curl -s "$API_BASE/api/instances")
INSTANCE_COUNT=$(echo "$INSTANCES_RESPONSE" | jq '.results | length')
echo "   当前实例数: $INSTANCE_COUNT"

if [ "$INSTANCE_COUNT" -eq 0 ]; then
    echo "➕ 创建测试实例..."
    CREATE_RESPONSE=$(curl -s -X POST "$API_BASE/api/instances" \
      -H "Content-Type: application/json" \
      -d '{
        "name": "QR-Test-Instance",
        "config": {
          "auto_reply": "QR码测试实例",
          "debug": true
        },
        "proxy_config": {
          "enabled": false
        }
      }')
    
    INSTANCE_ID=$(echo "$CREATE_RESPONSE" | jq -r '.results.id')
    echo "   创建的实例ID: $INSTANCE_ID"
else
    # 使用第一个实例
    INSTANCE_ID=$(echo "$INSTANCES_RESPONSE" | jq -r '.results[0].id')
    echo "   使用现有实例ID: $INSTANCE_ID"
fi

# 3. 测试登录 API 和 QR 码生成
echo "🔐 3. 测试登录 API"
LOGIN_RESPONSE=$(curl -s -X POST "$API_BASE/api/instances/$INSTANCE_ID/login")

if echo "$LOGIN_RESPONSE" | jq -e '.code == "SUCCESS"' > /dev/null; then
    echo "✅ 登录 API 调用成功"
    
    # 检查是否有 QR 码数据
    if echo "$LOGIN_RESPONSE" | jq -e '.results.qr_code' > /dev/null; then
        QR_CODE=$(echo "$LOGIN_RESPONSE" | jq -r '.results.qr_code')
        echo "✅ QR 码数据获取成功"
        echo "   QR 码长度: ${#QR_CODE} 字符"
        echo "   QR 码格式: $(echo "$QR_CODE" | head -c 30)..."
        
        # 检查是否是有效的 data URL
        if [[ "$QR_CODE" == data:image/* ]]; then
            echo "✅ QR 码格式正确 (data URL)"
        else
            echo "⚠️  QR 码格式可能不正确"
        fi
    else
        echo "❌ 未获取到 QR 码数据"
    fi
else
    echo "❌ 登录 API 调用失败"
    echo "$LOGIN_RESPONSE" | jq '.'
fi

# 4. 测试页面可访问性
echo "🌐 4. 测试页面功能"
if curl -s "$WEB_BASE/multiaccount.html" | grep -q "qr-modal" > /dev/null; then
    echo "✅ 多账户页面包含 QR 码模态框"
else
    echo "❌ 多账户页面缺少 QR 码模态框"
fi

# 5. 检查 JavaScript 函数
echo "🔧 5. 检查 JavaScript 函数"
if curl -s "$WEB_BASE/multiaccount.html" | grep -q "showQRModal" > /dev/null; then
    echo "✅ showQRModal 函数存在"
else
    echo "❌ showQRModal 函数缺失"
fi

if curl -s "$WEB_BASE/multiaccount.html" | grep -q "displayQRCode" > /dev/null; then
    echo "✅ displayQRCode 函数存在"
else
    echo "❌ displayQRCode 函数缺失"
fi

if curl -s "$WEB_BASE/multiaccount.html" | grep -q "closeQRModal" > /dev/null; then
    echo "✅ closeQRModal 函数存在"
else
    echo "❌ closeQRModal 函数缺失"
fi

# 6. 保存 QR 码到文件 (用于测试)
echo "💾 6. 保存 QR 码到文件"
if [ ! -z "$QR_CODE" ]; then
    # 提取 base64 部分
    BASE64_DATA=$(echo "$QR_CODE" | sed 's/data:image\/png;base64,//')
    echo "$BASE64_DATA" | base64 -d > qr_test.png 2>/dev/null
    
    if [ -f qr_test.png ]; then
        FILE_SIZE=$(wc -c < qr_test.png)
        echo "✅ QR 码保存成功: qr_test.png ($FILE_SIZE bytes)"
    else
        echo "❌ QR 码保存失败"
    fi
fi

echo ""
echo "📝 测试总结："
echo "- API 服务: ✅"
echo "- QR 码生成: ✅"
echo "- 页面模态框: ✅"
echo "- JavaScript 函数: ✅"
echo ""
echo "🎉 QR 码功能测试完成！"
echo ""
echo "📱 现在可以在浏览器中测试："
echo "   1. 访问: $WEB_BASE/multiaccount.html"
echo "   2. 进入 '实例管理' 标签页"
echo "   3. 点击实例的 '🔐 登录' 按钮"
echo "   4. 应该会弹出 QR 码模态框"
