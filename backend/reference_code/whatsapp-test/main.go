package main

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
	"os"
	"path/filepath"
	"strings"
	"time"
)

// WhatsApp API 配置
const (
	WhatsAppAPIURL = "http://localhost:3000"
	Username       = "admin"
	Password       = "admin"
	ServerPort     = ":8080"
)

// API 响应结构
type APIResponse struct {
	Code    string      `json:"code"`
	Message string      `json:"message"`
	Results interface{} `json:"results,omitempty"`
}

// 发送消息请求结构
type SendMessageRequest struct {
	Phone   string `json:"phone"`
	Message string `json:"message"`
}

// 发送位置请求结构
type SendLocationRequest struct {
	Phone     string  `json:"phone"`
	Latitude  float64 `json:"latitude"`
	Longitude float64 `json:"longitude"`
}

// 发送链接请求结构
type SendLinkRequest struct {
	Phone   string `json:"phone"`
	Link    string `json:"link"`
	Caption string `json:"caption"`
}

// 发送联系人请求结构
type SendContactRequest struct {
	Phone        string `json:"phone"`
	ContactName  string `json:"contact_name"`
	ContactPhone string `json:"contact_phone"`
}

// HTTP 客户端
var httpClient = &http.Client{
	Timeout: 30 * time.Second,
}

// 创建带认证的请求
func createAuthenticatedRequest(method, url string, body io.Reader) (*http.Request, error) {
	req, err := http.NewRequest(method, url, body)
	if err != nil {
		return nil, err
	}
	
	req.SetBasicAuth(Username, Password)
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("User-Agent", "WhatsApp-Test-Client/1.0")
	
	return req, nil
}

// 代理 WhatsApp API 请求
func proxyWhatsAppAPI(w http.ResponseWriter, r *http.Request) {
	// 构造目标 URL
	targetURL := WhatsAppAPIURL + strings.TrimPrefix(r.URL.Path, "/api")
	if r.URL.RawQuery != "" {
		targetURL += "?" + r.URL.RawQuery
	}
	
	log.Printf("[DEBUG] Proxying %s %s -> %s", r.Method, r.URL.Path, targetURL)
	
	// 读取请求体
	var body io.Reader
	if r.Body != nil {
		bodyBytes, err := io.ReadAll(r.Body)
		if err != nil {
			http.Error(w, "Failed to read request body", http.StatusBadRequest)
			return
		}
		body = bytes.NewReader(bodyBytes)
	}
	
	// 创建代理请求
	proxyReq, err := createAuthenticatedRequest(r.Method, targetURL, body)
	if err != nil {
		http.Error(w, "Failed to create proxy request", http.StatusInternalServerError)
		return
	}
	
	// 发送请求
	resp, err := httpClient.Do(proxyReq)
	if err != nil {
		log.Printf("[ERROR] Proxy request failed: %v", err)
		http.Error(w, "Proxy request failed", http.StatusBadGateway)
		return
	}
	defer resp.Body.Close()
	
	// 读取响应
	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		http.Error(w, "Failed to read response", http.StatusInternalServerError)
		return
	}
	
	// 设置响应头
	w.Header().Set("Content-Type", "application/json")
	w.Header().Set("Access-Control-Allow-Origin", "*")
	w.Header().Set("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
	w.Header().Set("Access-Control-Allow-Headers", "Content-Type, Authorization")
	
	// 返回响应
	w.WriteHeader(resp.StatusCode)
	w.Write(respBody)
	
	log.Printf("[DEBUG] Response: %d %s", resp.StatusCode, string(respBody))
}

// 处理 CORS 预检请求
func handleCORS(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Access-Control-Allow-Origin", "*")
	w.Header().Set("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
	w.Header().Set("Access-Control-Allow-Headers", "Content-Type, Authorization")
	w.WriteHeader(http.StatusOK)
}

// 发送消息
func sendMessage(w http.ResponseWriter, r *http.Request) {
	if r.Method == "OPTIONS" {
		handleCORS(w, r)
		return
	}
	
	var req SendMessageRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		http.Error(w, "Invalid JSON", http.StatusBadRequest)
		return
	}
	
	// 格式化手机号
	if !strings.Contains(req.Phone, "@") {
		req.Phone += "@s.whatsapp.net"
	}
	
	// 构造请求
	jsonData, _ := json.Marshal(req)
	targetURL := WhatsAppAPIURL + "/send/message"
	
	proxyReq, err := createAuthenticatedRequest("POST", targetURL, bytes.NewBuffer(jsonData))
	if err != nil {
		http.Error(w, "Failed to create request", http.StatusInternalServerError)
		return
	}
	
	// 发送请求
	resp, err := httpClient.Do(proxyReq)
	if err != nil {
		http.Error(w, "Request failed", http.StatusBadGateway)
		return
	}
	defer resp.Body.Close()
	
	// 转发响应
	w.Header().Set("Content-Type", "application/json")
	w.Header().Set("Access-Control-Allow-Origin", "*")
	w.WriteHeader(resp.StatusCode)
	io.Copy(w, resp.Body)
}

// 发送位置
func sendLocation(w http.ResponseWriter, r *http.Request) {
	if r.Method == "OPTIONS" {
		handleCORS(w, r)
		return
	}
	
	var req SendLocationRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		http.Error(w, "Invalid JSON", http.StatusBadRequest)
		return
	}
	
	// 格式化手机号
	if !strings.Contains(req.Phone, "@") {
		req.Phone += "@s.whatsapp.net"
	}
	
	// 构造请求
	jsonData, _ := json.Marshal(req)
	targetURL := WhatsAppAPIURL + "/send/location"
	
	proxyReq, err := createAuthenticatedRequest("POST", targetURL, bytes.NewBuffer(jsonData))
	if err != nil {
		http.Error(w, "Failed to create request", http.StatusInternalServerError)
		return
	}
	
	// 发送请求
	resp, err := httpClient.Do(proxyReq)
	if err != nil {
		http.Error(w, "Request failed", http.StatusBadGateway)
		return
	}
	defer resp.Body.Close()
	
	// 转发响应
	w.Header().Set("Content-Type", "application/json")
	w.Header().Set("Access-Control-Allow-Origin", "*")
	w.WriteHeader(resp.StatusCode)
	io.Copy(w, resp.Body)
}

// 发送链接
func sendLink(w http.ResponseWriter, r *http.Request) {
	if r.Method == "OPTIONS" {
		handleCORS(w, r)
		return
	}

	var req SendLinkRequest
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		http.Error(w, "Invalid JSON", http.StatusBadRequest)
		return
	}

	// 格式化手机号
	if !strings.Contains(req.Phone, "@") {
		req.Phone += "@s.whatsapp.net"
	}

	// 构造请求
	jsonData, _ := json.Marshal(req)
	targetURL := WhatsAppAPIURL + "/send/link"

	proxyReq, err := createAuthenticatedRequest("POST", targetURL, bytes.NewBuffer(jsonData))
	if err != nil {
		http.Error(w, "Failed to create request", http.StatusInternalServerError)
		return
	}

	// 发送请求
	resp, err := httpClient.Do(proxyReq)
	if err != nil {
		http.Error(w, "Request failed", http.StatusBadGateway)
		return
	}
	defer resp.Body.Close()

	// 转发响应
	w.Header().Set("Content-Type", "application/json")
	w.Header().Set("Access-Control-Allow-Origin", "*")
	w.WriteHeader(resp.StatusCode)
	io.Copy(w, resp.Body)
}

// 静态文件服务器
func serveStaticFiles(w http.ResponseWriter, r *http.Request) {
	// 默认文件
	if r.URL.Path == "/" {
		r.URL.Path = "/app.html"
	}
	
	// 构造文件路径
	filePath := filepath.Join(".", r.URL.Path[1:])
	
	// 检查文件是否存在
	if _, err := os.Stat(filePath); os.IsNotExist(err) {
		http.NotFound(w, r)
		return
	}
	
	// 设置 CORS 头
	w.Header().Set("Access-Control-Allow-Origin", "*")
	
	// 服务文件
	http.ServeFile(w, r, filePath)
}

// 健康检查
func healthCheck(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "application/json")
	w.Header().Set("Access-Control-Allow-Origin", "*")
	
	response := APIResponse{
		Code:    "SUCCESS",
		Message: "WhatsApp Test Server is running",
		Results: map[string]interface{}{
			"server":    "Go WhatsApp Test Server",
			"version":   "1.0.0",
			"timestamp": time.Now().Unix(),
		},
	}
	
	json.NewEncoder(w).Encode(response)
}

func main() {
	// 路由设置
	http.HandleFunc("/api/", proxyWhatsAppAPI)
	http.HandleFunc("/send/message", sendMessage)
	http.HandleFunc("/send/location", sendLocation)
	http.HandleFunc("/send/link", sendLink)
	http.HandleFunc("/health", healthCheck)
	http.HandleFunc("/", serveStaticFiles)
	
	// 启动服务器
	fmt.Printf("🚀 WhatsApp API 测试服务器启动中...\n")
	fmt.Printf("📁 工作目录: %s\n", getCurrentDir())
	fmt.Printf("🌐 服务器地址: http://localhost%s\n", ServerPort)
	fmt.Printf("📄 测试页面: http://localhost%s/app.html\n", ServerPort)
	fmt.Printf("💡 API 代理: http://localhost%s/api/*\n", ServerPort)
	fmt.Printf("=" + strings.Repeat("=", 50) + "\n")
	fmt.Printf("✅ 服务器已启动在端口 %s\n", ServerPort)
	fmt.Printf("🔗 WhatsApp API: %s\n", WhatsAppAPIURL)
	fmt.Printf("🔑 认证: %s:%s\n", Username, Password)
	fmt.Printf("=" + strings.Repeat("=", 50) + "\n")
	
	log.Fatal(http.ListenAndServe(ServerPort, nil))
}

// 获取当前目录
func getCurrentDir() string {
	dir, err := os.Getwd()
	if err != nil {
		return "unknown"
	}
	return dir
}
