<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WhatsApp API 完整测试工具</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #25D366, #128C7E);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }
        
        .header {
            background: #075E54;
            color: white;
            padding: 20px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .header p {
            font-size: 1.1em;
            opacity: 0.9;
        }

        .version-selector {
            background: #128C7E;
            padding: 15px;
            text-align: center;
        }

        .version-btn {
            background: #25D366;
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 600;
            margin: 0 10px;
            text-decoration: none;
            display: inline-block;
            transition: all 0.3s ease;
        }

        .version-btn:hover {
            background: #1da851;
            transform: translateY(-2px);
        }

        .version-btn.current {
            background: #075E54;
        }
        
        .nav-tabs {
            display: flex;
            background: #f8f9fa;
            border-bottom: 1px solid #ddd;
            overflow-x: auto;
        }
        
        .nav-tab {
            padding: 15px 25px;
            cursor: pointer;
            border: none;
            background: none;
            font-size: 16px;
            font-weight: 600;
            color: #666;
            transition: all 0.3s;
            white-space: nowrap;
        }
        
        .nav-tab.active {
            background: #25D366;
            color: white;
        }
        
        .nav-tab:hover {
            background: #e9ecef;
        }
        
        .nav-tab.active:hover {
            background: #128C7E;
        }
        
        .tab-content {
            display: none;
            padding: 30px;
        }
        
        .tab-content.active {
            display: block;
        }
        
        .main-content {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 30px;
        }
        
        .section {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 25px;
            border: 1px solid #e9ecef;
        }
        
        .section h3 {
            color: #075E54;
            margin-bottom: 20px;
            font-size: 1.3em;
            border-bottom: 2px solid #25D366;
            padding-bottom: 10px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #333;
        }
        
        .form-group input,
        .form-group textarea,
        .form-group select {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.3s;
        }
        
        .form-group input:focus,
        .form-group textarea:focus,
        .form-group select:focus {
            outline: none;
            border-color: #25D366;
        }
        
        .btn {
            background: #25D366;
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 600;
            transition: all 0.3s;
            width: 100%;
            margin-top: 10px;
        }
        
        .btn:hover {
            background: #128C7E;
            transform: translateY(-2px);
        }
        
        .btn:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
        }
        
        .btn-secondary {
            background: #6c757d;
        }
        
        .btn-secondary:hover {
            background: #5a6268;
        }
        
        .btn-danger {
            background: #dc3545;
        }
        
        .btn-danger:hover {
            background: #c82333;
        }
        
        .status {
            margin-top: 20px;
            padding: 15px;
            border-radius: 8px;
            font-weight: 600;
        }
        
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        
        .qr-container {
            text-align: center;
            margin-top: 20px;
        }
        
        .qr-container img {
            max-width: 100%;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .config-section {
            grid-column: 1 / -1;
            background: #fff3cd;
            border: 1px solid #ffeaa7;
        }
        
        .config-section h3 {
            color: #856404;
            border-bottom-color: #ffc107;
        }
        
        .config-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
        }
        
        .file-input {
            position: relative;
            overflow: hidden;
            display: inline-block;
            width: 100%;
        }
        
        .file-input input[type=file] {
            position: absolute;
            left: -9999px;
        }
        
        .file-input-label {
            display: block;
            padding: 12px;
            border: 2px dashed #ddd;
            border-radius: 8px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s;
        }
        
        .file-input-label:hover {
            border-color: #25D366;
            background: #f8f9fa;
        }
        
        .contacts-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        
        .contacts-table th,
        .contacts-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }
        
        .contacts-table th {
            background: #f8f9fa;
            font-weight: 600;
        }
        
        .contacts-table tr:hover {
            background: #f8f9fa;
        }

        .groups-table, .chats-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }

        .groups-table th, .groups-table td,
        .chats-table th, .chats-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }

        .groups-table th, .chats-table th {
            background: #f8f9fa;
            font-weight: 600;
        }

        .groups-table tr:hover, .chats-table tr:hover {
            background: #f8f9fa;
        }

        .messages-container {
            max-height: 400px;
            overflow-y: auto;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 10px;
            margin-top: 10px;
            background: #fff;
        }

        .message-item {
            border-bottom: 1px solid #eee;
            padding: 10px 0;
            margin-bottom: 10px;
        }

        .message-item:last-child {
            border-bottom: none;
        }

        .message-header {
            display: flex;
            justify-content: space-between;
            margin-bottom: 5px;
        }

        .message-time {
            color: #666;
            font-size: 0.9em;
        }

        .message-content {
            background-color: #f0f0f0;
            padding: 8px;
            border-radius: 5px;
            margin-bottom: 5px;
            word-wrap: break-word;
        }

        .message-id {
            color: #999;
            font-size: 0.8em;
        }

        @media (max-width: 768px) {
            .main-content {
                grid-template-columns: 1fr;
                gap: 20px;
            }
            
            .header h1 {
                font-size: 2em;
            }
            
            .nav-tabs {
                flex-wrap: wrap;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 WhatsApp API 完整测试工具</h1>
            <p>基于 go-whatsapp-web-multidevice 项目的全功能测试界面</p>
        </div>

        <div class="version-selector">
            <a href="index.html" class="version-btn">📱 单账户版本</a>
            <a href="multiaccount.html" class="version-btn">🚀 多账户版本</a>
            <a href="app.html" class="version-btn current">🔧 完整功能版本</a>
        </div>

        <div class="nav-tabs">
            <button class="nav-tab active" onclick="showTab('config')">⚙️ 配置</button>
            <button class="nav-tab" onclick="showTab('auth')">🔐 认证</button>
            <button class="nav-tab" onclick="showTab('send')">📤 发送消息</button>
            <button class="nav-tab" onclick="showTab('media')">📎 媒体文件</button>
            <button class="nav-tab" onclick="showTab('contacts')">👥 联系人</button>
            <button class="nav-tab" onclick="showTab('groups')">👨‍👩‍👧‍👦 群组</button>
            <button class="nav-tab" onclick="showTab('account')">👤 账户</button>
            <button class="nav-tab" onclick="showTab('chats')">💬 聊天</button>
        </div>

        <!-- 配置标签页 -->
        <div id="config" class="tab-content active">
            <div class="main-content">
                <div class="section config-section">
                    <h3>⚙️ API 配置</h3>
                    <div class="config-grid">
                        <div class="form-group">
                            <label for="apiUrl">API 基础地址:</label>
                            <input type="text" id="apiUrl" value="http://localhost:3000" placeholder="http://localhost:3000">
                        </div>
                        <div class="form-group">
                            <label for="username">用户名 (Basic Auth):</label>
                            <input type="text" id="username" value="admin" placeholder="admin">
                        </div>
                        <div class="form-group">
                            <label for="password">密码 (Basic Auth):</label>
                            <input type="password" id="password" value="admin" placeholder="admin">
                        </div>
                    </div>
                    <button class="btn" onclick="testConnection()">🔗 测试连接</button>
                    <div id="connectionStatus"></div>
                </div>

                <div class="section">
                    <h3>📊 系统状态</h3>
                    <button class="btn" onclick="getDevices()">📱 获取设备信息</button>
                    <button class="btn btn-secondary" onclick="reconnect()">🔄 重新连接</button>
                    <div id="systemStatus"></div>
                </div>
            </div>
        </div>

        <!-- 认证标签页 -->
        <div id="auth" class="tab-content">
            <div class="main-content">
                <div class="section">
                    <h3>📱 WhatsApp 登录</h3>
                    <button class="btn" onclick="loginWithQR()">📷 扫码登录</button>
                    <button class="btn" onclick="loginWithCode()">🔢 配对码登录</button>
                    <button class="btn btn-danger" onclick="logout()">🚪 退出登录</button>
                    <button class="btn btn-secondary" onclick="checkLoginStatus()">📊 检查登录状态</button>

                    <div class="form-group" style="margin-top: 20px;">
                        <label for="phoneNumber">手机号 (配对码登录用):</label>
                        <input type="text" id="phoneNumber" placeholder="628912344551">
                    </div>

                    <div id="loginStatus"></div>
                    <div class="qr-container" id="qrContainer"></div>
                </div>

                <div class="section">
                    <h3>👤 用户信息</h3>
                    <button class="btn" onclick="getUserInfo()">📋 获取用户信息</button>
                    <button class="btn" onclick="getUserAvatar()">🖼️ 获取头像</button>
                    <div id="userInfo"></div>
                </div>
            </div>
        </div>

        <!-- 发送消息标签页 -->
        <div id="send" class="tab-content">
            <div class="main-content">
                <div class="section">
                    <h3>💬 发送文本消息</h3>
                    <div class="form-group">
                        <label for="messageType">消息类型:</label>
                        <select id="messageType">
                            <option value="private">私人消息</option>
                            <option value="group">群组消息</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="recipient">接收者 (手机号或群组ID):</label>
                        <input type="text" id="recipient" placeholder="628912344551 或 群组ID">
                    </div>
                    <div class="form-group">
                        <label for="message">消息内容:</label>
                        <textarea id="message" rows="4" placeholder="输入要发送的消息..."></textarea>
                    </div>
                    <div class="form-group">
                        <label for="replyMessageId">回复消息ID (可选):</label>
                        <input type="text" id="replyMessageId" placeholder="回复特定消息的ID">
                    </div>
                    <button class="btn" onclick="sendMessage()">📤 发送消息</button>
                    <div id="sendStatus"></div>
                </div>

                <div class="section">
                    <h3>📍 发送位置</h3>
                    <div class="form-group">
                        <label for="locationRecipient">接收者:</label>
                        <input type="text" id="locationRecipient" placeholder="628912344551">
                    </div>
                    <div class="form-group">
                        <label for="latitude">纬度:</label>
                        <input type="number" id="latitude" step="any" placeholder="39.9042">
                    </div>
                    <div class="form-group">
                        <label for="longitude">经度:</label>
                        <input type="number" id="longitude" step="any" placeholder="116.4074">
                    </div>
                    <button class="btn" onclick="sendLocation()">📍 发送位置</button>
                    <div id="locationStatus"></div>
                </div>

                <div class="section">
                    <h3>🔗 发送链接</h3>
                    <div class="form-group">
                        <label for="linkRecipient">接收者:</label>
                        <input type="text" id="linkRecipient" placeholder="628912344551">
                    </div>
                    <div class="form-group">
                        <label for="linkUrl">链接地址:</label>
                        <input type="url" id="linkUrl" placeholder="https://example.com">
                    </div>
                    <div class="form-group">
                        <label for="linkCaption">链接描述:</label>
                        <textarea id="linkCaption" rows="3" placeholder="链接描述..."></textarea>
                    </div>
                    <button class="btn" onclick="sendLink()">🔗 发送链接</button>
                    <div id="linkStatus"></div>
                </div>
            </div>
        </div>

        <!-- 媒体文件标签页 -->
        <div id="media" class="tab-content">
            <div class="main-content">
                <div class="section">
                    <h3>🖼️ 发送图片</h3>
                    <div class="form-group">
                        <label for="imageRecipient">接收者:</label>
                        <input type="text" id="imageRecipient" placeholder="628912344551">
                    </div>
                    <div class="form-group">
                        <label for="imageFile">选择图片:</label>
                        <div class="file-input">
                            <input type="file" id="imageFile" accept="image/*">
                            <label for="imageFile" class="file-input-label">
                                📷 点击选择图片文件 (JPG/PNG)
                            </label>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="imageCaption">图片描述:</label>
                        <textarea id="imageCaption" rows="3" placeholder="图片描述..."></textarea>
                    </div>
                    <button class="btn" onclick="sendImage()">🖼️ 发送图片</button>
                    <div id="imageStatus"></div>
                </div>

                <div class="section">
                    <h3>🎵 发送音频</h3>
                    <div class="form-group">
                        <label for="audioRecipient">接收者:</label>
                        <input type="text" id="audioRecipient" placeholder="628912344551">
                    </div>
                    <div class="form-group">
                        <label for="audioFile">选择音频:</label>
                        <div class="file-input">
                            <input type="file" id="audioFile" accept="audio/*">
                            <label for="audioFile" class="file-input-label">
                                🎵 点击选择音频文件
                            </label>
                        </div>
                    </div>
                    <button class="btn" onclick="sendAudio()">🎵 发送音频</button>
                    <div id="audioStatus"></div>
                </div>

                <div class="section">
                    <h3>🎬 发送视频</h3>
                    <div class="form-group">
                        <label for="videoRecipient">接收者:</label>
                        <input type="text" id="videoRecipient" placeholder="628912344551">
                    </div>
                    <div class="form-group">
                        <label for="videoFile">选择视频:</label>
                        <div class="file-input">
                            <input type="file" id="videoFile" accept="video/*">
                            <label for="videoFile" class="file-input-label">
                                🎬 点击选择视频文件 (MP4, 最大100MB)
                            </label>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="videoCaption">视频描述:</label>
                        <textarea id="videoCaption" rows="3" placeholder="视频描述..."></textarea>
                    </div>
                    <button class="btn" onclick="sendVideo()">🎬 发送视频</button>
                    <div id="videoStatus"></div>
                </div>

                <div class="section">
                    <h3>📄 发送文件</h3>
                    <div class="form-group">
                        <label for="fileRecipient">接收者:</label>
                        <input type="text" id="fileRecipient" placeholder="628912344551">
                    </div>
                    <div class="form-group">
                        <label for="documentFile">选择文件:</label>
                        <div class="file-input">
                            <input type="file" id="documentFile">
                            <label for="documentFile" class="file-input-label">
                                📄 点击选择文件 (最大50MB)
                            </label>
                        </div>
                    </div>
                    <button class="btn" onclick="sendFile()">📄 发送文件</button>
                    <div id="fileStatus"></div>
                </div>
            </div>
        </div>

        <!-- 联系人标签页 -->
        <div id="contacts" class="tab-content">
            <div class="main-content">
                <div class="section">
                    <h3>👥 联系人管理</h3>
                    <button class="btn" onclick="getContacts()">📋 获取联系人列表</button>
                    <button class="btn btn-secondary" onclick="exportContacts()">📤 导出联系人</button>
                    <div id="contactsStatus"></div>
                    <div id="contactsList"></div>
                </div>

                <div class="section">
                    <h3>🔍 用户检查</h3>
                    <div class="form-group">
                        <label for="checkPhone">手机号:</label>
                        <input type="text" id="checkPhone" placeholder="628912344551">
                    </div>
                    <button class="btn" onclick="checkUser()">🔍 检查用户</button>
                    <div id="checkUserStatus"></div>
                </div>

                <div class="section">
                    <h3>📇 发送联系人</h3>
                    <div class="form-group">
                        <label for="contactRecipient">接收者:</label>
                        <input type="text" id="contactRecipient" placeholder="628912344551">
                    </div>
                    <div class="form-group">
                        <label for="contactName">联系人姓名:</label>
                        <input type="text" id="contactName" placeholder="张三">
                    </div>
                    <div class="form-group">
                        <label for="contactPhone">联系人电话:</label>
                        <input type="text" id="contactPhone" placeholder="628912344551">
                    </div>
                    <button class="btn" onclick="sendContact()">📇 发送联系人</button>
                    <div id="contactStatus"></div>
                </div>
            </div>
        </div>

        <!-- 群组标签页 -->
        <div id="groups" class="tab-content">
            <div class="main-content">
                <div class="section">
                    <h3>👨‍👩‍👧‍👦 群组管理</h3>
                    <button class="btn" onclick="getGroups()">📋 获取群组列表</button>
                    <div id="groupsStatus"></div>
                    <div id="groupsList"></div>
                </div>

                <div class="section">
                    <h3>➕ 创建群组</h3>
                    <div class="form-group">
                        <label for="groupName">群组名称:</label>
                        <input type="text" id="groupName" placeholder="我的群组">
                    </div>
                    <div class="form-group">
                        <label for="groupParticipants">群成员 (用逗号分隔):</label>
                        <textarea id="groupParticipants" rows="3" placeholder="628912344551,628912344552,628912344553"></textarea>
                    </div>
                    <button class="btn" onclick="createGroup()">➕ 创建群组</button>
                    <div id="createGroupStatus"></div>
                </div>

                <div class="section">
                    <h3>🔗 加入群组</h3>
                    <div class="form-group">
                        <label for="groupLink">群组邀请链接:</label>
                        <input type="text" id="groupLink" placeholder="https://chat.whatsapp.com/...">
                    </div>
                    <button class="btn" onclick="joinGroup()">🔗 加入群组</button>
                    <button class="btn btn-secondary" onclick="getGroupInfo()">ℹ️ 获取群组信息</button>
                    <div id="joinGroupStatus"></div>
                </div>
            </div>
        </div>

        <!-- 账户标签页 -->
        <div id="account" class="tab-content">
            <div class="main-content">
                <div class="section">
                    <h3>👤 个人资料</h3>
                    <button class="btn" onclick="getBusinessProfile()">🏢 获取商业资料</button>
                    <button class="btn" onclick="getPrivacySettings()">🔒 获取隐私设置</button>
                    <div id="profileStatus"></div>
                </div>

                <div class="section">
                    <h3>🖼️ 头像管理</h3>
                    <div class="form-group">
                        <label for="avatarFile">选择头像:</label>
                        <div class="file-input">
                            <input type="file" id="avatarFile" accept="image/*">
                            <label for="avatarFile" class="file-input-label">
                                🖼️ 点击选择头像图片
                            </label>
                        </div>
                    </div>
                    <button class="btn" onclick="changeAvatar()">🖼️ 更换头像</button>
                    <div id="avatarStatus"></div>
                </div>

                <div class="section">
                    <h3>✏️ 修改昵称</h3>
                    <div class="form-group">
                        <label for="newPushName">新昵称:</label>
                        <input type="text" id="newPushName" placeholder="输入新昵称">
                    </div>
                    <button class="btn" onclick="changePushName()">✏️ 修改昵称</button>
                    <div id="pushNameStatus"></div>
                </div>
            </div>
        </div>

        <!-- 聊天标签页 -->
        <div id="chats" class="tab-content">
            <div class="main-content">
                <div class="section">
                    <h3>💬 聊天列表</h3>
                    <button class="btn" onclick="getChatList()">📋 获取聊天列表</button>
                    <div id="chatListStatus"></div>
                    <div id="chatList"></div>
                </div>

                <div class="section">
                    <h3>📨 聊天消息</h3>
                    <div class="form-group">
                        <label for="chatJid">聊天ID:</label>
                        <input type="text" id="chatJid" placeholder="<EMAIL>">
                    </div>
                    <div class="form-group">
                        <label for="messageLimit">消息数量限制:</label>
                        <input type="number" id="messageLimit" value="20" min="1" max="100">
                    </div>
                    <button class="btn" onclick="getChatMessages()">📨 获取聊天消息</button>
                    <div id="chatMessagesStatus"></div>
                    <div id="chatMessages"></div>
                </div>

                <div class="section">
                    <h3>📌 聊天管理</h3>
                    <div class="form-group">
                        <label for="manageChatJid">聊天ID:</label>
                        <input type="text" id="manageChatJid" placeholder="<EMAIL>">
                    </div>
                    <button class="btn" onclick="pinChat()">📌 置顶聊天</button>
                    <button class="btn btn-secondary" onclick="unpinChat()">📌 取消置顶</button>
                    <div id="chatManageStatus"></div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 全局配置
        let config = {
            apiUrl: '/api',  // 使用 Go 后端代理路径
            username: 'admin',
            password: 'admin'
        };

        // 标签页切换
        function showTab(tabName) {
            // 隐藏所有标签页内容
            const tabContents = document.querySelectorAll('.tab-content');
            tabContents.forEach(tab => tab.classList.remove('active'));

            // 移除所有标签按钮的激活状态
            const tabButtons = document.querySelectorAll('.nav-tab');
            tabButtons.forEach(btn => btn.classList.remove('active'));

            // 显示选中的标签页
            document.getElementById(tabName).classList.add('active');
            event.target.classList.add('active');
        }

        // 更新配置
        function updateConfig() {
            // 保持使用代理路径，不从输入框更新
            // config.apiUrl = document.getElementById('apiUrl').value;
            config.username = document.getElementById('username').value;
            config.password = document.getElementById('password').value;
        }

        // 创建认证头
        function getAuthHeaders() {
            updateConfig();
            const headers = {
                'Content-Type': 'application/json'
            };

            // Go 后端会处理认证，这里不需要添加 Authorization 头
            return headers;
        }

        // 显示状态消息
        function showStatus(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            if (element) {
                element.innerHTML = `<div class="status ${type}">${message}</div>`;
            }
        }

        // 格式化手机号
        function formatPhoneNumber(phone) {
            if (!phone.includes('@')) {
                return phone + '@s.whatsapp.net';
            }
            return phone;
        }

        // ===== 配置和系统功能 =====

        // 测试连接
        async function testConnection() {
            showStatus('connectionStatus', '🔄 正在测试连接...', 'info');

            try {
                const response = await fetch(`${config.apiUrl}/app/devices`, {
                    headers: getAuthHeaders()
                });

                if (response.ok) {
                    const data = await response.json();
                    showStatus('connectionStatus', '✅ 连接成功！API 服务正常运行', 'success');
                } else {
                    showStatus('connectionStatus', `❌ 连接失败: ${response.status} ${response.statusText}`, 'error');
                }
            } catch (error) {
                showStatus('connectionStatus', `❌ 连接错误: ${error.message}`, 'error');
            }
        }

        // 获取设备信息
        async function getDevices() {
            showStatus('systemStatus', '🔄 正在获取设备信息...', 'info');

            try {
                const response = await fetch(`${config.apiUrl}/app/devices`, {
                    headers: getAuthHeaders()
                });

                const data = await response.json();

                if (response.ok) {
                    showStatus('systemStatus', `✅ 设备信息获取成功: ${JSON.stringify(data.results, null, 2)}`, 'success');
                } else {
                    showStatus('systemStatus', `❌ 获取设备信息失败: ${data.message}`, 'error');
                }
            } catch (error) {
                showStatus('systemStatus', `❌ 获取设备信息错误: ${error.message}`, 'error');
            }
        }

        // 重新连接
        async function reconnect() {
            showStatus('systemStatus', '🔄 正在重新连接...', 'info');

            try {
                const response = await fetch(`${config.apiUrl}/app/reconnect`, {
                    headers: getAuthHeaders()
                });

                const data = await response.json();

                if (response.ok) {
                    showStatus('systemStatus', '✅ 重新连接成功', 'success');
                } else {
                    showStatus('systemStatus', `❌ 重新连接失败: ${data.message}`, 'error');
                }
            } catch (error) {
                showStatus('systemStatus', `❌ 重新连接错误: ${error.message}`, 'error');
            }
        }

        // ===== 认证功能 =====

        // 扫码登录
        async function loginWithQR() {
            showStatus('loginStatus', '🔄 正在获取二维码...', 'info');

            try {
                const response = await fetch(`${config.apiUrl}/app/login`, {
                    headers: getAuthHeaders()
                });

                const data = await response.json();

                if (response.ok && data.code === 'SUCCESS') {
                    showStatus('loginStatus', '📷 请扫描二维码登录', 'success');
                    document.getElementById('qrContainer').innerHTML =
                        `<img src="data:image/png;base64,${data.results.qr_code}" alt="QR Code">`;
                } else if (data.code === 'ALREADY_LOGGED_IN') {
                    showStatus('loginStatus', '✅ 您已经登录了！无需重复登录', 'success');
                    document.getElementById('qrContainer').innerHTML = '';
                } else {
                    showStatus('loginStatus', `❌ 获取二维码失败: ${data.message}`, 'error');
                }
            } catch (error) {
                showStatus('loginStatus', `❌ 登录错误: ${error.message}`, 'error');
            }
        }

        // 配对码登录
        async function loginWithCode() {
            const phone = document.getElementById('phoneNumber').value;
            if (!phone) {
                showStatus('loginStatus', '❌ 请输入手机号', 'error');
                return;
            }

            showStatus('loginStatus', '🔄 正在获取配对码...', 'info');

            try {
                const response = await fetch(`${config.apiUrl}/app/login-with-code?phone=${phone}`, {
                    headers: getAuthHeaders()
                });

                const data = await response.json();

                if (response.ok && data.code === 'SUCCESS') {
                    showStatus('loginStatus', `🔢 配对码: ${data.results.code}`, 'success');
                } else {
                    showStatus('loginStatus', `❌ 获取配对码失败: ${data.message}`, 'error');
                }
            } catch (error) {
                showStatus('loginStatus', `❌ 登录错误: ${error.message}`, 'error');
            }
        }

        // 退出登录
        async function logout() {
            showStatus('loginStatus', '🔄 正在退出登录...', 'info');

            try {
                const response = await fetch(`${config.apiUrl}/app/logout`, {
                    headers: getAuthHeaders()
                });

                const data = await response.json();

                if (response.ok) {
                    showStatus('loginStatus', '✅ 已成功退出登录', 'success');
                    document.getElementById('qrContainer').innerHTML = '';
                } else {
                    showStatus('loginStatus', `❌ 退出登录失败: ${data.message}`, 'error');
                }
            } catch (error) {
                showStatus('loginStatus', `❌ 退出错误: ${error.message}`, 'error');
            }
        }

        // 检查登录状态
        async function checkLoginStatus() {
            showStatus('loginStatus', '🔄 正在检查登录状态...', 'info');

            try {
                // 使用 /user/my/privacy 端点检查登录状态
                const response = await fetch(`${config.apiUrl}/user/my/privacy`, {
                    headers: getAuthHeaders()
                });

                const data = await response.json();

                if (response.ok && data.code === 'SUCCESS') {
                    showStatus('loginStatus', '✅ 已登录：用户状态正常', 'success');
                } else {
                    showStatus('loginStatus', '❌ 未登录或连接失败', 'error');
                }
            } catch (error) {
                showStatus('loginStatus', `❌ 状态检查错误: ${error.message}`, 'error');
            }
        }

        // 获取用户信息
        async function getUserInfo() {
            showStatus('userInfo', '🔄 正在获取用户信息...', 'info');

            try {
                // 获取设备信息
                const deviceResponse = await fetch(`${config.apiUrl}/app/devices`, {
                    headers: getAuthHeaders()
                });

                const deviceData = await deviceResponse.json();

                if (deviceResponse.ok && deviceData.code === 'SUCCESS') {
                    const device = deviceData.results[0];

                    // 获取联系人信息来确认登录状态
                    const contactsResponse = await fetch(`${config.apiUrl}/user/my/contacts`, {
                        headers: getAuthHeaders()
                    });

                    const contactsData = await contactsResponse.json();
                    const contactCount = contactsData.code === 'SUCCESS' ? contactsData.results.data.length : 0;

                    const userInfoHtml = `
                        <div class="status success">
                            <strong>用户信息:</strong><br>
                            姓名: ${device.name}<br>
                            设备: ${device.device}<br>
                            联系人数量: ${contactCount}<br>
                            状态: 已登录
                        </div>
                    `;
                    document.getElementById('userInfo').innerHTML = userInfoHtml;
                } else {
                    showStatus('userInfo', `❌ 获取用户信息失败: ${deviceData.message}`, 'error');
                }
            } catch (error) {
                showStatus('userInfo', `❌ 获取用户信息错误: ${error.message}`, 'error');
            }
        }

        // 获取用户头像
        async function getUserAvatar() {
            showStatus('userInfo', '🔄 正在获取用户头像...', 'info');

            try {
                // 首先获取设备信息来获取手机号
                const deviceResponse = await fetch(`${config.apiUrl}/app/devices`, {
                    headers: getAuthHeaders()
                });

                const deviceData = await deviceResponse.json();

                if (deviceResponse.ok && deviceData.code === 'SUCCESS') {
                    const device = deviceData.results[0];
                    // 从设备ID中提取手机号 (格式: 8615659989049:<EMAIL>)
                    const phoneNumber = device.device.split(':')[0];

                    // 获取头像
                    const avatarResponse = await fetch(`${config.apiUrl}/user/avatar?phone=${phoneNumber}`, {
                        headers: getAuthHeaders()
                    });

                    const avatarData = await avatarResponse.json();

                    if (avatarResponse.ok && avatarData.code === 'SUCCESS') {
                        const avatarHtml = `
                            <div class="status success">
                                <strong>用户头像:</strong><br>
                                <img src="${avatarData.results.url}" alt="用户头像" style="max-width: 200px; border-radius: 50%; margin-top: 10px;"><br>
                                类型: ${avatarData.results.type}<br>
                                ID: ${avatarData.results.id}
                            </div>
                        `;
                        document.getElementById('userInfo').innerHTML = avatarHtml;
                    } else {
                        showStatus('userInfo', `❌ 获取头像失败: ${avatarData.message}`, 'error');
                    }
                } else {
                    showStatus('userInfo', `❌ 获取设备信息失败: ${deviceData.message}`, 'error');
                }
            } catch (error) {
                showStatus('userInfo', `❌ 获取头像错误: ${error.message}`, 'error');
            }
        }

        // ===== 发送消息功能 =====

        // 发送文本消息
        async function sendMessage() {
            const recipient = document.getElementById('recipient').value;
            const message = document.getElementById('message').value;
            const replyMessageId = document.getElementById('replyMessageId').value;

            if (!recipient || !message) {
                showStatus('sendStatus', '❌ 请填写接收者和消息内容', 'error');
                return;
            }

            showStatus('sendStatus', '🔄 正在发送消息...', 'info');

            try {
                const payload = {
                    phone: formatPhoneNumber(recipient),
                    message: message
                };

                if (replyMessageId) {
                    payload.reply_message_id = replyMessageId;
                }

                const response = await fetch(`${config.apiUrl}/send/message`, {
                    method: 'POST',
                    headers: getAuthHeaders(),
                    body: JSON.stringify(payload)
                });

                const data = await response.json();

                if (response.ok && data.code === 'SUCCESS') {
                    showStatus('sendStatus', '✅ 消息发送成功！', 'success');
                    document.getElementById('message').value = '';
                    document.getElementById('replyMessageId').value = '';
                } else {
                    showStatus('sendStatus', `❌ 发送失败: ${data.message}`, 'error');
                }
            } catch (error) {
                showStatus('sendStatus', `❌ 发送错误: ${error.message}`, 'error');
            }
        }

        // 发送位置
        async function sendLocation() {
            const recipient = document.getElementById('locationRecipient').value;
            const latitude = document.getElementById('latitude').value;
            const longitude = document.getElementById('longitude').value;

            if (!recipient || !latitude || !longitude) {
                showStatus('locationStatus', '❌ 请填写完整的位置信息', 'error');
                return;
            }

            showStatus('locationStatus', '🔄 正在发送位置...', 'info');

            try {
                const response = await fetch(`${config.apiUrl}/send/location`, {
                    method: 'POST',
                    headers: getAuthHeaders(),
                    body: JSON.stringify({
                        phone: formatPhoneNumber(recipient),
                        latitude: latitude.toString(),
                        longitude: longitude.toString()
                    })
                });

                const data = await response.json();

                if (response.ok && data.code === 'SUCCESS') {
                    showStatus('locationStatus', '✅ 位置发送成功！', 'success');
                } else {
                    showStatus('locationStatus', `❌ 发送失败: ${data.message}`, 'error');
                }
            } catch (error) {
                showStatus('locationStatus', `❌ 发送错误: ${error.message}`, 'error');
            }
        }

        // 发送链接
        async function sendLink() {
            const recipient = document.getElementById('linkRecipient').value;
            const url = document.getElementById('linkUrl').value;
            const caption = document.getElementById('linkCaption').value;

            if (!recipient || !url) {
                showStatus('linkStatus', '❌ 请填写接收者和链接地址', 'error');
                return;
            }

            showStatus('linkStatus', '🔄 正在发送链接...', 'info');

            try {
                const response = await fetch(`${config.apiUrl}/send/link`, {
                    method: 'POST',
                    headers: getAuthHeaders(),
                    body: JSON.stringify({
                        phone: formatPhoneNumber(recipient),
                        link: url,
                        caption: caption || ''
                    })
                });

                const data = await response.json();

                if (response.ok && data.code === 'SUCCESS') {
                    showStatus('linkStatus', '✅ 链接发送成功！', 'success');
                } else {
                    showStatus('linkStatus', `❌ 发送失败: ${data.message}`, 'error');
                }
            } catch (error) {
                showStatus('linkStatus', `❌ 发送错误: ${error.message}`, 'error');
            }
        }

        // 发送联系人
        async function sendContact() {
            const recipient = document.getElementById('contactRecipient').value;
            const name = document.getElementById('contactName').value;
            const phone = document.getElementById('contactPhone').value;

            if (!recipient || !name || !phone) {
                showStatus('contactStatus', '❌ 请填写完整的联系人信息', 'error');
                return;
            }

            showStatus('contactStatus', '🔄 正在发送联系人...', 'info');

            try {
                const response = await fetch(`${config.apiUrl}/send/contact`, {
                    method: 'POST',
                    headers: getAuthHeaders(),
                    body: JSON.stringify({
                        phone: formatPhoneNumber(recipient),
                        contact_name: name,
                        contact_phone: phone
                    })
                });

                const data = await response.json();

                if (response.ok && data.code === 'SUCCESS') {
                    showStatus('contactStatus', '✅ 联系人发送成功！', 'success');
                } else {
                    showStatus('contactStatus', `❌ 发送失败: ${data.message}`, 'error');
                }
            } catch (error) {
                showStatus('contactStatus', `❌ 发送错误: ${error.message}`, 'error');
            }
        }

        // ===== 媒体文件发送功能 =====

        // 发送图片
        async function sendImage() {
            const recipient = document.getElementById('imageRecipient').value;
            const fileInput = document.getElementById('imageFile');
            const caption = document.getElementById('imageCaption').value;

            if (!recipient || !fileInput.files[0]) {
                showStatus('imageStatus', '❌ 请选择接收者和图片文件', 'error');
                return;
            }

            showStatus('imageStatus', '🔄 正在发送图片...', 'info');

            try {
                const formData = new FormData();
                formData.append('phone', formatPhoneNumber(recipient));
                formData.append('image', fileInput.files[0]);
                if (caption) {
                    formData.append('caption', caption);
                }

                const headers = getAuthHeaders();
                delete headers['Content-Type']; // 让浏览器自动设置

                const response = await fetch(`${config.apiUrl}/send/image`, {
                    method: 'POST',
                    headers: headers,
                    body: formData
                });

                const data = await response.json();

                if (response.ok && data.code === 'SUCCESS') {
                    showStatus('imageStatus', '✅ 图片发送成功！', 'success');
                } else {
                    showStatus('imageStatus', `❌ 发送失败: ${data.message}`, 'error');
                }
            } catch (error) {
                showStatus('imageStatus', `❌ 发送错误: ${error.message}`, 'error');
            }
        }

        // 发送音频
        async function sendAudio() {
            const recipient = document.getElementById('audioRecipient').value;
            const fileInput = document.getElementById('audioFile');

            if (!recipient || !fileInput.files[0]) {
                showStatus('audioStatus', '❌ 请选择接收者和音频文件', 'error');
                return;
            }

            showStatus('audioStatus', '🔄 正在发送音频...', 'info');

            try {
                const formData = new FormData();
                formData.append('phone', formatPhoneNumber(recipient));
                formData.append('audio', fileInput.files[0]);

                const headers = getAuthHeaders();
                delete headers['Content-Type'];

                const response = await fetch(`${config.apiUrl}/send/audio`, {
                    method: 'POST',
                    headers: headers,
                    body: formData
                });

                const data = await response.json();

                if (response.ok && data.code === 'SUCCESS') {
                    showStatus('audioStatus', '✅ 音频发送成功！', 'success');
                } else {
                    showStatus('audioStatus', `❌ 发送失败: ${data.message}`, 'error');
                }
            } catch (error) {
                showStatus('audioStatus', `❌ 发送错误: ${error.message}`, 'error');
            }
        }

        // 发送视频
        async function sendVideo() {
            const recipient = document.getElementById('videoRecipient').value;
            const fileInput = document.getElementById('videoFile');
            const caption = document.getElementById('videoCaption').value;

            if (!recipient || !fileInput.files[0]) {
                showStatus('videoStatus', '❌ 请选择接收者和视频文件', 'error');
                return;
            }

            showStatus('videoStatus', '🔄 正在发送视频...', 'info');

            try {
                const formData = new FormData();
                formData.append('phone', formatPhoneNumber(recipient));
                formData.append('video', fileInput.files[0]);
                if (caption) {
                    formData.append('caption', caption);
                }

                const headers = getAuthHeaders();
                delete headers['Content-Type'];

                const response = await fetch(`${config.apiUrl}/send/video`, {
                    method: 'POST',
                    headers: headers,
                    body: formData
                });

                const data = await response.json();

                if (response.ok && data.code === 'SUCCESS') {
                    showStatus('videoStatus', '✅ 视频发送成功！', 'success');
                } else {
                    showStatus('videoStatus', `❌ 发送失败: ${data.message}`, 'error');
                }
            } catch (error) {
                showStatus('videoStatus', `❌ 发送错误: ${error.message}`, 'error');
            }
        }

        // 发送文件
        async function sendFile() {
            const recipient = document.getElementById('fileRecipient').value;
            const fileInput = document.getElementById('documentFile');

            if (!recipient || !fileInput.files[0]) {
                showStatus('fileStatus', '❌ 请选择接收者和文件', 'error');
                return;
            }

            showStatus('fileStatus', '🔄 正在发送文件...', 'info');

            try {
                const formData = new FormData();
                formData.append('phone', formatPhoneNumber(recipient));
                formData.append('file', fileInput.files[0]);

                const headers = getAuthHeaders();
                delete headers['Content-Type'];

                const response = await fetch(`${config.apiUrl}/send/file`, {
                    method: 'POST',
                    headers: headers,
                    body: formData
                });

                const data = await response.json();

                if (response.ok && data.code === 'SUCCESS') {
                    showStatus('fileStatus', '✅ 文件发送成功！', 'success');
                } else {
                    showStatus('fileStatus', `❌ 发送失败: ${data.message}`, 'error');
                }
            } catch (error) {
                showStatus('fileStatus', `❌ 发送错误: ${error.message}`, 'error');
            }
        }

        // ===== 联系人功能 =====

        // 获取联系人列表
        async function getContacts() {
            showStatus('contactsStatus', '🔄 正在获取联系人列表...', 'info');

            try {
                const response = await fetch(`${config.apiUrl}/user/my/contacts`, {
                    headers: getAuthHeaders()
                });

                const data = await response.json();

                if (response.ok && data.code === 'SUCCESS') {
                    showStatus('contactsStatus', '✅ 联系人列表获取成功', 'success');
                    displayContacts(data.results.data);
                } else {
                    showStatus('contactsStatus', `❌ 获取失败: ${data.message}`, 'error');
                }
            } catch (error) {
                showStatus('contactsStatus', `❌ 获取错误: ${error.message}`, 'error');
            }
        }

        // 显示联系人列表
        function displayContacts(contacts) {
            if (!contacts || contacts.length === 0) {
                document.getElementById('contactsList').innerHTML = '<div class="status info">暂无联系人</div>';
                return;
            }

            let html = '<table class="contacts-table"><thead><tr><th>姓名</th><th>JID</th></tr></thead><tbody>';
            contacts.forEach(contact => {
                html += `<tr><td>${contact.name || 'N/A'}</td><td>${contact.jid}</td></tr>`;
            });
            html += '</tbody></table>';

            document.getElementById('contactsList').innerHTML = html;
        }

        // 检查用户
        async function checkUser() {
            const phone = document.getElementById('checkPhone').value;

            if (!phone) {
                showStatus('checkUserStatus', '❌ 请输入手机号', 'error');
                return;
            }

            showStatus('checkUserStatus', '🔄 正在检查用户...', 'info');

            try {
                const response = await fetch(`${config.apiUrl}/user/check?phone=${phone}`, {
                    headers: getAuthHeaders()
                });

                const data = await response.json();

                if (response.ok && data.code === 'SUCCESS') {
                    const status = data.results.is_on_whatsapp ? '✅ 用户在 WhatsApp 上' : '❌ 用户不在 WhatsApp 上';
                    showStatus('checkUserStatus', status, data.results.is_on_whatsapp ? 'success' : 'error');
                } else {
                    showStatus('checkUserStatus', `❌ 检查失败: ${data.message}`, 'error');
                }
            } catch (error) {
                showStatus('checkUserStatus', `❌ 检查错误: ${error.message}`, 'error');
            }
        }

        // ===== 群组管理功能 =====

        // 获取群组列表
        async function getGroups() {
            showStatus('groupsStatus', '🔄 正在获取群组列表...', 'info');

            try {
                const response = await fetch(`${config.apiUrl}/user/my/groups`, {
                    headers: getAuthHeaders()
                });

                const data = await response.json();

                if (response.ok && data.code === 'SUCCESS') {
                    showStatus('groupsStatus', '✅ 群组列表获取成功', 'success');
                    displayGroups(data.results.data);
                } else {
                    showStatus('groupsStatus', `❌ 获取失败: ${data.message}`, 'error');
                }
            } catch (error) {
                showStatus('groupsStatus', `❌ 获取错误: ${error.message}`, 'error');
            }
        }

        // 显示群组列表
        function displayGroups(groups) {
            if (!groups || groups.length === 0) {
                document.getElementById('groupsList').innerHTML = '<div class="status info">暂无群组</div>';
                return;
            }

            let html = '<table class="groups-table"><thead><tr><th>群组名称</th><th>群组ID</th><th>创建时间</th></tr></thead><tbody>';
            groups.forEach(group => {
                html += `<tr><td>${group.Name || 'N/A'}</td><td>${group.JID}</td><td>${group.GroupCreated || 'N/A'}</td></tr>`;
            });
            html += '</tbody></table>';

            document.getElementById('groupsList').innerHTML = html;
        }

        // 创建群组
        async function createGroup() {
            const name = document.getElementById('groupName').value;
            const participantsText = document.getElementById('groupParticipants').value;

            if (!name || !participantsText) {
                showStatus('createGroupStatus', '❌ 请填写群组名称和成员', 'error');
                return;
            }

            const participants = participantsText.split(',').map(p => p.trim()).filter(p => p);

            showStatus('createGroupStatus', '🔄 正在创建群组...', 'info');

            try {
                const response = await fetch(`${config.apiUrl}/group`, {
                    method: 'POST',
                    headers: getAuthHeaders(),
                    body: JSON.stringify({
                        title: name,
                        participants: participants
                    })
                });

                const data = await response.json();

                if (response.ok && data.code === 'SUCCESS') {
                    showStatus('createGroupStatus', '✅ 群组创建成功！', 'success');
                    document.getElementById('groupName').value = '';
                    document.getElementById('groupParticipants').value = '';
                } else {
                    showStatus('createGroupStatus', `❌ 创建失败: ${data.message}`, 'error');
                }
            } catch (error) {
                showStatus('createGroupStatus', `❌ 创建错误: ${error.message}`, 'error');
            }
        }

        // 加入群组
        async function joinGroup() {
            const link = document.getElementById('groupLink').value;

            if (!link) {
                showStatus('joinGroupStatus', '❌ 请输入群组邀请链接', 'error');
                return;
            }

            showStatus('joinGroupStatus', '🔄 正在加入群组...', 'info');

            try {
                const response = await fetch(`${config.apiUrl}/group/join-with-link`, {
                    method: 'POST',
                    headers: getAuthHeaders(),
                    body: JSON.stringify({
                        link: link
                    })
                });

                const data = await response.json();

                if (response.ok && data.code === 'SUCCESS') {
                    showStatus('joinGroupStatus', '✅ 成功加入群组！', 'success');
                } else {
                    showStatus('joinGroupStatus', `❌ 加入失败: ${data.message}`, 'error');
                }
            } catch (error) {
                showStatus('joinGroupStatus', `❌ 加入错误: ${error.message}`, 'error');
            }
        }

        // 获取群组信息
        async function getGroupInfo() {
            const link = document.getElementById('groupLink').value;

            if (!link) {
                showStatus('joinGroupStatus', '❌ 请输入群组邀请链接', 'error');
                return;
            }

            showStatus('joinGroupStatus', '🔄 正在获取群组信息...', 'info');

            try {
                const response = await fetch(`${config.apiUrl}/group/info-from-link?link=${encodeURIComponent(link)}`, {
                    headers: getAuthHeaders()
                });

                const data = await response.json();

                if (response.ok && data.code === 'SUCCESS') {
                    const info = data.results;
                    const infoHtml = `
                        <div class="status success">
                            <strong>群组信息:</strong><br>
                            名称: ${info.name}<br>
                            ID: ${info.group_id}<br>
                            成员数: ${info.participant_count}<br>
                            创建时间: ${info.created_at}<br>
                            描述: ${info.topic || '无'}
                        </div>
                    `;
                    document.getElementById('joinGroupStatus').innerHTML = infoHtml;
                } else {
                    showStatus('joinGroupStatus', `❌ 获取失败: ${data.message}`, 'error');
                }
            } catch (error) {
                showStatus('joinGroupStatus', `❌ 获取错误: ${error.message}`, 'error');
            }
        }

        // ===== 账户管理功能 =====

        // 获取商业资料
        async function getBusinessProfile() {
            showStatus('profileStatus', '🔄 正在获取商业资料...', 'info');

            try {
                const response = await fetch(`${config.apiUrl}/user/business-profile`, {
                    headers: getAuthHeaders()
                });

                const data = await response.json();

                if (response.ok && data.code === 'SUCCESS') {
                    const profile = data.results;
                    const profileHtml = `
                        <div class="status success">
                            <strong>商业资料:</strong><br>
                            名称: ${profile.name || 'N/A'}<br>
                            描述: ${profile.description || 'N/A'}<br>
                            类别: ${profile.category || 'N/A'}<br>
                            地址: ${profile.address || 'N/A'}<br>
                            邮箱: ${profile.email || 'N/A'}<br>
                            网站: ${profile.website || 'N/A'}
                        </div>
                    `;
                    document.getElementById('profileStatus').innerHTML = profileHtml;
                } else {
                    showStatus('profileStatus', `❌ 获取失败: ${data.message}`, 'error');
                }
            } catch (error) {
                showStatus('profileStatus', `❌ 获取错误: ${error.message}`, 'error');
            }
        }

        // 获取隐私设置
        async function getPrivacySettings() {
            showStatus('profileStatus', '🔄 正在获取隐私设置...', 'info');

            try {
                const response = await fetch(`${config.apiUrl}/user/my/privacy`, {
                    headers: getAuthHeaders()
                });

                const data = await response.json();

                if (response.ok && data.code === 'SUCCESS') {
                    const privacy = data.results;
                    const privacyHtml = `
                        <div class="status success">
                            <strong>隐私设置:</strong><br>
                            群组添加: ${privacy.group_add}<br>
                            最后上线: ${privacy.last_seen || '隐藏'}<br>
                            状态: ${privacy.status}<br>
                            头像: ${privacy.profile}<br>
                            已读回执: ${privacy.read_receipts}
                        </div>
                    `;
                    document.getElementById('profileStatus').innerHTML = privacyHtml;
                } else {
                    showStatus('profileStatus', `❌ 获取失败: ${data.message}`, 'error');
                }
            } catch (error) {
                showStatus('profileStatus', `❌ 获取错误: ${error.message}`, 'error');
            }
        }

        // 修改昵称
        async function changePushName() {
            const newName = document.getElementById('newPushName').value;

            if (!newName) {
                showStatus('pushNameStatus', '❌ 请输入新昵称', 'error');
                return;
            }

            showStatus('pushNameStatus', '🔄 正在修改昵称...', 'info');

            try {
                const response = await fetch(`${config.apiUrl}/user/pushname`, {
                    method: 'POST',
                    headers: getAuthHeaders(),
                    body: JSON.stringify({
                        push_name: newName
                    })
                });

                const data = await response.json();

                if (response.ok && data.code === 'SUCCESS') {
                    showStatus('pushNameStatus', '✅ 昵称修改成功！', 'success');
                    document.getElementById('newPushName').value = '';
                } else {
                    showStatus('pushNameStatus', `❌ 修改失败: ${data.message}`, 'error');
                }
            } catch (error) {
                showStatus('pushNameStatus', `❌ 修改错误: ${error.message}`, 'error');
            }
        }

        // 更换头像
        async function changeAvatar() {
            const fileInput = document.getElementById('avatarFile');

            if (!fileInput.files[0]) {
                showStatus('avatarStatus', '❌ 请选择头像图片', 'error');
                return;
            }

            showStatus('avatarStatus', '🔄 正在更换头像...', 'info');

            try {
                const formData = new FormData();
                formData.append('avatar', fileInput.files[0]);

                const headers = getAuthHeaders();
                delete headers['Content-Type']; // 让浏览器自动设置 multipart/form-data

                const response = await fetch(`${config.apiUrl}/user/avatar`, {
                    method: 'POST',
                    headers: headers,
                    body: formData
                });

                const data = await response.json();

                if (response.ok && data.code === 'SUCCESS') {
                    showStatus('avatarStatus', '✅ 头像更换成功！', 'success');
                    fileInput.value = ''; // 清空文件选择
                } else {
                    showStatus('avatarStatus', `❌ 更换失败: ${data.message}`, 'error');
                }
            } catch (error) {
                showStatus('avatarStatus', `❌ 更换错误: ${error.message}`, 'error');
            }
        }

        // ===== 聊天管理功能 =====

        // 获取聊天列表
        async function getChatList() {
            showStatus('chatListStatus', '🔄 正在获取聊天列表...', 'info');

            try {
                const response = await fetch(`${config.apiUrl}/chats`, {
                    headers: getAuthHeaders()
                });

                const data = await response.json();

                if (response.ok && data.code === 'SUCCESS') {
                    showStatus('chatListStatus', '✅ 聊天列表获取成功', 'success');
                    displayChatList(data.results.data);
                } else {
                    showStatus('chatListStatus', `❌ 获取失败: ${data.message}`, 'error');
                }
            } catch (error) {
                showStatus('chatListStatus', `❌ 获取错误: ${error.message}`, 'error');
            }
        }

        // 显示聊天列表
        function displayChatList(chats) {
            if (!chats || chats.length === 0) {
                document.getElementById('chatList').innerHTML = '<div class="status info">暂无聊天记录</div>';
                return;
            }

            let html = '<table class="chats-table"><thead><tr><th>聊天名称</th><th>聊天ID</th><th>最后消息时间</th><th>是否置顶</th></tr></thead><tbody>';
            chats.forEach(chat => {
                html += `<tr>
                    <td>${chat.name || chat.jid}</td>
                    <td>${chat.jid}</td>
                    <td>${chat.last_message_time || 'N/A'}</td>
                    <td>${chat.pinned ? '📌' : ''}</td>
                </tr>`;
            });
            html += '</tbody></table>';

            document.getElementById('chatList').innerHTML = html;
        }

        // 获取聊天消息
        async function getChatMessages() {
            const chatJid = document.getElementById('chatJid').value;
            const limit = document.getElementById('messageLimit').value;

            if (!chatJid) {
                showStatus('chatMessagesStatus', '❌ 请输入聊天ID', 'error');
                return;
            }

            showStatus('chatMessagesStatus', '🔄 正在获取聊天消息...', 'info');

            try {
                const response = await fetch(`${config.apiUrl}/chat/${encodeURIComponent(chatJid)}/messages?limit=${limit}`, {
                    headers: getAuthHeaders()
                });

                const data = await response.json();

                if (response.ok && data.code === 'SUCCESS') {
                    showStatus('chatMessagesStatus', '✅ 聊天消息获取成功', 'success');
                    displayChatMessages(data.results.data);
                } else {
                    showStatus('chatMessagesStatus', `❌ 获取失败: ${data.message}`, 'error');
                }
            } catch (error) {
                showStatus('chatMessagesStatus', `❌ 获取错误: ${error.message}`, 'error');
            }
        }

        // 显示聊天消息
        function displayChatMessages(messages) {
            if (!messages || messages.length === 0) {
                document.getElementById('chatMessages').innerHTML = '<div class="status info">暂无消息</div>';
                return;
            }

            let html = '<div class="messages-container">';
            messages.forEach(msg => {
                html += `
                    <div class="message-item">
                        <div class="message-header">
                            <strong>${msg.sender || 'Unknown'}</strong>
                            <span class="message-time">${msg.timestamp || 'N/A'}</span>
                        </div>
                        <div class="message-content">${msg.content || msg.text || 'N/A'}</div>
                        <div class="message-id">ID: ${msg.id || 'N/A'}</div>
                    </div>
                `;
            });
            html += '</div>';

            document.getElementById('chatMessages').innerHTML = html;
        }

        // 置顶聊天
        async function pinChat() {
            const chatJid = document.getElementById('manageChatJid').value;

            if (!chatJid) {
                showStatus('chatManageStatus', '❌ 请输入聊天ID', 'error');
                return;
            }

            showStatus('chatManageStatus', '🔄 正在置顶聊天...', 'info');

            try {
                const response = await fetch(`${config.apiUrl}/chat/${encodeURIComponent(chatJid)}/pin`, {
                    method: 'POST',
                    headers: getAuthHeaders(),
                    body: JSON.stringify({
                        pinned: true
                    })
                });

                const data = await response.json();

                if (response.ok && data.code === 'SUCCESS') {
                    showStatus('chatManageStatus', '✅ 聊天置顶成功！', 'success');
                } else {
                    showStatus('chatManageStatus', `❌ 置顶失败: ${data.message}`, 'error');
                }
            } catch (error) {
                showStatus('chatManageStatus', `❌ 置顶错误: ${error.message}`, 'error');
            }
        }

        // 取消置顶聊天
        async function unpinChat() {
            const chatJid = document.getElementById('manageChatJid').value;

            if (!chatJid) {
                showStatus('chatManageStatus', '❌ 请输入聊天ID', 'error');
                return;
            }

            showStatus('chatManageStatus', '🔄 正在取消置顶...', 'info');

            try {
                const response = await fetch(`${config.apiUrl}/chat/${encodeURIComponent(chatJid)}/pin`, {
                    method: 'POST',
                    headers: getAuthHeaders(),
                    body: JSON.stringify({
                        pinned: false
                    })
                });

                const data = await response.json();

                if (response.ok && data.code === 'SUCCESS') {
                    showStatus('chatManageStatus', '✅ 取消置顶成功！', 'success');
                } else {
                    showStatus('chatManageStatus', `❌ 取消置顶失败: ${data.message}`, 'error');
                }
            } catch (error) {
                showStatus('chatManageStatus', `❌ 取消置顶错误: ${error.message}`, 'error');
            }
        }

        // 页面加载时自动测试连接
        window.onload = function() {
            setTimeout(testConnection, 1000);
        };
    </script>
</body>
</html>
