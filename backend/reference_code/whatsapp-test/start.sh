#!/bin/bash

# WhatsApp API 测试项目启动脚本

echo "🚀 WhatsApp API 测试项目启动脚本"
echo "=================================="

# 检查 Python 是否安装
if ! command -v python3 &> /dev/null; then
    echo "❌ Python3 未安装，请先安装 Python3"
    exit 1
fi

# 检查是否在正确目录
if [ ! -f "index.html" ]; then
    echo "❌ 请在包含 index.html 的目录中运行此脚本"
    exit 1
fi

echo "✅ 环境检查通过"
echo ""

# 检查 WhatsApp API 服务是否运行
echo "🔍 检查 WhatsApp API 服务状态..."
if curl -s http://localhost:3000/app/devices > /dev/null 2>&1; then
    echo "✅ WhatsApp API 服务正在运行"
else
    echo "⚠️  WhatsApp API 服务未运行"
    echo "💡 请先启动 WhatsApp API 服务："
    echo "   cd ../go-whatsapp-web-multidevice/src"
    echo "   go run . rest --port=3000 --basic-auth=admin:admin --debug=true"
    echo ""
    echo "❓ 是否继续启动测试服务器？(y/n)"
    read -r response
    if [[ ! "$response" =~ ^[Yy]$ ]]; then
        echo "🛑 已取消启动"
        exit 0
    fi
fi

echo ""
echo "🌐 启动测试网页服务器..."
echo "📄 测试页面将在 http://localhost:8080/index.html 打开"
echo "🛑 按 Ctrl+C 停止服务器"
echo ""

# 启动 Python 服务器
python3 server.py
