<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>QR 码测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        .qr-code {
            text-align: center;
            margin: 20px 0;
        }
        .qr-code img {
            max-width: 300px;
            border: 2px solid #ddd;
            border-radius: 10px;
        }
        .btn {
            background: #25D366;
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
        }
        .btn:hover {
            background: #128C7E;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 QR 码功能测试</h1>
        
        <div>
            <button class="btn" onclick="testQRCode()">📱 测试 QR 码显示</button>
            <button class="btn" onclick="testLogin()">🔐 测试实例登录</button>
        </div>
        
        <div id="result"></div>
        
        <div class="qr-code" id="qrDisplay" style="display: none;">
            <h3>📱 扫描二维码登录</h3>
            <img id="qrImage" src="" alt="QR Code">
            <p>请使用手机 WhatsApp 扫描上方二维码</p>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:3001';
        const INSTANCE_ID = '6e2d6e0f-56c8-46dd-b428-eb744720a5fa';

        async function testQRCode() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = '<p>🔄 正在获取 QR 码...</p>';
            
            try {
                const response = await fetch(`${API_BASE}/api/instances/${INSTANCE_ID}/login`, {
                    method: 'POST'
                });
                
                const result = await response.json();
                
                if (result.code === 'SUCCESS' && result.results.qr_code) {
                    // 显示 QR 码
                    const qrImage = document.getElementById('qrImage');
                    qrImage.src = result.results.qr_code;
                    document.getElementById('qrDisplay').style.display = 'block';
                    
                    resultDiv.innerHTML = `
                        <div style="background: #d4edda; padding: 15px; border-radius: 8px; color: #155724;">
                            <strong>✅ QR 码获取成功！</strong><br>
                            实例ID: ${result.results.instance_id}<br>
                            消息: ${result.results.message}
                        </div>
                    `;
                } else {
                    resultDiv.innerHTML = `
                        <div style="background: #f8d7da; padding: 15px; border-radius: 8px; color: #721c24;">
                            <strong>❌ QR 码获取失败</strong><br>
                            ${result.message || '未知错误'}
                        </div>
                    `;
                }
            } catch (error) {
                resultDiv.innerHTML = `
                    <div style="background: #f8d7da; padding: 15px; border-radius: 8px; color: #721c24;">
                        <strong>❌ 请求失败</strong><br>
                        ${error.message}
                    </div>
                `;
            }
        }

        async function testLogin() {
            // 打开多账户测试页面
            window.open('/multiaccount.html', '_blank');
        }
    </script>
</body>
</html>
