const { chromium } = require('playwright');

async function testMultiAccountQR() {
    console.log('🎭 启动 Playwright 测试多账户 QR 码功能');
    console.log('='.repeat(50));

    const browser = await chromium.launch({ 
        headless: false,  // 显示浏览器窗口
        slowMo: 1000     // 每个操作间隔1秒，便于观察
    });
    
    const context = await browser.newContext({
        viewport: { width: 1280, height: 720 }
    });
    
    const page = await context.newPage();

    try {
        // 1. 访问多账户测试页面
        console.log('📱 1. 访问多账户测试页面');
        await page.goto('http://localhost:8765/multiaccount.html');
        await page.waitForLoadState('networkidle');
        
        // 检查页面标题
        const title = await page.title();
        console.log(`   页面标题: ${title}`);
        
        // 2. 等待页面加载完成
        console.log('⏳ 2. 等待页面加载完成');
        await page.waitForSelector('.nav-tabs', { timeout: 10000 });
        console.log('   ✅ 导航标签页已加载');

        // 3. 检查系统状态
        console.log('📊 3. 检查系统状态');
        const totalInstances = await page.textContent('#totalInstances');
        const activeInstances = await page.textContent('#activeInstances');
        console.log(`   总实例数: ${totalInstances}`);
        console.log(`   活跃实例数: ${activeInstances}`);

        // 4. 切换到实例管理标签页
        console.log('📱 4. 切换到实例管理标签页');
        await page.click('button:has-text("📱 实例管理")');
        await page.waitForTimeout(2000);
        console.log('   ✅ 已切换到实例管理页面');

        // 5. 检查是否有实例，如果没有则创建
        console.log('🔍 5. 检查实例列表');
        const instancesList = await page.locator('#instancesList').textContent();
        
        if (instancesList.includes('暂无实例')) {
            console.log('   📝 没有实例，创建新实例...');
            
            // 切换到创建实例标签页
            await page.click('button:has-text("➕ 创建实例")');
            await page.waitForTimeout(1000);
            
            // 填写实例信息
            await page.fill('#instanceName', 'Playwright-Test-Instance');
            await page.fill('#autoReply', 'Playwright 自动化测试实例');
            await page.check('#debugMode');
            
            // 创建实例
            await page.click('button:has-text("🚀 创建实例")');
            await page.waitForTimeout(3000);
            
            // 等待创建成功提示
            const createResult = await page.locator('#createInstanceResult').textContent();
            console.log(`   创建结果: ${createResult}`);
            
            // 切换回实例管理页面
            await page.click('button:has-text("📱 实例管理")');
            await page.waitForTimeout(2000);
        }

        // 6. 等待实例列表加载并查找登录按钮
        console.log('🔐 6. 等待实例列表加载');
        await page.waitForSelector('#instancesList', { timeout: 10000 });
        await page.waitForTimeout(3000); // 等待实例数据加载

        // 滚动到实例列表区域
        await page.locator('#instancesList').scrollIntoViewIfNeeded();

        // 查找登录按钮
        console.log('🔍 查找实例登录按钮');
        const loginButtons = await page.locator('button:has-text("🔐 登录")');
        await page.waitForTimeout(2000);

        const loginButtonCount = await loginButtons.count();
        console.log(`   找到 ${loginButtonCount} 个登录按钮`);

        if (loginButtonCount === 0) {
            // 尝试刷新实例列表
            console.log('   🔄 没有找到登录按钮，尝试刷新实例列表');
            const refreshButton = page.locator('button:has-text("🔄 刷新列表")');
            if (await refreshButton.isVisible()) {
                await refreshButton.click();
                await page.waitForTimeout(3000);

                const newLoginButtonCount = await loginButtons.count();
                console.log(`   刷新后找到 ${newLoginButtonCount} 个登录按钮`);

                if (newLoginButtonCount === 0) {
                    throw new Error('刷新后仍然没有找到登录按钮');
                }
            } else {
                throw new Error('没有找到登录按钮且无法刷新');
            }
        }

        // 7. 确保登录按钮可见并点击
        console.log('🎯 7. 点击登录按钮');
        const firstLoginButton = loginButtons.first();

        // 滚动到按钮位置
        await firstLoginButton.scrollIntoViewIfNeeded();
        await page.waitForTimeout(1000);

        // 等待按钮可见和可点击
        await firstLoginButton.waitFor({ state: 'visible', timeout: 10000 });

        // 点击按钮
        await firstLoginButton.click();
        console.log('   ✅ 登录按钮点击成功');
        await page.waitForTimeout(3000);

        // 8. 检查 QR 码模态框是否弹出
        console.log('📱 8. 检查 QR 码模态框');
        const qrModal = page.locator('#qrModal');

        // 等待模态框出现
        try {
            await qrModal.waitFor({ state: 'visible', timeout: 10000 });
            console.log('   ✅ QR 码模态框已弹出');
        } catch (error) {
            console.log('   ⏳ 模态框未立即出现，等待更长时间...');
            await page.waitForTimeout(5000);
        }

        const isModalVisible = await qrModal.isVisible();

        if (isModalVisible) {
            console.log('   ✅ QR 码模态框确认可见');
            
            // 检查模态框内容
            const modalTitle = await page.locator('.qr-modal-title').textContent();
            console.log(`   模态框标题: ${modalTitle}`);
            
            // 等待 QR 码加载
            console.log('⏳ 等待 QR 码加载...');
            await page.waitForSelector('.qr-code-image', { timeout: 15000 });
            console.log('   ✅ QR 码图片已加载');
            
            // 检查 QR 码图片
            const qrImage = page.locator('.qr-code-image');
            const qrSrc = await qrImage.getAttribute('src');
            
            if (qrSrc && qrSrc.startsWith('data:image/')) {
                console.log('   ✅ QR 码格式正确 (data URL)');
                console.log(`   QR 码长度: ${qrSrc.length} 字符`);
            } else {
                console.log('   ❌ QR 码格式不正确');
            }
            
            // 9. 测试刷新 QR 码功能
            console.log('🔄 9. 测试刷新 QR 码功能');
            const refreshButton = page.locator('button:has-text("🔄 刷新二维码")');
            if (await refreshButton.isVisible()) {
                await refreshButton.click();
                console.log('   ✅ 点击刷新按钮成功');
                await page.waitForTimeout(3000);
            }
            
            // 10. 测试关闭模态框
            console.log('❌ 10. 测试关闭模态框');
            
            // 方法1: 点击取消按钮
            const cancelButton = page.locator('button:has-text("❌ 取消登录")');
            if (await cancelButton.isVisible()) {
                await cancelButton.click();
                await page.waitForTimeout(1000);
                
                const isModalHidden = !(await qrModal.isVisible());
                if (isModalHidden) {
                    console.log('   ✅ 通过取消按钮关闭模态框成功');
                } else {
                    console.log('   ❌ 通过取消按钮关闭模态框失败');
                }
            }
            
        } else {
            console.log('   ❌ QR 码模态框未弹出');
            
            // 检查是否有错误信息
            const errorElements = await page.locator('.status.error').all();
            for (const errorElement of errorElements) {
                const errorText = await errorElement.textContent();
                if (errorText.trim()) {
                    console.log(`   错误信息: ${errorText}`);
                }
            }
        }

        // 11. 测试 ESC 键关闭功能
        console.log('⌨️ 11. 测试 ESC 键关闭功能');
        if (loginButtonCount > 0) {
            // 重新打开模态框
            await loginButtons.first().click();
            await page.waitForTimeout(2000);
            
            // 按 ESC 键
            await page.keyboard.press('Escape');
            await page.waitForTimeout(1000);
            
            const isModalHidden = !(await qrModal.isVisible());
            if (isModalHidden) {
                console.log('   ✅ ESC 键关闭模态框成功');
            } else {
                console.log('   ❌ ESC 键关闭模态框失败');
            }
        }

        // 12. 截图保存
        console.log('📸 12. 保存测试截图');
        await page.screenshot({ 
            path: 'multiaccount_test_screenshot.png',
            fullPage: true 
        });
        console.log('   ✅ 截图已保存: multiaccount_test_screenshot.png');

        console.log('\n🎉 测试完成！');
        
    } catch (error) {
        console.error('❌ 测试过程中出现错误:', error.message);
        
        // 错误时也保存截图
        try {
            await page.screenshot({ 
                path: 'multiaccount_test_error.png',
                fullPage: true 
            });
            console.log('   📸 错误截图已保存: multiaccount_test_error.png');
        } catch (screenshotError) {
            console.error('   截图保存失败:', screenshotError.message);
        }
    } finally {
        // 保持浏览器打开一段时间以便观察
        console.log('⏳ 保持浏览器打开 10 秒以便观察...');
        await page.waitForTimeout(10000);
        
        await browser.close();
        console.log('🔚 浏览器已关闭');
    }
}

// 运行测试
testMultiAccountQR().catch(console.error);
