package main

import (
	"fmt"
	"sync"
	"time"

	"hive/backend/internal/config"
	"hive/backend/internal/engine"
	"hive/backend/internal/storage"
	"hive/backend/internal/types"

	"github.com/sirupsen/logrus"
	"gorm.io/gorm"
)

// WhatsAppEngineManager WhatsApp 引擎管理器
type WhatsAppEngineManager struct {
	engine   *engine.WhatsAppEngine
	db       *gorm.DB
	cache    storage.CacheInterface
	config   *config.Config
	running  bool
	mutex    sync.RWMutex
	logger   *logrus.Logger
}

// 全局引擎实例
var (
	waEngineManager *WhatsAppEngineManager
	waEngineOnce    sync.Once
)

// InitWhatsAppEngine 初始化 WhatsApp 引擎
func InitWhatsAppEngine(db *gorm.DB) error {
	var err error
	waEngineOnce.Do(func() {
		// 创建配置
		cfg := &config.Config{
			Server: config.ServerConfig{
				Port:         "3000",
				Host:         "0.0.0.0",
				Mode:         "release",
				ReadTimeout:  30 * time.Second,
				WriteTimeout: 30 * time.Second,
				IdleTimeout:  120 * time.Second,
			},
			Database: config.DatabaseConfig{
				Type:            "sqlite",
				URL:             "./hive.db", // 使用现有的数据库
				MaxOpenConns:    25,
				MaxIdleConns:    5,
				ConnMaxLifetime: time.Hour,
			},
			Cache: config.CacheConfig{
				MaxSize:         10000,
				DefaultTTL:      time.Hour,
				CleanupInterval: time.Minute,
			},
			Session: config.SessionConfig{
				MaxSessions:     10000,
				SessionTimeout:  24 * time.Hour,
				CleanupInterval: time.Hour,
				StoragePath:     "./sessions",
				AutoRestore:     true,
			},
			Proxy: config.ProxyConfig{
				HealthCheck: config.ProxyHealthCheckConfig{
					Interval:         30 * time.Second,
					Timeout:          10 * time.Second,
					RetryCount:       3,
					Workers:          10,
					FailureThreshold: 3,
				},
				Allocation: config.ProxyAllocationConfig{
					Strategy:        "region_affinity",
					StickySessions:  true,
					MaxRetries:      3,
					RetryDelay:      5 * time.Second,
				},
				Failover: config.ProxyFailoverConfig{
					Enabled:              true,
					Workers:              5,
					UrgentThreshold:      10 * time.Second,
					MaxFailoverAttempts:  3,
				},
			},
			Message: config.MessageConfig{
				RateLimit: config.MessageRateLimitConfig{
					Enabled:            true,
					RequestsPerMinute:  60,
					BurstSize:          10,
				},
				Queue: config.MessageQueueConfig{
					Workers:      20,
					BufferSize:   10000,
					BatchSize:    100,
					BatchTimeout: 5 * time.Second,
				},
				Retry: config.MessageRetryConfig{
					MaxAttempts:       3,
					InitialDelay:      time.Second,
					MaxDelay:          30 * time.Second,
					BackoffMultiplier: 2.0,
				},
			},
		}

		// 初始化缓存
		cache, err := storage.InitCache(cfg.Cache)
		if err != nil {
			logrus.Errorf("初始化缓存失败: %v", err)
			return
		}

		// 创建引擎
		waEngine, err := engine.NewWhatsAppEngine(cfg, db, cache)
		if err != nil {
			logrus.Errorf("创建 WhatsApp 引擎失败: %v", err)
			return
		}

		waEngineManager = &WhatsAppEngineManager{
			engine: waEngine,
			db:     db,
			cache:  cache,
			config: cfg,
			logger: logrus.New(),
		}

		// 启动引擎
		if err := waEngineManager.Start(); err != nil {
			logrus.Errorf("启动 WhatsApp 引擎失败: %v", err)
		}
	})

	return err
}

// Start 启动引擎
func (w *WhatsAppEngineManager) Start() error {
	w.mutex.Lock()
	defer w.mutex.Unlock()

	if w.running {
		return nil
	}

	if err := w.engine.Start(); err != nil {
		return fmt.Errorf("启动引擎失败: %v", err)
	}

	w.running = true
	w.logger.Info("WhatsApp 引擎启动成功")
	return nil
}

// Stop 停止引擎
func (w *WhatsAppEngineManager) Stop() error {
	w.mutex.Lock()
	defer w.mutex.Unlock()

	if !w.running {
		return nil
	}

	if err := w.engine.Stop(); err != nil {
		return fmt.Errorf("停止引擎失败: %v", err)
	}

	w.running = false
	w.logger.Info("WhatsApp 引擎已停止")
	return nil
}

// GetEngine 获取引擎实例
func GetWhatsAppEngine() *engine.WhatsAppEngine {
	if waEngineManager == nil {
		return nil
	}
	return waEngineManager.engine
}

// WhatsApp 引擎函数接口

// CreateWhatsAppSession 创建 WhatsApp 会话
func CreateWhatsAppSession(sessionID string, tenantID uint, phoneNumber string) (*types.WhatsAppSession, error) {
	engine := GetWhatsAppEngine()
	if engine == nil {
		return nil, fmt.Errorf("WhatsApp 引擎未初始化")
	}

	// 创建会话（不使用代理）
	sessionReq := &types.CreateSessionRequest{
		SessionID:   sessionID,
		TenantID:    tenantID,
		PhoneNumber: phoneNumber,
		ProxyID:     "", // 暂时不使用代理
		ProxyRegion: "local",
	}

	return engine.GetSessionManager().CreateSession(sessionReq)
}

// ConnectWhatsAppSession 连接 WhatsApp 会话
func ConnectWhatsAppSession(sessionID string) error {
	engine := GetWhatsAppEngine()
	if engine == nil {
		return fmt.Errorf("WhatsApp 引擎未初始化")
	}

	return engine.GetSessionManager().ConnectSession(sessionID)
}

// DisconnectWhatsAppSession 断开 WhatsApp 会话
func DisconnectWhatsAppSession(sessionID string) error {
	engine := GetWhatsAppEngine()
	if engine == nil {
		return fmt.Errorf("WhatsApp 引擎未初始化")
	}

	return engine.GetSessionManager().DisconnectSession(sessionID)
}

// DeleteWhatsAppSession 删除 WhatsApp 会话
func DeleteWhatsAppSession(sessionID string) error {
	engine := GetWhatsAppEngine()
	if engine == nil {
		return fmt.Errorf("WhatsApp 引擎未初始化")
	}

	return engine.GetSessionManager().DeleteSession(sessionID)
}

// GetWhatsAppSession 获取 WhatsApp 会话
func GetWhatsAppSession(sessionID string) *types.WhatsAppSession {
	engine := GetWhatsAppEngine()
	if engine == nil {
		return nil
	}

	return engine.GetSessionManager().GetSession(sessionID)
}

// SendWhatsAppMessage 发送 WhatsApp 消息
func SendWhatsAppMessage(sessionID, to, message string) (*types.MessageResult, error) {
	engine := GetWhatsAppEngine()
	if engine == nil {
		return nil, fmt.Errorf("WhatsApp 引擎未初始化")
	}

	msgReq := &types.MessageRequest{
		SessionID:   sessionID,
		To:          to,
		MessageType: types.MessageTypeText,
		Content:     message,
		Priority:    types.PriorityNormal,
	}

	return engine.GetMessageManager().SendMessage(msgReq)
}

// SendWhatsAppMessageAsync 异步发送 WhatsApp 消息
func SendWhatsAppMessageAsync(sessionID, to, message string) error {
	engine := GetWhatsAppEngine()
	if engine == nil {
		return fmt.Errorf("WhatsApp 引擎未初始化")
	}

	msgReq := &types.MessageRequest{
		SessionID:   sessionID,
		To:          to,
		MessageType: types.MessageTypeText,
		Content:     message,
		Priority:    types.PriorityNormal,
	}

	return engine.GetMessageManager().SendMessageAsync(msgReq)
}

// GetWhatsAppEngineStats 获取引擎统计信息
func GetWhatsAppEngineStats() *engine.EngineStats {
	engine := GetWhatsAppEngine()
	if engine == nil {
		return nil
	}

	return engine.GetStats()
}

// ImportProxies 导入代理
func ImportProxies(proxies []*types.ProxyInfo) (int, int, error) {
	engine := GetWhatsAppEngine()
	if engine == nil {
		return 0, 0, fmt.Errorf("WhatsApp 引擎未初始化")
	}

	imported := 0
	failed := 0

	for _, proxy := range proxies {
		if err := engine.GetProxyManager().AddProxy(proxy); err != nil {
			failed++
			continue
		}
		imported++
	}

	return imported, failed, nil
}
