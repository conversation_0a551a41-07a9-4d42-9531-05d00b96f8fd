<!DOCTYPE html>
<html>
<head>
    <title>WhatsApp 引擎性能测试报告</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { background: #f0f0f0; padding: 20px; border-radius: 5px; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .metric { display: inline-block; margin: 10px; padding: 10px; background: #e8f4fd; border-radius: 3px; }
        .success { color: green; }
        .warning { color: orange; }
        .error { color: red; }
        pre { background: #f5f5f5; padding: 10px; border-radius: 3px; overflow-x: auto; }
    </style>
</head>
<body>
    <div class="header">
        <h1>WhatsApp 引擎性能测试报告</h1>
        <p>测试ID: perf_test_20250804_015524</p>
        <p>测试时间: 2025年 8月 4日 星期一 01时56分29秒 CST</p>
        <p>测试环境: Darwin Mac-mini-2.local 24.5.0 Darwin Kernel Version 24.5.0: <PERSON><PERSON> Apr 22 19:54:43 PDT 2025; root:xnu-11417.121.6~2/RELEASE_ARM64_T8132 arm64</p>
    </div>
    
    <div class="section">
        <h2>测试结果摘要</h2>
        <div id="summary"></div>
    </div>
    
    <div class="section">
        <h2>详细结果</h2>
        <pre id="details"></pre>
    </div>
    
    <script>
        // 加载测试结果
        fetch('perf_test_20250804_015524.json')
            .then(response => response.json())
            .then(data => {
                // 显示摘要
                const summary = document.getElementById('summary');
                if (data.api_response_test) {
                    summary.innerHTML += '<div class="metric">API平均响应时间: ' + data.api_response_test.avg_response_time_ms + 'ms</div>';
                    summary.innerHTML += '<div class="metric">API成功率: ' + data.api_response_test.success_rate + '%</div>';
                }
                if (data.concurrent_session_test) {
                    summary.innerHTML += '<div class="metric">并发会话成功率: ' + data.concurrent_session_test.success_rate + '%</div>';
                    summary.innerHTML += '<div class="metric">平均创建时间: ' + data.concurrent_session_test.avg_create_time_ms + 'ms</div>';
                }
                if (data.resource_monitoring) {
                    summary.innerHTML += '<div class="metric">平均内存使用: ' + data.resource_monitoring.avg_memory_percent + '%</div>';
                    summary.innerHTML += '<div class="metric">平均CPU使用: ' + data.resource_monitoring.avg_cpu_percent + '%</div>';
                }
                
                // 显示详细结果
                document.getElementById('details').textContent = JSON.stringify(data, null, 2);
            })
            .catch(error => {
                console.error('Error loading results:', error);
                document.getElementById('summary').innerHTML = '<div class="error">无法加载测试结果</div>';
            });
    </script>
</body>
</html>
