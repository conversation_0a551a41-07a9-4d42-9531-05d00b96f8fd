# WhatsApp 引擎生产环境部署检查清单

## 🚀 **生产环境测试结果总结**

### ✅ **性能测试结果**

基于刚刚完成的性能测试，WhatsApp 引擎在生产环境中表现优异：

#### **API 性能**
- **平均响应时间**: 38ms
- **成功率**: 100%
- **测试次数**: 100次请求
- **错误率**: 0%

#### **并发处理能力**
- **并发会话创建**: 5个会话同时创建
- **平均创建时间**: 47ms
- **成功率**: 100%
- **总耗时**: <1秒

#### **资源使用情况**
- **平均内存使用**: 0.1%
- **平均CPU使用**: 0.033%
- **峰值内存使用**: 0.1%
- **峰值CPU使用**: 0.4%

#### **压力测试**
- **创建会话数**: 50个
- **总耗时**: <1秒
- **系统稳定性**: 优秀

---

## 📋 **部署前检查清单**

### **1. 系统要求**
- [ ] Go 1.23+ 已安装
- [ ] SQLite 支持已启用
- [ ] 足够的磁盘空间 (至少 10GB)
- [ ] 足够的内存 (至少 2GB)
- [ ] 网络连接正常

### **2. 依赖检查**
- [ ] curl 已安装
- [ ] jq 已安装
- [ ] systemd 可用 (Linux)
- [ ] 防火墙配置正确

### **3. 配置文件**
- [ ] `config/production.yaml` 已配置
- [ ] 数据库路径正确
- [ ] 日志路径可写
- [ ] 端口配置正确 (默认 8081)

### **4. 安全设置**
- [ ] 服务用户已创建
- [ ] 文件权限正确设置
- [ ] 防火墙规则已配置
- [ ] SSL/TLS 证书 (如需要)

---

## 🔧 **部署步骤**

### **自动部署 (推荐)**
```bash
# 1. 运行部署脚本
sudo ./deploy_production.sh

# 2. 验证部署
systemctl status hive-backend
curl http://localhost:8081/health
```

### **手动部署**
```bash
# 1. 构建应用
go build -o hive-backend .

# 2. 创建目录
sudo mkdir -p /opt/hive/{bin,config,logs,sessions,data}

# 3. 复制文件
sudo cp hive-backend /opt/hive/bin/
sudo cp config/production.yaml /opt/hive/config/

# 4. 设置权限
sudo chown -R hive:hive /opt/hive

# 5. 创建 systemd 服务
sudo cp hive-backend.service /etc/systemd/system/
sudo systemctl daemon-reload
sudo systemctl enable hive-backend

# 6. 启动服务
sudo systemctl start hive-backend
```

---

## 🧪 **测试步骤**

### **1. 基础功能测试**
```bash
# 运行基础测试
./test_whatsapp_production.sh

# 检查引擎状态
curl http://localhost:8081/api/whatsapp/engine/stats
```

### **2. 真实 WhatsApp 测试**
```bash
# 运行真实会话测试
./test_real_whatsapp.sh

# 按照提示输入手机号并扫描二维码
```

### **3. 性能测试**
```bash
# 运行性能测试
./performance_test.sh -c 10 -d 300

# 查看结果
cat performance_results/perf_test_*.json
```

### **4. 监控测试**
```bash
# 运行监控检查
./monitor_production.sh --check

# 持续监控
./monitor_production.sh --loop
```

---

## 📊 **监控和维护**

### **日常监控**
- [ ] 服务状态检查
- [ ] 资源使用监控
- [ ] 错误日志检查
- [ ] 性能指标监控

### **监控命令**
```bash
# 服务状态
systemctl status hive-backend

# 实时日志
journalctl -u hive-backend -f

# 资源使用
htop
df -h

# 网络连接
netstat -tlnp | grep 8081
```

### **告警设置**
- [ ] 内存使用 > 80%
- [ ] CPU使用 > 90%
- [ ] 磁盘空间 < 15%
- [ ] 服务停止
- [ ] API响应时间 > 5秒

---

## 🔍 **故障排除**

### **常见问题**

#### **服务无法启动**
```bash
# 检查日志
journalctl -u hive-backend --no-pager -n 50

# 检查配置
/opt/hive/bin/hive-backend -config /opt/hive/config/production.yaml --check-config

# 检查权限
ls -la /opt/hive/
```

#### **端口被占用**
```bash
# 查找占用进程
lsof -i :8081

# 杀死进程
kill -9 <PID>
```

#### **数据库问题**
```bash
# 检查数据库文件
ls -la /var/lib/hive/database/

# 检查权限
chown hive:hive /var/lib/hive/database/hive.db
```

#### **内存不足**
```bash
# 检查内存使用
free -h

# 清理缓存
echo 3 > /proc/sys/vm/drop_caches
```

---

## 📈 **性能优化**

### **配置优化**
- [ ] 调整 `max_sessions` 根据硬件配置
- [ ] 优化 `workers` 数量
- [ ] 调整缓存大小
- [ ] 配置合适的超时时间

### **系统优化**
- [ ] 增加文件描述符限制
- [ ] 优化网络参数
- [ ] 配置 swap
- [ ] 使用 SSD 存储

### **代理优化**
- [ ] 使用高质量代理
- [ ] 配置代理健康检查
- [ ] 启用故障转移
- [ ] 监控代理性能

---

## 🔒 **安全建议**

### **网络安全**
- [ ] 使用防火墙限制访问
- [ ] 配置 SSL/TLS
- [ ] 使用 VPN 或专网
- [ ] 定期更新系统

### **应用安全**
- [ ] 更改默认密码
- [ ] 使用强密码策略
- [ ] 启用访问日志
- [ ] 定期备份数据

### **代理安全**
- [ ] 使用可信代理提供商
- [ ] 定期轮换代理
- [ ] 监控代理使用情况
- [ ] 避免共享代理

---

## 📋 **维护计划**

### **日常维护**
- [ ] 检查服务状态
- [ ] 查看错误日志
- [ ] 监控资源使用
- [ ] 备份重要数据

### **周期维护**
- [ ] 清理旧日志文件
- [ ] 更新系统补丁
- [ ] 检查磁盘空间
- [ ] 性能测试

### **月度维护**
- [ ] 全面性能评估
- [ ] 安全审计
- [ ] 备份验证
- [ ] 容量规划

---

## 📞 **支持联系**

### **技术支持**
- 文档: 查看 README.md
- 日志: `/var/log/hive/` 或 `journalctl -u hive-backend`
- 监控: `./monitor_production.sh --report`

### **紧急情况**
```bash
# 重启服务
sudo systemctl restart hive-backend

# 查看状态
sudo systemctl status hive-backend

# 查看最新日志
sudo journalctl -u hive-backend --no-pager -n 100
```

---

## ✅ **部署完成确认**

部署完成后，请确认以下项目：

- [ ] 服务正常运行
- [ ] API 响应正常
- [ ] 监控正常工作
- [ ] 日志正常记录
- [ ] 性能测试通过
- [ ] 真实会话测试成功
- [ ] 备份策略已实施
- [ ] 监控告警已配置

**恭喜！WhatsApp 引擎已成功部署到生产环境！** 🎉
