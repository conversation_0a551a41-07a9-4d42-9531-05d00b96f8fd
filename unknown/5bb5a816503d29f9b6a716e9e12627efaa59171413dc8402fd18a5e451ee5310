#!/bin/bash

# WhatsApp 引擎生产环境部署脚本

set -e

# 配置
SERVICE_NAME="hive-backend"
SERVICE_USER="hive"
SERVICE_DIR="/opt/hive"
CONFIG_FILE="config/production.yaml"
LOG_DIR="/var/log/hive"
DATA_DIR="/var/lib/hive"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查权限
check_permissions() {
    if [ "$EUID" -ne 0 ]; then
        log_error "请使用 sudo 运行此脚本"
        exit 1
    fi
}

# 创建服务用户
create_service_user() {
    if ! id "$SERVICE_USER" &>/dev/null; then
        log_info "创建服务用户: $SERVICE_USER"
        useradd -r -s /bin/false -d "$SERVICE_DIR" "$SERVICE_USER"
        log_success "服务用户创建成功"
    else
        log_info "服务用户已存在: $SERVICE_USER"
    fi
}

# 创建目录结构
create_directories() {
    log_info "创建目录结构..."
    
    mkdir -p "$SERVICE_DIR"/{bin,config,logs,sessions,data}
    mkdir -p "$LOG_DIR"
    mkdir -p "$DATA_DIR"/{database,cache}
    
    # 设置权限
    chown -R "$SERVICE_USER:$SERVICE_USER" "$SERVICE_DIR"
    chown -R "$SERVICE_USER:$SERVICE_USER" "$LOG_DIR"
    chown -R "$SERVICE_USER:$SERVICE_USER" "$DATA_DIR"
    
    chmod 755 "$SERVICE_DIR"
    chmod 750 "$SERVICE_DIR"/{config,logs,sessions,data}
    chmod 755 "$LOG_DIR"
    chmod 750 "$DATA_DIR"
    
    log_success "目录结构创建完成"
}

# 构建应用
build_application() {
    log_info "构建应用..."
    
    # 确保在正确的目录
    if [ ! -f "go.mod" ]; then
        log_error "请在项目根目录运行此脚本"
        exit 1
    fi
    
    # 构建生产版本
    CGO_ENABLED=1 GOOS=linux go build -a -ldflags '-extldflags "-static"' -o "$SERVICE_NAME" .
    
    if [ $? -eq 0 ]; then
        log_success "应用构建成功"
    else
        log_error "应用构建失败"
        exit 1
    fi
}

# 部署应用
deploy_application() {
    log_info "部署应用..."
    
    # 停止现有服务
    if systemctl is-active --quiet "$SERVICE_NAME"; then
        log_info "停止现有服务..."
        systemctl stop "$SERVICE_NAME"
    fi
    
    # 复制二进制文件
    cp "$SERVICE_NAME" "$SERVICE_DIR/bin/"
    chmod 755 "$SERVICE_DIR/bin/$SERVICE_NAME"
    
    # 复制配置文件
    if [ -f "$CONFIG_FILE" ]; then
        cp "$CONFIG_FILE" "$SERVICE_DIR/config/"
    else
        log_warning "配置文件不存在: $CONFIG_FILE"
    fi
    
    # 复制其他必要文件
    if [ -d "config" ]; then
        cp -r config/* "$SERVICE_DIR/config/" 2>/dev/null || true
    fi
    
    # 设置权限
    chown -R "$SERVICE_USER:$SERVICE_USER" "$SERVICE_DIR"
    
    log_success "应用部署完成"
}

# 创建 systemd 服务
create_systemd_service() {
    log_info "创建 systemd 服务..."
    
    cat > "/etc/systemd/system/$SERVICE_NAME.service" << EOF
[Unit]
Description=Hive WhatsApp Engine
After=network.target
Wants=network.target

[Service]
Type=simple
User=$SERVICE_USER
Group=$SERVICE_USER
WorkingDirectory=$SERVICE_DIR
ExecStart=$SERVICE_DIR/bin/$SERVICE_NAME -config $SERVICE_DIR/config/production.yaml
ExecReload=/bin/kill -HUP \$MAINPID
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal
SyslogIdentifier=$SERVICE_NAME

# 安全设置
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=$SERVICE_DIR $LOG_DIR $DATA_DIR

# 资源限制
LimitNOFILE=65536
LimitNPROC=4096

# 环境变量
Environment=GIN_MODE=release
Environment=LOG_LEVEL=info

[Install]
WantedBy=multi-user.target
EOF
    
    # 重新加载 systemd
    systemctl daemon-reload
    systemctl enable "$SERVICE_NAME"
    
    log_success "systemd 服务创建完成"
}

# 配置日志轮转
setup_log_rotation() {
    log_info "配置日志轮转..."
    
    cat > "/etc/logrotate.d/$SERVICE_NAME" << EOF
$LOG_DIR/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 $SERVICE_USER $SERVICE_USER
    postrotate
        systemctl reload $SERVICE_NAME > /dev/null 2>&1 || true
    endscript
}
EOF
    
    log_success "日志轮转配置完成"
}

# 配置防火墙
setup_firewall() {
    log_info "配置防火墙..."
    
    if command -v ufw > /dev/null; then
        ufw allow 8081/tcp
        log_success "UFW 防火墙规则已添加"
    elif command -v firewall-cmd > /dev/null; then
        firewall-cmd --permanent --add-port=8081/tcp
        firewall-cmd --reload
        log_success "firewalld 防火墙规则已添加"
    else
        log_warning "未检测到防火墙，请手动开放端口 8081"
    fi
}

# 创建监控脚本
setup_monitoring() {
    log_info "设置监控..."
    
    # 复制监控脚本
    cp monitor_production.sh "$SERVICE_DIR/bin/"
    chmod 755 "$SERVICE_DIR/bin/monitor_production.sh"
    
    # 创建监控 cron 任务
    cat > "/etc/cron.d/$SERVICE_NAME-monitor" << EOF
# WhatsApp Engine 监控
*/5 * * * * $SERVICE_USER cd $SERVICE_DIR && ./bin/monitor_production.sh --check >> $LOG_DIR/monitor.log 2>&1
EOF
    
    log_success "监控设置完成"
}

# 启动服务
start_service() {
    log_info "启动服务..."
    
    systemctl start "$SERVICE_NAME"
    
    # 等待服务启动
    sleep 5
    
    if systemctl is-active --quiet "$SERVICE_NAME"; then
        log_success "服务启动成功"
        
        # 显示状态
        systemctl status "$SERVICE_NAME" --no-pager
    else
        log_error "服务启动失败"
        journalctl -u "$SERVICE_NAME" --no-pager -n 20
        exit 1
    fi
}

# 运行健康检查
health_check() {
    log_info "运行健康检查..."
    
    # 等待服务完全启动
    sleep 10
    
    # 检查端口
    if netstat -tlnp | grep :8081 > /dev/null; then
        log_success "端口 8081 正在监听"
    else
        log_error "端口 8081 未监听"
        return 1
    fi
    
    # 检查 HTTP 响应
    if curl -s http://localhost:8081/health > /dev/null; then
        log_success "HTTP 健康检查通过"
    else
        log_error "HTTP 健康检查失败"
        return 1
    fi
    
    # 运行完整测试
    if [ -f "$SERVICE_DIR/bin/test_whatsapp_production.sh" ]; then
        log_info "运行生产环境测试..."
        cd "$SERVICE_DIR"
        sudo -u "$SERVICE_USER" ./bin/test_whatsapp_production.sh --check
    fi
}

# 显示部署信息
show_deployment_info() {
    log_success "部署完成！"
    echo ""
    echo "服务信息:"
    echo "  服务名称: $SERVICE_NAME"
    echo "  服务用户: $SERVICE_USER"
    echo "  安装目录: $SERVICE_DIR"
    echo "  配置文件: $SERVICE_DIR/config/production.yaml"
    echo "  日志目录: $LOG_DIR"
    echo "  数据目录: $DATA_DIR"
    echo ""
    echo "管理命令:"
    echo "  启动服务: systemctl start $SERVICE_NAME"
    echo "  停止服务: systemctl stop $SERVICE_NAME"
    echo "  重启服务: systemctl restart $SERVICE_NAME"
    echo "  查看状态: systemctl status $SERVICE_NAME"
    echo "  查看日志: journalctl -u $SERVICE_NAME -f"
    echo ""
    echo "监控命令:"
    echo "  健康检查: $SERVICE_DIR/bin/monitor_production.sh --check"
    echo "  生成报告: $SERVICE_DIR/bin/monitor_production.sh --report"
    echo "  持续监控: $SERVICE_DIR/bin/monitor_production.sh --loop"
    echo ""
    echo "测试命令:"
    echo "  功能测试: $SERVICE_DIR/bin/test_whatsapp_production.sh"
    echo ""
    echo "访问地址:"
    echo "  健康检查: http://localhost:8081/health"
    echo "  API 文档: http://localhost:8081/api"
    echo ""
}

# 清理函数
cleanup() {
    log_info "清理临时文件..."
    rm -f "$SERVICE_NAME"
}

# 主部署流程
main() {
    log_info "开始生产环境部署..."
    
    # 检查权限
    check_permissions
    
    # 构建应用
    build_application
    
    # 创建用户和目录
    create_service_user
    create_directories
    
    # 部署应用
    deploy_application
    
    # 配置系统服务
    create_systemd_service
    setup_log_rotation
    setup_firewall
    setup_monitoring
    
    # 复制测试脚本
    cp test_whatsapp_production.sh "$SERVICE_DIR/bin/" 2>/dev/null || true
    chmod 755 "$SERVICE_DIR/bin/test_whatsapp_production.sh" 2>/dev/null || true
    
    # 启动服务
    start_service
    
    # 健康检查
    health_check
    
    # 清理
    cleanup
    
    # 显示部署信息
    show_deployment_info
}

# 显示帮助
show_help() {
    echo "WhatsApp 引擎生产环境部署脚本"
    echo ""
    echo "使用方法:"
    echo "  sudo $0 [选项]"
    echo ""
    echo "选项:"
    echo "  -h, --help     显示帮助信息"
    echo "  --user USER    设置服务用户 (默认: hive)"
    echo "  --dir DIR      设置安装目录 (默认: /opt/hive)"
    echo ""
    echo "示例:"
    echo "  sudo $0                    # 使用默认配置部署"
    echo "  sudo $0 --user myuser      # 使用自定义用户"
    echo ""
}

# 解析命令行参数
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        --user)
            SERVICE_USER="$2"
            shift 2
            ;;
        --dir)
            SERVICE_DIR="$2"
            shift 2
            ;;
        *)
            log_error "未知参数: $1"
            show_help
            exit 1
            ;;
    esac
done

# 运行部署
main
