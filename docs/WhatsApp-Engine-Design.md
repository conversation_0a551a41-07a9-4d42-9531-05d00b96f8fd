# WhatsApp 引擎完整设计文档

## 📋 **项目概述**

基于 whatsmeow 库开发的高性能、可扩展的 WhatsApp 引擎，支持大规模账号管理、智能代理网络、历史记录查询等功能。

## 🏗️ **整体架构**

```mermaid
graph TB
    subgraph "WhatsApp 引擎核心"
        API[API 接口层]
        SM[会话管理器]
        PM[代理管理器]
        MM[消息管理器]
        HM[历史记录管理器]
        FM[文件管理器]
        Monitor[监控系统]
    end
    
    subgraph "代理网络层"
        PG[代理分组管理]
        PC[代理健康检查]
        PA[代理分配器]
        PB[代理负载均衡]
    end
    
    subgraph "存储层"
        DB[(SQLite 数据库)]
        Cache[(Redis 缓存)]
        Files[文件存储]
        Sessions[会话存储]
    end
    
    subgraph "外部服务"
        WA[WhatsApp 服务器]
        Proxies[代理服务器集群]
    end
    
    API --> SM
    API --> PM
    API --> MM
    API --> HM
    
    SM --> PA
    MM --> PA
    
    PM --> PG
    PM --> PC
    PM --> PB
    
    PA --> Proxies
    SM --> WA
    MM --> WA
    
    SM --> DB
    PM --> DB
    HM --> DB
    
    SM --> Cache
    PM --> Cache
    
    Monitor --> SM
    Monitor --> PM
    Monitor --> PC
```

## 🔧 **核心功能模块**

### **1. 会话管理器 (Session Manager)**

```go
type SessionManager struct {
    sessions    sync.Map                    // sessionID -> *WhatsAppSession
    clients     sync.Map                    // sessionID -> *whatsmeow.Client
    proxyMgr    *ProxyManager              // 代理管理器
    storage     SessionStorage             // 会话存储
    config      *SessionConfig             // 会话配置
    eventBus    chan *SessionEvent         // 事件总线
    metrics     *SessionMetrics            // 会话指标
}

type WhatsAppSession struct {
    ID              string                 `json:"session_id"`
    TenantID        uint                   `json:"tenant_id"`
    PhoneNumber     string                 `json:"phone_number"`
    Status          SessionStatus          `json:"status"`
    Client          *whatsmeow.Client      `json:"-"`
    Device          *store.Device          `json:"-"`
    ProxyID         string                 `json:"proxy_id"`
    ProxyRegion     string                 `json:"proxy_region"`
    CreatedAt       time.Time              `json:"created_at"`
    UpdatedAt       time.Time              `json:"updated_at"`
    LastActivity    time.Time              `json:"last_activity"`
    ClientInfo      *ClientInfo            `json:"client_info,omitempty"`
    ConnectionCount int                    `json:"connection_count"`
    ErrorCount      int                    `json:"error_count"`
    MessageCount    int64                  `json:"message_count"`
}

// 会话状态枚举
type SessionStatus string
const (
    SessionStatusInitializing SessionStatus = "initializing"
    SessionStatusConnecting   SessionStatus = "connecting"
    SessionStatusConnected    SessionStatus = "connected"
    SessionStatusDisconnected SessionStatus = "disconnected"
    SessionStatusError        SessionStatus = "error"
    SessionStatusSuspended    SessionStatus = "suspended"
)
```

### **2. 代理管理器 (Proxy Manager)**

```go
type ProxyManager struct {
    proxies         sync.Map                // proxyID -> *ProxyInfo
    groups          sync.Map                // groupID -> *ProxyGroup
    allocator       *ProxyAllocator         // 代理分配器
    healthChecker   *ProxyHealthChecker     // 健康检查器
    loadBalancer    *ProxyLoadBalancer      // 负载均衡器
    storage         ProxyStorage            // 代理存储
    config          *ProxyConfig            // 代理配置
    metrics         *ProxyMetrics           // 代理指标
}

type ProxyInfo struct {
    ID              string                 `json:"id"`
    GroupID         string                 `json:"group_id"`
    Type            ProxyType              `json:"type"`
    Host            string                 `json:"host"`
    Port            int                    `json:"port"`
    Username        string                 `json:"username,omitempty"`
    Password        string                 `json:"password,omitempty"`
    Region          string                 `json:"region"`
    Quality         ProxyQuality           `json:"quality"`
    Status          ProxyStatus            `json:"status"`
    MaxConnections  int                    `json:"max_connections"`
    CurrentLoad     int                    `json:"current_load"`
    ResponseTime    time.Duration          `json:"response_time"`
    SuccessRate     float64                `json:"success_rate"`
    LastCheck       time.Time              `json:"last_check"`
    CreatedAt       time.Time              `json:"created_at"`
    UpdatedAt       time.Time              `json:"updated_at"`
    Tags            []string               `json:"tags"`
    Metadata        map[string]interface{} `json:"metadata"`
}

type ProxyGroup struct {
    ID              string                 `json:"id"`
    Name            string                 `json:"name"`
    Region          string                 `json:"region"`
    Quality         ProxyQuality           `json:"quality"`
    ProxyIDs        []string               `json:"proxy_ids"`
    LoadBalanceType LoadBalanceType        `json:"load_balance_type"`
    HealthThreshold float64                `json:"health_threshold"`
    MaxProxiesPerSession int               `json:"max_proxies_per_session"`
    CreatedAt       time.Time              `json:"created_at"`
    UpdatedAt       time.Time              `json:"updated_at"`
}

// 代理类型和质量枚举
type ProxyType string
const (
    ProxyTypeHTTP   ProxyType = "http"
    ProxyTypeSOCKS5 ProxyType = "socks5"
)

type ProxyQuality string
const (
    ProxyQualityHigh   ProxyQuality = "high"
    ProxyQualityMedium ProxyQuality = "medium"
    ProxyQualityLow    ProxyQuality = "low"
)

type ProxyStatus string
const (
    ProxyStatusActive      ProxyStatus = "active"
    ProxyStatusInactive    ProxyStatus = "inactive"
    ProxyStatusMaintenance ProxyStatus = "maintenance"
    ProxyStatusError       ProxyStatus = "error"
)
```

### **3. 代理分配器 (Proxy Allocator)**

```go
type ProxyAllocator struct {
    manager         *ProxyManager
    allocationMap   sync.Map                // sessionID -> proxyID
    affinityMap     sync.Map                // sessionID -> preferredRegion
    strategy        AllocationStrategy
    mutex           sync.RWMutex
}

type AllocationStrategy string
const (
    StrategyRegionAffinity AllocationStrategy = "region_affinity"
    StrategyLoadBalance    AllocationStrategy = "load_balance"
    StrategyQualityFirst   AllocationStrategy = "quality_first"
    StrategySticky         AllocationStrategy = "sticky"
)

// 代理分配请求
type ProxyAllocationRequest struct {
    SessionID       string                 `json:"session_id"`
    TenantID        uint                   `json:"tenant_id"`
    PhoneNumber     string                 `json:"phone_number"`
    PreferredRegion string                 `json:"preferred_region,omitempty"`
    RequiredQuality ProxyQuality           `json:"required_quality,omitempty"`
    ExcludeProxies  []string               `json:"exclude_proxies,omitempty"`
    Sticky          bool                   `json:"sticky"`
    Metadata        map[string]interface{} `json:"metadata,omitempty"`
}

// 分配代理
func (pa *ProxyAllocator) AllocateProxy(req *ProxyAllocationRequest) (*ProxyInfo, error) {
    pa.mutex.Lock()
    defer pa.mutex.Unlock()
    
    // 1. 检查是否已有分配的代理
    if existingProxyID, exists := pa.allocationMap.Load(req.SessionID); exists {
        if proxy := pa.manager.GetProxy(existingProxyID.(string)); proxy != nil {
            if proxy.Status == ProxyStatusActive && pa.isProxyHealthy(proxy) {
                return proxy, nil // 返回现有代理
            }
        }
    }
    
    // 2. 根据策略选择新代理
    proxy, err := pa.selectProxyByStrategy(req)
    if err != nil {
        return nil, err
    }
    
    // 3. 分配代理
    pa.allocationMap.Store(req.SessionID, proxy.ID)
    if req.PreferredRegion != "" {
        pa.affinityMap.Store(req.SessionID, req.PreferredRegion)
    }
    
    // 4. 更新代理负载
    pa.manager.IncrementProxyLoad(proxy.ID)
    
    return proxy, nil
}
```

## 📊 **代理健康检查系统**

### **健康检查器设计**

```go
type ProxyHealthChecker struct {
    manager         *ProxyManager
    checkInterval   time.Duration
    timeout         time.Duration
    retryCount      int
    healthThreshold float64
    workers         int
    checkQueue      chan *ProxyInfo
    stopChan        chan struct{}
}

// 健康检查结果
type HealthCheckResult struct {
    ProxyID      string        `json:"proxy_id"`
    Success      bool          `json:"success"`
    ResponseTime time.Duration `json:"response_time"`
    Error        string        `json:"error,omitempty"`
    Timestamp    time.Time     `json:"timestamp"`
}

// 启动健康检查
func (phc *ProxyHealthChecker) Start() {
    // 启动工作协程
    for i := 0; i < phc.workers; i++ {
        go phc.healthCheckWorker()
    }
    
    // 启动定时检查
    ticker := time.NewTicker(phc.checkInterval)
    go func() {
        for {
            select {
            case <-ticker.C:
                phc.scheduleHealthChecks()
            case <-phc.stopChan:
                ticker.Stop()
                return
            }
        }
    }()
}

// 执行健康检查
func (phc *ProxyHealthChecker) checkProxyHealth(proxy *ProxyInfo) *HealthCheckResult {
    start := time.Now()
    
    // 创建代理客户端
    proxyURL := fmt.Sprintf("%s://%s:%d", proxy.Type, proxy.Host, proxy.Port)
    if proxy.Username != "" {
        proxyURL = fmt.Sprintf("%s://%s:%s@%s:%d", 
            proxy.Type, proxy.Username, proxy.Password, proxy.Host, proxy.Port)
    }
    
    // 测试连接
    client := &http.Client{
        Timeout: phc.timeout,
        Transport: &http.Transport{
            Proxy: http.ProxyURL(&url.URL{
                Scheme: string(proxy.Type),
                Host:   fmt.Sprintf("%s:%d", proxy.Host, proxy.Port),
                User:   url.UserPassword(proxy.Username, proxy.Password),
            }),
        },
    }
    
    // 发送测试请求
    resp, err := client.Get("https://httpbin.org/ip")
    responseTime := time.Since(start)
    
    result := &HealthCheckResult{
        ProxyID:      proxy.ID,
        ResponseTime: responseTime,
        Timestamp:    time.Now(),
    }
    
    if err != nil {
        result.Success = false
        result.Error = err.Error()
    } else {
        result.Success = resp.StatusCode == 200
        resp.Body.Close()
    }
    
    return result
}
```

## 🔄 **代理故障转移机制**

```go
type ProxyFailoverManager struct {
    allocator       *ProxyAllocator
    healthChecker   *ProxyHealthChecker
    sessionManager  *SessionManager
    failoverQueue   chan *FailoverRequest
    workers         int
}

type FailoverRequest struct {
    SessionID    string    `json:"session_id"`
    FailedProxy  string    `json:"failed_proxy"`
    Reason       string    `json:"reason"`
    Timestamp    time.Time `json:"timestamp"`
    Urgent       bool      `json:"urgent"`
}

// 处理代理故障转移
func (pfm *ProxyFailoverManager) HandleFailover(req *FailoverRequest) error {
    // 1. 获取会话信息
    session := pfm.sessionManager.GetSession(req.SessionID)
    if session == nil {
        return fmt.Errorf("session not found: %s", req.SessionID)
    }
    
    // 2. 标记失败的代理
    pfm.allocator.manager.MarkProxyFailed(req.FailedProxy, req.Reason)
    
    // 3. 分配新代理
    newProxy, err := pfm.allocator.AllocateProxy(&ProxyAllocationRequest{
        SessionID:       req.SessionID,
        TenantID:        session.TenantID,
        PhoneNumber:     session.PhoneNumber,
        PreferredRegion: session.ProxyRegion,
        ExcludeProxies:  []string{req.FailedProxy},
        Sticky:          true,
    })
    if err != nil {
        return fmt.Errorf("failed to allocate new proxy: %v", err)
    }
    
    // 4. 更新会话代理
    session.ProxyID = newProxy.ID
    session.ProxyRegion = newProxy.Region
    
    // 5. 重新连接会话
    if session.Status == SessionStatusConnected {
        go pfm.sessionManager.ReconnectSession(req.SessionID)
    }
    
    return nil
}
```

## 📁 **文件存储结构**

```
whatsapp-engine/
├── main.go                          # 主程序入口
├── config/
│   ├── config.go                    # 配置管理
│   └── proxy_config.go              # 代理配置
├── internal/
│   ├── session/
│   │   ├── manager.go               # 会话管理器
│   │   ├── storage.go               # 会话存储
│   │   └── events.go                # 会话事件
│   ├── proxy/
│   │   ├── manager.go               # 代理管理器
│   │   ├── allocator.go             # 代理分配器
│   │   ├── health_checker.go        # 健康检查器
│   │   ├── failover.go              # 故障转移
│   │   └── storage.go               # 代理存储
│   ├── message/
│   │   ├── manager.go               # 消息管理器
│   │   ├── sender.go                # 消息发送器
│   │   └── history.go               # 历史记录
│   ├── api/
│   │   ├── session_api.go           # 会话API
│   │   ├── proxy_api.go             # 代理API
│   │   ├── message_api.go           # 消息API
│   │   └── compatibility.go         # 兼容层
│   └── storage/
│       ├── database.go              # 数据库操作
│       ├── cache.go                 # 缓存操作
│       └── files.go                 # 文件操作
├── pkg/
│   ├── types/                       # 类型定义
│   ├── utils/                       # 工具函数
│   └── metrics/                     # 指标收集
└── docs/
    ├── api.md                       # API文档
    ├── deployment.md                # 部署文档
    └── troubleshooting.md           # 故障排除
```

## 🚀 **部署配置**

### **Docker 配置**

```yaml
# docker-compose.yml
version: '3.8'
services:
  whatsapp-engine:
    build: .
    ports:
      - "3000:3000"
    environment:
      - DB_PATH=/app/data/whatsapp.db
      - REDIS_URL=redis://redis:6379
      - SESSION_PATH=/app/sessions
      - PROXY_CHECK_INTERVAL=30s
      - MAX_SESSIONS=50000
    volumes:
      - ./data:/app/data
      - ./sessions:/app/sessions
      - ./config:/app/config
    depends_on:
      - redis
    restart: unless-stopped
    
  redis:
    image: redis:alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped

volumes:
  redis_data:
```

## 📈 **性能指标**

### **资源消耗估算**

| 账号数量 | 内存使用 | CPU使用 | 代理数量 | 推荐配置 |
|---------|---------|---------|---------|----------|
| 1,000 | 1.5GB | 15% | 50-100 | 4C8G |
| 5,000 | 4GB | 25% | 200-500 | 8C16G |
| 10,000 | 8GB | 35% | 500-1000 | 16C32G |
| 50,000 | 30GB | 50% | 2000-5000 | 32C64G |
| 100,000 | 60GB | 70% | 5000-10000 | 64C128G |

### **代理网络规模建议**

- **小规模 (1K账号)**: 50-100个代理，覆盖3-5个地区
- **中规模 (10K账号)**: 500-1000个代理，覆盖10-15个地区  
- **大规模 (100K账号)**: 5000-10000个代理，全球覆盖

## 🔧 **配置示例**

### **代理配置**

```yaml
proxy:
  health_check:
    interval: 30s
    timeout: 10s
    retry_count: 3
    workers: 10
  
  allocation:
    strategy: "region_affinity"
    sticky_sessions: true
    max_retries: 3
  
  failover:
    enabled: true
    workers: 5
    urgent_threshold: 10s
  
  groups:
    - id: "us-high"
      name: "US High Quality"
      region: "us"
      quality: "high"
      load_balance: "least_connections"
    
    - id: "eu-medium"
      name: "EU Medium Quality"
      region: "eu"
      quality: "medium"
      load_balance: "round_robin"
```

## 🔌 **API 接口设计**

### **代理管理 API**

```go
// 导入代理
func importProxies(c *gin.Context) {
    var req struct {
        Proxies []struct {
            Host     string `json:"host" binding:"required"`
            Port     int    `json:"port" binding:"required"`
            Type     string `json:"type" binding:"required"`
            Username string `json:"username"`
            Password string `json:"password"`
            Region   string `json:"region" binding:"required"`
            Quality  string `json:"quality" binding:"required"`
            Tags     []string `json:"tags"`
        } `json:"proxies" binding:"required"`
        GroupID string `json:"group_id"`
    }

    if err := c.ShouldBindJSON(&req); err != nil {
        errorResponse(c, 400, "Invalid request format")
        return
    }

    imported := 0
    failed := 0

    for _, proxyData := range req.Proxies {
        proxy := &ProxyInfo{
            ID:       generateProxyID(),
            GroupID:  req.GroupID,
            Host:     proxyData.Host,
            Port:     proxyData.Port,
            Type:     ProxyType(proxyData.Type),
            Username: proxyData.Username,
            Password: proxyData.Password,
            Region:   proxyData.Region,
            Quality:  ProxyQuality(proxyData.Quality),
            Status:   ProxyStatusActive,
            Tags:     proxyData.Tags,
            CreatedAt: time.Now(),
        }

        if err := proxyManager.AddProxy(proxy); err != nil {
            failed++
            continue
        }
        imported++
    }

    successResponse(c, gin.H{
        "imported": imported,
        "failed":   failed,
        "total":    len(req.Proxies),
    })
}

// 创建代理分组
func createProxyGroup(c *gin.Context) {
    var req struct {
        Name            string  `json:"name" binding:"required"`
        Region          string  `json:"region" binding:"required"`
        Quality         string  `json:"quality" binding:"required"`
        LoadBalanceType string  `json:"load_balance_type"`
        HealthThreshold float64 `json:"health_threshold"`
    }

    if err := c.ShouldBindJSON(&req); err != nil {
        errorResponse(c, 400, "Invalid request format")
        return
    }

    group := &ProxyGroup{
        ID:              generateGroupID(),
        Name:            req.Name,
        Region:          req.Region,
        Quality:         ProxyQuality(req.Quality),
        LoadBalanceType: LoadBalanceType(req.LoadBalanceType),
        HealthThreshold: req.HealthThreshold,
        CreatedAt:       time.Now(),
    }

    if err := proxyManager.CreateGroup(group); err != nil {
        errorResponse(c, 500, fmt.Sprintf("Failed to create group: %v", err))
        return
    }

    successResponse(c, group)
}

// 获取代理状态
func getProxyStatus(c *gin.Context) {
    groupID := c.Query("group_id")
    region := c.Query("region")
    quality := c.Query("quality")

    proxies := proxyManager.GetProxies(&ProxyFilter{
        GroupID: groupID,
        Region:  region,
        Quality: ProxyQuality(quality),
    })

    stats := calculateProxyStats(proxies)

    successResponse(c, gin.H{
        "proxies": proxies,
        "stats":   stats,
    })
}
```

### **会话管理 API**

```go
// 创建会话（兼容原 Node.js 接口）
func createWhatsAppSession(c *gin.Context) {
    var req NodeServiceRequest
    if err := c.ShouldBindJSON(&req); err != nil {
        errorResponse(c, 400, "Invalid request format")
        return
    }

    // 分配代理
    proxyReq := &ProxyAllocationRequest{
        SessionID:   req.SessionID,
        TenantID:    req.TenantID,
        PhoneNumber: req.PhoneNumber,
        Sticky:      true,
    }

    proxy, err := proxyAllocator.AllocateProxy(proxyReq)
    if err != nil {
        errorResponse(c, 500, fmt.Sprintf("Failed to allocate proxy: %v", err))
        return
    }

    // 创建会话
    session, err := sessionManager.CreateSession(&CreateSessionRequest{
        SessionID:   req.SessionID,
        TenantID:    req.TenantID,
        PhoneNumber: req.PhoneNumber,
        ProxyID:     proxy.ID,
        ProxyRegion: proxy.Region,
    })

    if err != nil {
        errorResponse(c, 500, fmt.Sprintf("Failed to create session: %v", err))
        return
    }

    // 返回兼容格式
    successResponse(c, NodeServiceResponse{
        Success: true,
        Message: "Session created successfully",
        Data: map[string]interface{}{
            "session_id":    session.ID,
            "status":        session.Status,
            "proxy_id":      session.ProxyID,
            "proxy_region":  session.ProxyRegion,
        },
    })
}

// 连接会话
func connectWhatsAppSession(c *gin.Context) {
    var req NodeServiceRequest
    if err := c.ShouldBindJSON(&req); err != nil {
        errorResponse(c, 400, "Invalid request format")
        return
    }

    // 获取会话
    session := sessionManager.GetSession(req.SessionID)
    if session == nil {
        errorResponse(c, 404, "Session not found")
        return
    }

    // 检查代理状态
    proxy := proxyManager.GetProxy(session.ProxyID)
    if proxy == nil || proxy.Status != ProxyStatusActive {
        // 重新分配代理
        newProxy, err := proxyAllocator.AllocateProxy(&ProxyAllocationRequest{
            SessionID:       req.SessionID,
            TenantID:        req.TenantID,
            PhoneNumber:     req.PhoneNumber,
            PreferredRegion: session.ProxyRegion,
            ExcludeProxies:  []string{session.ProxyID},
        })
        if err != nil {
            errorResponse(c, 500, fmt.Sprintf("Failed to reallocate proxy: %v", err))
            return
        }

        session.ProxyID = newProxy.ID
        session.ProxyRegion = newProxy.Region
    }

    // 连接会话
    result, err := sessionManager.ConnectSession(req.SessionID)
    if err != nil {
        errorResponse(c, 500, fmt.Sprintf("Failed to connect session: %v", err))
        return
    }

    successResponse(c, result)
}
```

## 🔍 **监控和指标系统**

### **代理监控指标**

```go
type ProxyMetrics struct {
    // 基础指标
    TotalProxies     int                    `json:"total_proxies"`
    ActiveProxies    int                    `json:"active_proxies"`
    FailedProxies    int                    `json:"failed_proxies"`

    // 地区分布
    RegionStats      map[string]int         `json:"region_stats"`

    // 质量分布
    QualityStats     map[string]int         `json:"quality_stats"`

    // 性能指标
    AvgResponseTime  time.Duration          `json:"avg_response_time"`
    SuccessRate      float64                `json:"success_rate"`

    // 负载指标
    TotalConnections int                    `json:"total_connections"`
    AvgLoadPerProxy  float64                `json:"avg_load_per_proxy"`

    // 故障转移指标
    FailoverCount    int                    `json:"failover_count"`
    FailoverRate     float64                `json:"failover_rate"`
}

// 会话监控指标
type SessionMetrics struct {
    // 会话统计
    TotalSessions    int                    `json:"total_sessions"`
    OnlineSessions   int                    `json:"online_sessions"`
    ActiveSessions   int                    `json:"active_sessions"`

    // 状态分布
    StatusStats      map[string]int         `json:"status_stats"`

    // 地区分布
    RegionStats      map[string]int         `json:"region_stats"`

    // 性能指标
    AvgConnectTime   time.Duration          `json:"avg_connect_time"`
    MessageRate      float64                `json:"message_rate"`
    ErrorRate        float64                `json:"error_rate"`

    // 资源使用
    MemoryUsage      int64                  `json:"memory_usage"`
    CPUUsage         float64                `json:"cpu_usage"`
}
```

### **告警系统**

```go
type AlertManager struct {
    rules       []AlertRule
    channels    []AlertChannel
    history     []Alert
    mutex       sync.RWMutex
}

type AlertRule struct {
    ID          string                 `json:"id"`
    Name        string                 `json:"name"`
    Condition   string                 `json:"condition"`
    Threshold   float64                `json:"threshold"`
    Duration    time.Duration          `json:"duration"`
    Severity    AlertSeverity          `json:"severity"`
    Enabled     bool                   `json:"enabled"`
}

type Alert struct {
    ID          string                 `json:"id"`
    RuleID      string                 `json:"rule_id"`
    Title       string                 `json:"title"`
    Message     string                 `json:"message"`
    Severity    AlertSeverity          `json:"severity"`
    Status      AlertStatus            `json:"status"`
    CreatedAt   time.Time              `json:"created_at"`
    ResolvedAt  *time.Time             `json:"resolved_at,omitempty"`
    Metadata    map[string]interface{} `json:"metadata"`
}

// 告警规则示例
var defaultAlertRules = []AlertRule{
    {
        ID:        "proxy_failure_rate_high",
        Name:      "代理失败率过高",
        Condition: "proxy_failure_rate > threshold",
        Threshold: 0.1, // 10%
        Duration:  time.Minute * 5,
        Severity:  AlertSeverityWarning,
        Enabled:   true,
    },
    {
        ID:        "session_error_rate_high",
        Name:      "会话错误率过高",
        Condition: "session_error_rate > threshold",
        Threshold: 0.05, // 5%
        Duration:  time.Minute * 3,
        Severity:  AlertSeverityCritical,
        Enabled:   true,
    },
    {
        ID:        "proxy_response_time_high",
        Name:      "代理响应时间过长",
        Condition: "avg_proxy_response_time > threshold",
        Threshold: 5000, // 5秒
        Duration:  time.Minute * 2,
        Severity:  AlertSeverityWarning,
        Enabled:   true,
    },
}
```

## 🛠️ **故障排除和维护**

### **常见问题处理**

```go
type TroubleshootingManager struct {
    sessionManager *SessionManager
    proxyManager   *ProxyManager
    diagnostics    *DiagnosticsCollector
}

// 诊断会话问题
func (tm *TroubleshootingManager) DiagnoseSession(sessionID string) *DiagnosisReport {
    report := &DiagnosisReport{
        SessionID: sessionID,
        Timestamp: time.Now(),
        Issues:    []Issue{},
        Suggestions: []string{},
    }

    session := tm.sessionManager.GetSession(sessionID)
    if session == nil {
        report.Issues = append(report.Issues, Issue{
            Type:        "session_not_found",
            Severity:    "critical",
            Description: "会话不存在",
        })
        return report
    }

    // 检查代理状态
    proxy := tm.proxyManager.GetProxy(session.ProxyID)
    if proxy == nil {
        report.Issues = append(report.Issues, Issue{
            Type:        "proxy_not_found",
            Severity:    "critical",
            Description: "代理不存在",
        })
        report.Suggestions = append(report.Suggestions, "重新分配代理")
    } else if proxy.Status != ProxyStatusActive {
        report.Issues = append(report.Issues, Issue{
            Type:        "proxy_inactive",
            Severity:    "high",
            Description: fmt.Sprintf("代理状态异常: %s", proxy.Status),
        })
        report.Suggestions = append(report.Suggestions, "检查代理连接或更换代理")
    }

    // 检查连接状态
    if session.Status == SessionStatusError {
        report.Issues = append(report.Issues, Issue{
            Type:        "session_error",
            Severity:    "high",
            Description: "会话处于错误状态",
        })
        report.Suggestions = append(report.Suggestions, "重启会话或检查网络连接")
    }

    return report
}

// 自动修复
func (tm *TroubleshootingManager) AutoFix(sessionID string) *FixResult {
    result := &FixResult{
        SessionID: sessionID,
        Actions:   []string{},
        Success:   false,
    }

    diagnosis := tm.DiagnoseSession(sessionID)

    for _, issue := range diagnosis.Issues {
        switch issue.Type {
        case "proxy_not_found", "proxy_inactive":
            // 重新分配代理
            if err := tm.reallocateProxy(sessionID); err == nil {
                result.Actions = append(result.Actions, "重新分配代理")
            }

        case "session_error":
            // 重启会话
            if err := tm.sessionManager.RestartSession(sessionID); err == nil {
                result.Actions = append(result.Actions, "重启会话")
            }
        }
    }

    // 检查修复结果
    newDiagnosis := tm.DiagnoseSession(sessionID)
    result.Success = len(newDiagnosis.Issues) == 0

    return result
}
```

## 💬 **消息管理系统**

### **消息管理器设计**

```go
type MessageManager struct {
    sessionManager  *SessionManager
    proxyManager    *ProxyManager
    storage         MessageStorage
    queue           *MessageQueue
    rateLimiter     *RateLimiter
    metrics         *MessageMetrics
    config          *MessageConfig
}

type MessageRequest struct {
    SessionID       string                 `json:"session_id"`
    To              string                 `json:"to"`
    MessageType     MessageType            `json:"message_type"`
    Content         string                 `json:"content,omitempty"`
    MediaData       []byte                 `json:"media_data,omitempty"`
    MediaType       string                 `json:"media_type,omitempty"`
    Filename        string                 `json:"filename,omitempty"`
    Caption         string                 `json:"caption,omitempty"`
    Priority        MessagePriority        `json:"priority"`
    ScheduledAt     *time.Time             `json:"scheduled_at,omitempty"`
    RetryCount      int                    `json:"retry_count"`
    Metadata        map[string]interface{} `json:"metadata,omitempty"`
}

type MessageType string
const (
    MessageTypeText     MessageType = "text"
    MessageTypeImage    MessageType = "image"
    MessageTypeDocument MessageType = "document"
    MessageTypeAudio    MessageType = "audio"
    MessageTypeVideo    MessageType = "video"
    MessageTypeLocation MessageType = "location"
    MessageTypeContact  MessageType = "contact"
)

type MessagePriority int
const (
    PriorityLow    MessagePriority = 1
    PriorityNormal MessagePriority = 5
    PriorityHigh   MessagePriority = 8
    PriorityUrgent MessagePriority = 10
)

// 发送消息
func (mm *MessageManager) SendMessage(req *MessageRequest) (*MessageResult, error) {
    // 1. 验证会话状态
    session := mm.sessionManager.GetSession(req.SessionID)
    if session == nil {
        return nil, fmt.Errorf("session not found: %s", req.SessionID)
    }

    if session.Status != SessionStatusConnected {
        return nil, fmt.Errorf("session not connected: %s", session.Status)
    }

    // 2. 检查速率限制
    if !mm.rateLimiter.Allow(req.SessionID) {
        return nil, fmt.Errorf("rate limit exceeded for session: %s", req.SessionID)
    }

    // 3. 构造 WhatsApp 消息
    jid, err := types.ParseJID(req.To)
    if err != nil {
        return nil, fmt.Errorf("invalid JID: %v", err)
    }

    var waMsg *waProto.Message
    switch req.MessageType {
    case MessageTypeText:
        waMsg = &waProto.Message{
            Conversation: proto.String(req.Content),
        }
    case MessageTypeImage:
        waMsg, err = mm.buildImageMessage(req)
    case MessageTypeDocument:
        waMsg, err = mm.buildDocumentMessage(req)
    case MessageTypeAudio:
        waMsg, err = mm.buildAudioMessage(req)
    case MessageTypeVideo:
        waMsg, err = mm.buildVideoMessage(req)
    default:
        return nil, fmt.Errorf("unsupported message type: %s", req.MessageType)
    }

    if err != nil {
        return nil, fmt.Errorf("failed to build message: %v", err)
    }

    // 4. 发送消息
    resp, err := session.Client.SendMessage(context.Background(), jid, waMsg)
    if err != nil {
        // 检查是否是代理问题
        if mm.isProxyError(err) {
            // 触发代理故障转移
            go mm.handleProxyFailure(req.SessionID, err)
        }
        return nil, fmt.Errorf("failed to send message: %v", err)
    }

    // 5. 记录消息历史
    messageRecord := &MessageRecord{
        ID:          resp.ID,
        SessionID:   req.SessionID,
        To:          req.To,
        MessageType: req.MessageType,
        Content:     req.Content,
        Status:      MessageStatusSent,
        SentAt:      time.Now(),
        Timestamp:   time.Unix(int64(resp.Timestamp), 0),
    }

    if err := mm.storage.SaveMessage(messageRecord); err != nil {
        log.Printf("Failed to save message record: %v", err)
    }

    // 6. 更新指标
    mm.metrics.RecordMessageSent(req.SessionID, req.MessageType, true)

    return &MessageResult{
        MessageID: resp.ID,
        Status:    MessageStatusSent,
        SentAt:    time.Now(),
        Timestamp: time.Unix(int64(resp.Timestamp), 0),
    }, nil
}

// 批量发送消息
func (mm *MessageManager) SendBulkMessages(requests []*MessageRequest) ([]*MessageResult, error) {
    results := make([]*MessageResult, len(requests))

    // 按会话分组
    sessionGroups := make(map[string][]*MessageRequest)
    for i, req := range requests {
        sessionGroups[req.SessionID] = append(sessionGroups[req.SessionID], req)
        results[i] = &MessageResult{Status: MessageStatusPending}
    }

    // 并发处理每个会话的消息
    var wg sync.WaitGroup
    semaphore := make(chan struct{}, 10) // 限制并发数

    for sessionID, sessionRequests := range sessionGroups {
        wg.Add(1)
        go func(sid string, reqs []*MessageRequest) {
            defer wg.Done()
            semaphore <- struct{}{}
            defer func() { <-semaphore }()

            mm.processBulkMessagesForSession(sid, reqs, results)
        }(sessionID, sessionRequests)
    }

    wg.Wait()
    return results, nil
}
```

### **消息队列系统**

```go
type MessageQueue struct {
    queues      map[string]*SessionQueue   // sessionID -> queue
    workers     int
    workerPool  chan chan *MessageRequest
    quit        chan bool
    mutex       sync.RWMutex
}

type SessionQueue struct {
    sessionID   string
    messages    chan *MessageRequest
    priority    *PriorityQueue
    processing  bool
    lastActive  time.Time
}

// 启动消息队列
func (mq *MessageQueue) Start() {
    mq.workerPool = make(chan chan *MessageRequest, mq.workers)

    // 启动工作协程
    for i := 0; i < mq.workers; i++ {
        worker := NewMessageWorker(i, mq.workerPool, mq.quit)
        worker.Start()
    }

    // 启动调度协程
    go mq.dispatcher()
}

// 消息调度器
func (mq *MessageQueue) dispatcher() {
    for {
        select {
        case <-mq.quit:
            return
        default:
            mq.processQueues()
            time.Sleep(time.Millisecond * 100)
        }
    }
}

// 处理队列
func (mq *MessageQueue) processQueues() {
    mq.mutex.RLock()
    defer mq.mutex.RUnlock()

    for sessionID, queue := range mq.queues {
        if !queue.processing && queue.HasMessages() {
            select {
            case worker := <-mq.workerPool:
                queue.processing = true
                go func(w chan *MessageRequest, q *SessionQueue) {
                    defer func() { q.processing = false }()

                    if msg := q.GetNextMessage(); msg != nil {
                        w <- msg
                    }
                }(worker, queue)
            default:
                // 没有可用的工作协程
            }
        }
    }
}
```

## 📚 **历史记录管理系统**

### **历史记录管理器**

```go
type HistoryManager struct {
    sessionManager  *SessionManager
    storage         HistoryStorage
    cache           HistoryCache
    syncManager     *HistorySyncManager
    config          *HistoryConfig
}

type HistoryConfig struct {
    AutoSyncOnConnect    bool          `config:"true"`
    SyncInterval         time.Duration `config:"24h"`
    MaxSyncMessages      int           `config:"10000"`
    RetentionDays        int           `config:"90"`
    CacheSize            int           `config:"1000"`
    CompressionEnabled   bool          `config:"true"`
    EncryptionEnabled    bool          `config:"false"`
}

// 获取聊天历史
func (hm *HistoryManager) GetChatHistory(sessionID string, chatJID types.JID, options *HistoryQueryOptions) (*HistoryResult, error) {
    // 1. 检查缓存
    if cached := hm.cache.Get(sessionID, chatJID, options); cached != nil {
        return cached, nil
    }

    // 2. 从本地存储查询
    messages, err := hm.storage.QueryMessages(sessionID, chatJID, options)
    if err != nil {
        return nil, fmt.Errorf("failed to query local messages: %v", err)
    }

    // 3. 如果本地消息不足，尝试同步
    if len(messages) < options.Limit && hm.shouldSync(sessionID, chatJID) {
        if err := hm.syncManager.SyncChatHistory(sessionID, chatJID); err != nil {
            log.Printf("Failed to sync chat history: %v", err)
        } else {
            // 重新查询
            messages, _ = hm.storage.QueryMessages(sessionID, chatJID, options)
        }
    }

    result := &HistoryResult{
        SessionID: sessionID,
        ChatJID:   chatJID,
        Messages:  messages,
        Total:     len(messages),
        HasMore:   len(messages) == options.Limit,
        SyncTime:  time.Now(),
    }

    // 4. 缓存结果
    hm.cache.Set(sessionID, chatJID, result)

    return result, nil
}

// 搜索消息
func (hm *HistoryManager) SearchMessages(sessionID string, query *MessageSearchQuery) (*SearchResult, error) {
    // 构建搜索条件
    searchOptions := &HistoryQueryOptions{
        StartTime:    query.StartTime,
        EndTime:      query.EndTime,
        MessageTypes: query.MessageTypes,
        FromJID:      query.FromJID,
        Keyword:      query.Keyword,
        Limit:        query.Limit,
        Offset:       query.Offset,
    }

    // 执行搜索
    messages, err := hm.storage.SearchMessages(sessionID, searchOptions)
    if err != nil {
        return nil, fmt.Errorf("failed to search messages: %v", err)
    }

    // 统计结果
    total, err := hm.storage.CountMessages(sessionID, searchOptions)
    if err != nil {
        total = len(messages)
    }

    return &SearchResult{
        Messages:    messages,
        Total:       total,
        Query:       query,
        SearchTime:  time.Now(),
    }, nil
}

// 导出聊天记录
func (hm *HistoryManager) ExportChatHistory(sessionID string, chatJID types.JID, format ExportFormat) (*ExportResult, error) {
    // 获取所有消息
    options := &HistoryQueryOptions{
        Limit: -1, // 获取所有消息
    }

    messages, err := hm.storage.QueryMessages(sessionID, chatJID, options)
    if err != nil {
        return nil, fmt.Errorf("failed to query messages for export: %v", err)
    }

    // 根据格式导出
    var exportData []byte
    var filename string

    switch format {
    case ExportFormatJSON:
        exportData, err = json.MarshalIndent(messages, "", "  ")
        filename = fmt.Sprintf("chat_%s_%s.json", sessionID, chatJID.User)
    case ExportFormatCSV:
        exportData, err = hm.exportToCSV(messages)
        filename = fmt.Sprintf("chat_%s_%s.csv", sessionID, chatJID.User)
    case ExportFormatTXT:
        exportData, err = hm.exportToText(messages)
        filename = fmt.Sprintf("chat_%s_%s.txt", sessionID, chatJID.User)
    default:
        return nil, fmt.Errorf("unsupported export format: %s", format)
    }

    if err != nil {
        return nil, fmt.Errorf("failed to export data: %v", err)
    }

    return &ExportResult{
        Filename:    filename,
        Data:        exportData,
        Format:      format,
        MessageCount: len(messages),
        ExportTime:  time.Now(),
    }, nil
}
```

### **历史记录同步管理器**

```go
type HistorySyncManager struct {
    sessionManager  *SessionManager
    storage         HistoryStorage
    syncStatus      sync.Map                // sessionID -> *SyncStatus
    syncQueue       chan *SyncRequest
    workers         int
    config          *HistoryConfig
}

type SyncRequest struct {
    SessionID   string    `json:"session_id"`
    ChatJID     types.JID `json:"chat_jid"`
    MessageCount int      `json:"message_count"`
    Priority    int       `json:"priority"`
    CreatedAt   time.Time `json:"created_at"`
}

type SyncStatus struct {
    SessionID       string    `json:"session_id"`
    ChatJID         types.JID `json:"chat_jid"`
    Status          string    `json:"status"`
    Progress        float64   `json:"progress"`
    MessagesSynced  int       `json:"messages_synced"`
    TotalMessages   int       `json:"total_messages"`
    StartTime       time.Time `json:"start_time"`
    LastUpdateTime  time.Time `json:"last_update_time"`
    Error           string    `json:"error,omitempty"`
}

// 同步聊天历史
func (hsm *HistorySyncManager) SyncChatHistory(sessionID string, chatJID types.JID) error {
    // 检查是否已在同步
    if status, exists := hsm.syncStatus.Load(sessionID + chatJID.String()); exists {
        syncStatus := status.(*SyncStatus)
        if syncStatus.Status == "syncing" {
            return fmt.Errorf("sync already in progress")
        }
    }

    // 创建同步请求
    syncReq := &SyncRequest{
        SessionID:    sessionID,
        ChatJID:      chatJID,
        MessageCount: hsm.config.MaxSyncMessages,
        Priority:     5,
        CreatedAt:    time.Now(),
    }

    // 添加到同步队列
    select {
    case hsm.syncQueue <- syncReq:
        return nil
    default:
        return fmt.Errorf("sync queue is full")
    }
}

// 执行历史同步
func (hsm *HistorySyncManager) performSync(req *SyncRequest) error {
    key := req.SessionID + req.ChatJID.String()

    // 更新同步状态
    status := &SyncStatus{
        SessionID:      req.SessionID,
        ChatJID:        req.ChatJID,
        Status:         "syncing",
        Progress:       0,
        StartTime:      time.Now(),
        LastUpdateTime: time.Now(),
    }
    hsm.syncStatus.Store(key, status)

    defer func() {
        status.Status = "completed"
        status.Progress = 100
        status.LastUpdateTime = time.Now()
        hsm.syncStatus.Store(key, status)
    }()

    // 获取会话
    session := hsm.sessionManager.GetSession(req.SessionID)
    if session == nil {
        status.Error = "session not found"
        status.Status = "failed"
        return fmt.Errorf("session not found: %s", req.SessionID)
    }

    // 如果会话未连接，临时连接
    wasConnected := session.Status == SessionStatusConnected
    if !wasConnected {
        if err := hsm.sessionManager.ConnectSession(req.SessionID); err != nil {
            status.Error = fmt.Sprintf("failed to connect session: %v", err)
            status.Status = "failed"
            return err
        }
        defer func() {
            if !wasConnected {
                hsm.sessionManager.DisconnectSession(req.SessionID)
            }
        }()
    }

    // 请求历史记录同步
    // 这里使用 whatsmeow 的历史同步功能
    // 具体实现依赖于 whatsmeow 的 API

    return nil
}
```

## 🔄 **兼容性接口层**

### **Node.js 服务兼容接口**

```go
// 兼容层 - 保持与原 Node.js 服务完全兼容
type CompatibilityLayer struct {
    sessionManager  *SessionManager
    messageManager  *MessageManager
    historyManager  *HistoryManager
    proxyManager    *ProxyManager
}

// 处理原 Node.js 的通用 Action 接口
func (cl *CompatibilityLayer) HandleNodeAction(req *NodeServiceRequest) (*NodeServiceResponse, error) {
    switch req.Action {
    case "create":
        return cl.handleCreateAction(req)
    case "connect":
        return cl.handleConnectAction(req)
    case "disconnect":
        return cl.handleDisconnectAction(req)
    case "reconnect":
        return cl.handleReconnectAction(req)
    case "send_message":
        return cl.handleSendMessageAction(req)
    case "get_info":
        return cl.handleGetInfoAction(req)
    default:
        return &NodeServiceResponse{
            Success: false,
            Error:   fmt.Sprintf("unsupported action: %s", req.Action),
        }, nil
    }
}

// 创建会话 - 兼容原接口
func (cl *CompatibilityLayer) handleCreateAction(req *NodeServiceRequest) (*NodeServiceResponse, error) {
    // 分配代理
    proxy, err := cl.proxyManager.AllocateProxy(&ProxyAllocationRequest{
        SessionID:   req.SessionID,
        TenantID:    req.TenantID,
        PhoneNumber: req.PhoneNumber,
        Sticky:      true,
    })
    if err != nil {
        return &NodeServiceResponse{
            Success: false,
            Error:   fmt.Sprintf("failed to allocate proxy: %v", err),
        }, nil
    }

    // 创建会话
    session, err := cl.sessionManager.CreateSession(&CreateSessionRequest{
        SessionID:   req.SessionID,
        TenantID:    req.TenantID,
        PhoneNumber: req.PhoneNumber,
        ProxyID:     proxy.ID,
        ProxyRegion: proxy.Region,
        Config:      extractSessionConfig(req.Data),
    })

    if err != nil {
        return &NodeServiceResponse{
            Success: false,
            Error:   fmt.Sprintf("failed to create session: %v", err),
        }, nil
    }

    return &NodeServiceResponse{
        Success: true,
        Message: "Session created successfully",
        Data: map[string]interface{}{
            "session_id":    session.ID,
            "status":        session.Status,
            "proxy_id":      session.ProxyID,
            "proxy_region":  session.ProxyRegion,
            "created_at":    session.CreatedAt,
        },
    }, nil
}

// 发送消息 - 兼容原接口
func (cl *CompatibilityLayer) handleSendMessageAction(req *NodeServiceRequest) (*NodeServiceResponse, error) {
    // 解析消息数据
    messageData, ok := req.Data["message"].(string)
    if !ok {
        return &NodeServiceResponse{
            Success: false,
            Error:   "missing or invalid message content",
        }, nil
    }

    to, ok := req.Data["to"].(string)
    if !ok {
        return &NodeServiceResponse{
            Success: false,
            Error:   "missing or invalid 'to' parameter",
        }, nil
    }

    // 构造消息请求
    msgReq := &MessageRequest{
        SessionID:   req.SessionID,
        To:          to,
        MessageType: MessageTypeText,
        Content:     messageData,
        Priority:    PriorityNormal,
    }

    // 检查是否有媒体数据
    if mediaType, exists := req.Data["media_type"].(string); exists {
        msgReq.MessageType = MessageType(mediaType)
        if mediaData, exists := req.Data["media_data"].([]byte); exists {
            msgReq.MediaData = mediaData
        }
        if filename, exists := req.Data["filename"].(string); exists {
            msgReq.Filename = filename
        }
        if caption, exists := req.Data["caption"].(string); exists {
            msgReq.Caption = caption
        }
    }

    // 发送消息
    result, err := cl.messageManager.SendMessage(msgReq)
    if err != nil {
        return &NodeServiceResponse{
            Success: false,
            Error:   fmt.Sprintf("failed to send message: %v", err),
        }, nil
    }

    return &NodeServiceResponse{
        Success: true,
        Message: "Message sent successfully",
        Data: map[string]interface{}{
            "message_id": result.MessageID,
            "status":     result.Status,
            "sent_at":    result.SentAt,
            "timestamp":  result.Timestamp,
        },
    }, nil
}
```

### **REST API 路由定义**

```go
// 设置所有 API 路由
func setupWhatsAppEngineRoutes(r *gin.Engine, engine *WhatsAppEngine) {
    api := r.Group("/api")

    // 兼容原 Node.js 服务的接口
    api.POST("/whatsapp/action", handleWhatsAppAction)
    api.POST("/whatsapp/session/:sessionId/restore", handleSessionRestore)
    api.POST("/whatsapp/send-message", handleSendMessage)
    api.GET("/whatsapp/sessions", handleGetSessions)
    api.POST("/whatsapp/cleanup-all", handleCleanupAll)
    api.GET("/health", handleHealthCheck)

    // 新增的代理管理接口
    proxy := api.Group("/proxy")
    {
        proxy.POST("/import", importProxies)
        proxy.POST("/groups", createProxyGroup)
        proxy.GET("/status", getProxyStatus)
        proxy.GET("/groups", getProxyGroups)
        proxy.PUT("/groups/:groupId", updateProxyGroup)
        proxy.DELETE("/groups/:groupId", deleteProxyGroup)
        proxy.GET("/health", getProxyHealth)
        proxy.POST("/test", testProxy)
    }

    // 会话管理接口
    session := api.Group("/session")
    {
        session.GET("/:sessionId", getSessionInfo)
        session.POST("/:sessionId/connect", connectSession)
        session.POST("/:sessionId/disconnect", disconnectSession)
        session.POST("/:sessionId/restart", restartSession)
        session.GET("/:sessionId/status", getSessionStatus)
        session.GET("/:sessionId/metrics", getSessionMetrics)
    }

    // 消息管理接口
    message := api.Group("/message")
    {
        message.POST("/send", sendMessage)
        message.POST("/bulk", sendBulkMessages)
        message.GET("/:sessionId/history", getMessageHistory)
        message.GET("/:sessionId/search", searchMessages)
        message.POST("/:sessionId/export", exportChatHistory)
    }

    // 监控和管理接口
    monitor := api.Group("/monitor")
    {
        monitor.GET("/metrics", getSystemMetrics)
        monitor.GET("/alerts", getAlerts)
        monitor.POST("/alerts/:alertId/resolve", resolveAlert)
        monitor.GET("/diagnostics/:sessionId", diagnoseSession)
        monitor.POST("/autofix/:sessionId", autoFixSession)
    }
}
```

## 📦 **完整部署指南**

### **环境要求**

```yaml
# 系统要求
system_requirements:
  os: "Linux/macOS/Windows"
  go_version: "1.21+"
  memory: "4GB+ (推荐 8GB+)"
  cpu: "2+ cores (推荐 4+ cores)"
  storage: "50GB+ SSD"
  network: "稳定的互联网连接"

# 依赖服务
dependencies:
  redis: "6.0+"
  sqlite: "3.35+"
  optional:
    postgresql: "13+"
    mysql: "8.0+"
```

### **Docker 部署**

```dockerfile
# Dockerfile
FROM golang:1.21-alpine AS builder

WORKDIR /app
COPY go.mod go.sum ./
RUN go mod download

COPY . .
RUN CGO_ENABLED=1 GOOS=linux go build -a -installsuffix cgo -o whatsapp-engine .

FROM alpine:latest
RUN apk --no-cache add ca-certificates tzdata
WORKDIR /root/

COPY --from=builder /app/whatsapp-engine .
COPY --from=builder /app/config ./config

EXPOSE 3000
CMD ["./whatsapp-engine"]
```

```yaml
# docker-compose.yml - 生产环境
version: '3.8'
services:
  whatsapp-engine:
    build: .
    ports:
      - "3000:3000"
    environment:
      - ENV=production
      - DB_TYPE=postgres
      - DB_URL=**********************************/whatsapp
      - REDIS_URL=redis://redis:6379
      - SESSION_PATH=/app/sessions
      - PROXY_CHECK_INTERVAL=30s
      - MAX_SESSIONS=50000
      - LOG_LEVEL=info
    volumes:
      - ./sessions:/app/sessions
      - ./logs:/app/logs
      - ./config:/app/config
    depends_on:
      - postgres
      - redis
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 8G
          cpus: '4.0'
        reservations:
          memory: 4G
          cpus: '2.0'

  postgres:
    image: postgres:13
    environment:
      - POSTGRES_DB=whatsapp
      - POSTGRES_USER=user
      - POSTGRES_PASSWORD=pass
    volumes:
      - postgres_data:/var/lib/postgresql/data
    restart: unless-stopped

  redis:
    image: redis:alpine
    command: redis-server --maxmemory 2gb --maxmemory-policy allkeys-lru
    volumes:
      - redis_data:/data
    restart: unless-stopped

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - whatsapp-engine
    restart: unless-stopped

volumes:
  postgres_data:
  redis_data:
```

### **配置文件示例**

```yaml
# config/config.yaml
server:
  port: 3000
  host: "0.0.0.0"
  read_timeout: 30s
  write_timeout: 30s
  idle_timeout: 120s

database:
  type: "postgres"  # sqlite, postgres, mysql
  url: "postgres://user:pass@localhost:5432/whatsapp"
  max_open_conns: 100
  max_idle_conns: 10
  conn_max_lifetime: 1h

redis:
  url: "redis://localhost:6379"
  pool_size: 100
  min_idle_conns: 10
  dial_timeout: 5s
  read_timeout: 3s
  write_timeout: 3s

session:
  max_sessions: 50000
  session_timeout: 24h
  cleanup_interval: 1h
  storage_path: "./sessions"
  auto_restore: true

proxy:
  health_check:
    interval: 30s
    timeout: 10s
    retry_count: 3
    workers: 10
    failure_threshold: 3

  allocation:
    strategy: "region_affinity"  # region_affinity, load_balance, quality_first
    sticky_sessions: true
    max_retries: 3
    retry_delay: 5s

  failover:
    enabled: true
    workers: 5
    urgent_threshold: 10s
    max_failover_attempts: 3

message:
  rate_limit:
    enabled: true
    requests_per_minute: 60
    burst_size: 10

  queue:
    workers: 20
    buffer_size: 10000
    batch_size: 100
    batch_timeout: 5s

  retry:
    max_attempts: 3
    initial_delay: 1s
    max_delay: 30s
    backoff_multiplier: 2

history:
  auto_sync_on_connect: true
  sync_interval: 24h
  max_sync_messages: 10000
  retention_days: 90
  cache_size: 1000
  compression_enabled: true

monitoring:
  metrics_enabled: true
  metrics_interval: 30s
  alerts_enabled: true
  log_level: "info"  # debug, info, warn, error

logging:
  level: "info"
  format: "json"  # json, text
  output: "stdout"  # stdout, file
  file_path: "./logs/whatsapp-engine.log"
  max_size: 100  # MB
  max_backups: 10
  max_age: 30  # days
```

### **启动脚本**

```bash
#!/bin/bash
# start.sh

set -e

echo "Starting WhatsApp Engine..."

# 检查环境变量
if [ -z "$DB_URL" ]; then
    echo "Error: DB_URL environment variable is required"
    exit 1
fi

if [ -z "$REDIS_URL" ]; then
    echo "Error: REDIS_URL environment variable is required"
    exit 1
fi

# 创建必要的目录
mkdir -p ./sessions
mkdir -p ./logs
mkdir -p ./data

# 数据库迁移
echo "Running database migrations..."
./whatsapp-engine migrate

# 启动服务
echo "Starting WhatsApp Engine server..."
exec ./whatsapp-engine server
```

## 🎯 **总结**

这个 WhatsApp 引擎设计提供了完整的企业级功能：

### **核心特性**
1. **智能代理分配** - 基于地区亲和性和负载均衡
2. **实时健康检查** - 持续监控代理可用性
3. **自动故障转移** - 代理失效时立即切换
4. **灵活的分组管理** - 支持按地区、质量分组
5. **完整的监控告警** - 实时监控和问题预警
6. **自动故障修复** - 智能诊断和自动修复
7. **高效消息管理** - 支持各种消息类型和批量发送
8. **智能历史记录** - 按需同步和快速查询
9. **强大的搜索功能** - 支持多条件消息搜索
10. **灵活的导出功能** - 支持多种格式导出

### **技术优势**
- **高性能**: 基于 Go 和 whatsmeow，资源消耗极低
- **高可用**: 自动故障转移和健康检查
- **易扩展**: 单体架构，支持垂直扩展
- **易部署**: Docker 容器化，一键部署
- **易维护**: 完整的监控和诊断工具

### **适用场景**
- **客户服务**: 大规模客服系统
- **营销推广**: WhatsApp 营销活动
- **企业通信**: 内部和外部通信
- **SaaS 平台**: 多租户 WhatsApp 服务

这个设计可以支持从小规模（1000 账号）到超大规模（100,000+ 账号）的部署需求。
