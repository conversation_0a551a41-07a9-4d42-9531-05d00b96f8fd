before:
  hooks:
    - make vendor
builds:
- main: ./cmd/main/main.go
  env:
    - CGO_ENABLED=0
  ldflags:
    - -s
    - -w
  goos:
    - darwin
    - linux
    - windows
  goarch:
    - 386
    - amd64
    - arm64
archives:
- replacements:
    darwin: macos
    linux: linux
    windows: windows
    386: 32-bit
    amd64: 64-bit
    arm64: arm-64-bit
  format: zip
  files:
    - LICENSE
    - README.md
    - .env.example
    - dbs/.gitkeep
checksum:
  name_template: 'checksums.txt'
snapshot:
  name_template: "{{ .Version }}_{{ .ShortCommit }}"
changelog:
  filters:
    exclude:
    - '^docs:'
    - '^test:'
